
from dataverse_helper.dv_common import read_fields_from_dataverse
from datetime import datetime, timedelta
from dataverse_helper.token_manager import get_token_for_env, get_dataverse_credentials_for_env, Environment, get_enum_for_env_num
import requests
from common.CustomExceptions import DataverseError
from common.appLogger import getGlobalAppLogger
from common.secrets_env import load_secrets_env_variables

#For backward compatibility only. Should be removed
def readContactInfo(email, sandbox=0, normal=False):
    enum_env = get_enum_for_env_num(sandbox)
    return readContactInfoEnum(email, enum_env, normal)

def readContactInfoEnum(email, enum_env, normal=False):
    token = get_token_for_env(enum_env)
    credentials = get_dataverse_credentials_for_env(enum_env)

    # Today's date
    today_date = datetime.now()

    # Date 12 months ago
    last_12_months_date = today_date - timedelta(days=365)

    # Format dates as 'YYYY-MM-DD'
    today_str = today_date.strftime('%Y-%m-%d')
    last_12_months_str = last_12_months_date.strftime('%Y-%m-%d')

    resource_url = credentials["RESOURCE_URL"]
    #print(f"{token}")
    #print(f"{json.dumps(credentials.to_dict(), indent=4)}")
    #print(f"{resource_url}")
    #fields = ['emailaddress2', 'mercury_lastcontacted', 'mercury_lastcontacted', 'mercury_lastcalled', 'mercury_lastcalledby', 'ownerid', 'recruit_owningconsultant', 'address2_city', 'address2_stateorprovince']
    #Fields being extracted, need to investigate others as needed.
    fields = ['emailaddress2', 'mercury_lastcontacted', 'mercury_lastcalled', 'address2_city', 'address2_stateorprovince', 'statecode', 'recruit_iscandidatecontact', 'contactid', 'recruit_userfield10']
    if not normal :
        whereClause = f""" (statecode eq 0) and (recruit_iscandidatecontact eq true) and (emailaddress2 eq '{email}')
        and (((mercury_lastcontacted eq null) or (mercury_lastcontacted le {last_12_months_str}))
        and ((mercury_lastcalled eq null) or (mercury_lastcalled le {last_12_months_str})))
        """
    else:
        whereClause = f""" (statecode eq 0) and (recruit_iscandidatecontact eq true) and (emailaddress2 eq '{email}')
        """

    response = read_fields_from_dataverse(token, resource_url, "contact", fields, whereClause)
    #print(response)
    if response is None or len(response['value']) == 0:
        return []
    #print(f"found {email} contactid = {response['value'][0]['contactid']}")
    return response['value']

def contact_is_missing(res):
    if (len(res) == 0):
        return True
    return False

def contact_has_skills_assigned(contact):
    flags = contact[0].get('recruit_userfield10', None)
    if flags is None:
        return False
    if flags == '':
        return False
    return True

def get_all_candidate_info(token, dataverse_url, candidateid, logger=None):
    if logger == None:
        logger = getGlobalAppLogger()
    fields = ['fullname','emailaddress2', 'recruit_cvurl', 'recruit_userfield10', 'recruit_mobilehome', 'telephone2',
        'recruit_candidatetagcontrol1', 'recruit_nationalityid', 'address2_city', 'recruit_address2_state', 
        'recruit_address2_country', 'recruit_willrelocate', 'recruit_availability', 'mercury_lastcontacted', 'jobtitle',
        'address2_stateorprovince', 'recruit_candidatecontactstatus', 'recruit_donotsms'
    ]

    # Join the fields for $select
    select_fields = ','.join(fields)
    # Navigation property for the country lookup
    expand_clause = ','.join([
        'recruit_address2_country($select=mercury_name)',
        'recruit_address2_state($select=mercury_name)',
        'recruit_nationalityid($select=recruit_name)',
        'recruit_contact_candidatetag($select=)'
    ])
    whereClause = f" (contactid eq '{candidateid}') "

     # Build the URL with $select, $expand, and $filter
    url = (
        f"{dataverse_url}/api/data/v9.2/contacts?"
        f"$select={select_fields}"
        f"&$expand={expand_clause}"
        f"&$filter=contactid eq {candidateid}"
    )
    
    # Prepare headers
    headers = {
        'Authorization': f'Bearer {token.get_token()}',
        'Content-Type': 'application/json',
        'OData-MaxVersion': '4.0',
        'OData-Version': '4.0'
    }

    # Make the request
    response = requests.get(url, headers=headers)

    # Check response and return data
    if response.status_code == 200:
        data = response.json()
        if 'value' in data and len(data['value']) > 0:
            return data['value']
        else:
            return []  # Empty if no data found
    else:
        logger.error(f" file get_candidate_newdata_tojson - {response.status_code} - {response.text}")
        return None


#Only get candidates who are active.
def get_candidate(dataverse_url, token, contactid):

    headers = {
        "Authorization": f"Bearer {token.get_token()}",
        "Content-Type": "application/json"
    }
    
    filter_query = f"contactid eq {contactid} and statecode eq 0"
    url = f"{dataverse_url}/api/data/v9.1/contacts?$select=emailaddress2, recruit_cvurl, recruit_userfield10, contactid,_recruit_address2_country_value&$filter={filter_query}"
    response = requests.get(url, headers=headers)

    if response.status_code == 200:
        data = response.json()
        return  data.get("value", [])

    else:
        raise DataverseError(f"file - get_candidate_list -  \nFailed to find. Status code: {response.status_code} - \nResponse:, {response.text}")

def testContactRead():
    users = [
        ["<EMAIL>", 2, True],
        ["**<EMAIL>", 0, True],
        ["<EMAIL>", 0, True],
        ["<EMAIL>", 2, True],
    ]

    for user in users:
        res = readContactInfo(user[0], user[1], user[2])
        if contact_is_missing(res):
            print(f"Got empty contact for {user[0]}")
        else:
            print(f"Got contact for {user[0]} -> {res[0]['contactid']}")
            if contact_has_skills_assigned(res):
                print(f"Contact {user[0]} has skills assigned")
            else:
                print(f"Contact {user[0]} -> {res[0]['contactid']} has no skills assigned")
        print(res)
        print("--------------------\n")

def test_get_candidate_info():
    load_secrets_env_variables()
    env = Environment.SANDBOX
    token = get_token_for_env(env)
    dataverse_url = get_dataverse_credentials_for_env(env)["RESOURCE_URL"]
    #candidateid = "45cc37b0-90e7-ee11-85fb-6045bd7b41b0" #Prod
    candidateid = "b077425b-cde2-ef11-a731-7c1e52580d80" #Sandbox
    res = get_all_candidate_info(token, dataverse_url, candidateid)

    jobtitle = res[0]['jobtitle']
    print(f"Job title: {jobtitle}")
    state = res[0]['recruit_address2_state']['mercury_name']
    print(f"State: {state}")
 
if __name__ == "__main__":
    #testContactRead()
    test_get_candidate_info()
    