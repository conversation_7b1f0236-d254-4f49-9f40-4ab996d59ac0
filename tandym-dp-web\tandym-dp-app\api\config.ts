const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || "http://0.0.0.0:8005";
const PORTAL_SERVICE_BASE_URL =
  process.env.DP_PORTAL_SERVICE || "http://0.0.0.0:8006";
// Ensure this is the correct base URL for your experiment APIs,
// the example uses localhost:8006, so adjust if necessary.
// For this example, I will use the user-provided localhost:8006
export const EXPERIMENT_BASE_URL =
  process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:8005";

export const isADLogin = (): boolean =>
  process.env.NEXT_PUBLIC_AD_LOGIN === "true";

export const isParsedResume =
  process.env.NEXT_PUBLIC_PARSE_RESUME_FROM_MERCURY === "true";

export const IS_WHY_FIT_EDITABLE = process.env.NEXT_PUBLIC_IS_WHY_FIT_EDITABLE;

export const IS_LOCK_FEATURE_DISABLED =
  process.env.NEXT_PUBLIC_IS_LOCK_FEATURE_DISABLED;

export const API_ENDPOINTS = {
  categories: `${BASE_URL}/categories`,
  subCategories: `${BASE_URL}/subcategories`,
  subCategoriesPools: `${BASE_URL}/subcategory/pools`,
  jobTitlesBySubCategory: `${BASE_URL}/jobtitles/:sub_category_id?limit=10000000`,
  deleteAttribute: `${BASE_URL}/attribute/delete/:attribute_id`,
  fetchAttributesBySubCategory: `${BASE_URL}/attributes/:sub_category_id?limit=10000000`,
  fetchWeightsBySubCategory: `${BASE_URL}/weights/:sub_category_id`,
  updateAttributeWeight: `${BASE_URL}/attributes/:sub_category_id/update`,
  updateSubcategoryOfAttribute: `${BASE_URL}/attributes/:attribute_id/subcategory`,
  updateAttributeApprovalStatus: `${BASE_URL}/attributes/:attribute_id/approval`,
  candidatesData: `${BASE_URL}/candidates`,
  updateCandidatesReviewData: `${BASE_URL}/candidates/update_in_db`,
  jobsData: `${BASE_URL}/jobs`,
  getVacancies: `${BASE_URL}/vacancies`,
  getVacanciesFromFiles: `${BASE_URL}/files/vacancies`,
  getCandidatesByVacancyId: `${BASE_URL}/candidates/:vacancy_id`,
  getResumeByContactId: `${BASE_URL}/resume/:contact_id`,
  getResumeFromFileByContactId: `${BASE_URL}/files/candidate-resume/:contact_id`,
  getAllSubcategoryWeightConfigs: `${BASE_URL}/v1/subcategory/weight-configs`,
  updateSubcategoryWeightConfig: `${BASE_URL}/v1/subcategory/weight-configs/:subcategory_id`,
  getCandidateStats: `${BASE_URL}/api/candidate-stats`,
  getEntitlements: `${PORTAL_SERVICE_BASE_URL}/api/entitlement`,
  getVacancyConfigAndStatus: `${BASE_URL}/vacancies/:vacancy_id/status`,
  startVacancyReview: `${BASE_URL}/vacancies/:vacancy_id/start`,
  completeVacancyReview: `${BASE_URL}/vacancies/:vacancy_id/complete`,
  updateWhyFitData: `${BASE_URL}/candidates/fitness_reason`,
  regenerateCatalystMatch: `${BASE_URL}/vacancies/:vacancy_id/regenerate-catalyst-match`,
  catalystMatchStatus: `${BASE_URL}/vacancies/:vacancy_id/catalystmatchstatus`,

  // New Experiment Endpoints
  experimentGetVacancies: `${EXPERIMENT_BASE_URL}/experiment/vacancies`,
  experimentGetVacancyRunDetails: `${EXPERIMENT_BASE_URL}/experiment/vacancies/:vacancy_id/runs/:run_id/details`,
  experimentGetCandidatesForVacancyRun: `${EXPERIMENT_BASE_URL}/experiment/vacancies/:vacancy_id/runs/:run_id/candidates`,
  experimentGetRunConfig: `${EXPERIMENT_BASE_URL}/experiment/runs/:run_id/config`,
  experimentGetCandidateResume: `${EXPERIMENT_BASE_URL}/experiment/candidates/:contact_id/resume`,
  experimentArchiveVacancy: `${EXPERIMENT_BASE_URL}/experiment/vacancies/archive`, // Added new endpoint
  experimentPromoteResults: `${EXPERIMENT_BASE_URL}/experiment/results/promote`, // + New endpoint for promoting results
};
