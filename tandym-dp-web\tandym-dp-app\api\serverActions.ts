// api/serverActions.ts
"use server";
import { API_ENDPOINTS } from "./config";
import { getData } from "./get";
import { postData } from "./post";
import { updateData } from "./put";
import { EntitlementResponse } from "./types";

export async function fetchAllCategories() {
  try {
    const response = await fetch(API_ENDPOINTS.categories);
    const data = await response.json();
    return data?.categories || [];
  } catch (error) {
    console.error("Error fetching subcategories:", error);
    return [];
  }
}

export async function fetchAllSubCategories() {
  try {
    const response = await fetch(API_ENDPOINTS.subCategories);
    const data = await response.json();
    return data?.subcategories || [];
  } catch (error) {
    console.error("Error fetching subcategories:", error);
    return [];
  }
}

export async function fetchJobTitlesBySubCategory(subCategoryId: number) {
  try {
    const response = await fetch(
      API_ENDPOINTS.jobTitlesBySubCategory.replace(
        ":sub_category_id",
        subCategoryId.toString()
      )
    );
    const data = await response.json();
    return data?.job_titles || [];
  } catch (error) {
    console.error("Error fetching job titles:", error);
    return [];
  }
}

export async function deleteAttributeTitleById(attributeId: number) {
  try {
    const response = await fetch(
      API_ENDPOINTS.deleteAttribute.replace(
        ":attribute_id",
        attributeId.toString()
      ),
      { method: "DELETE" }
    );
    return response.ok;
  } catch (error) {
    console.error("Error deleting job title:", error);
    return false;
  }
}

export async function fetchAttributeBySubcategoryId(subCategoryId: number) {
  try {
    const response = await fetch(
      API_ENDPOINTS.fetchAttributesBySubCategory.replace(
        ":sub_category_id",
        subCategoryId.toString()
      )
    );
    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error deleting job title:", error);
    return false;
  }
}

export async function updateAttributeWeight(
  subCategoryId: number,
  updatedData: any
) {
  const url = API_ENDPOINTS.updateAttributeWeight.replace(
    ":sub_category_id",
    subCategoryId.toString()
  );

  try {
    const response = await postData(url, updatedData);
    return response; // Return the API response
  } catch (error) {
    console.error("Error updating attribute weight:", error);
    throw new Error("Failed to update attribute weight");
  }
}

export async function updateSubcategoryOfAttribute(
  attributeId: number,
  data: { new_subcategory_id: number }
) {
  const url = API_ENDPOINTS.updateSubcategoryOfAttribute.replace(
    ":attribute_id",
    attributeId.toString()
  );

  try {
    const response = await updateData(url, data);
    return response;
  } catch (error) {
    throw new Error("Failed to update attribute subcategory");
  }
}

export async function updateAttributeApprovalStatus(
  attributeId: number,
  data: { is_approved: boolean }
) {
  const url = API_ENDPOINTS.updateAttributeApprovalStatus.replace(
    ":attribute_id",
    attributeId.toString()
  );

  try {
    const response = await updateData(url, data);
    return response; // Return the API response
  } catch (error) {
    console.error("Error updating attribute approval status:", error);
    throw new Error("Failed to update attribute approval status");
  }
}

export async function fetchWeightsBySubcategoryId(subCategoryId: number) {
  try {
    const response = await fetch(
      API_ENDPOINTS.fetchWeightsBySubCategory.replace(
        ":sub_category_id",
        subCategoryId.toString()
      )
    );
    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error deleting job title:", error);
    return false;
  }
}

export async function fetchVacancies() {
  try {
    const response = await fetch(API_ENDPOINTS.getVacancies);
    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error deleting job title:", error);
    return false;
  }
}

export async function fetchVacanciesFromFiles() {
  try {
    const response = await fetch(API_ENDPOINTS.getVacanciesFromFiles);
    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error deleting job title:", error);
    return false;
  }
}

export async function fetchCandidatesByVacancyId(vacancyId: string) {
  try {
    const response = await fetch(
      API_ENDPOINTS.getCandidatesByVacancyId.replace(
        ":vacancy_id",
        vacancyId.toString()
      )
    );
    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error deleting job title:", error);
    return false;
  }
}

export async function fetchResumeByCandidateId(contactId: string) {
  try {
    const response = await fetch(
      API_ENDPOINTS.getResumeByContactId.replace(
        ":contact_id",
        contactId.toString()
      )
    );
    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error while fetching resume:", error);
    return false;
  }
}

export async function fetchResumeByCandidateIdFromFiles(contactId: string) {
  try {
    const response = await fetch(
      API_ENDPOINTS.getResumeFromFileByContactId.replace(
        ":contact_id",
        contactId.toString()
      )
    );
    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error while fetching resume:", error);
    return false;
  }
}

interface RecruiterPostReview {
  hiring_decision: string;
  review_message: string;
  candidate_contact_id: string;
  recruiter_email: string;
}

export async function updateRecruiterReview(data: any) {
  const url = API_ENDPOINTS.updateCandidatesReviewData;
  console.log("updateRecruiterReview data::", data);
  try {
    const response = await postData(url, data);
    return response; // Return the API response
  } catch (error) {
    console.error("Error updating attribute weight:", error);
    throw new Error("Failed to update attribute weight");
  }
}

export async function getVacancyConfigAndStatus(vacancyId: string) {
  try {
    const response = await fetch(
      API_ENDPOINTS.getVacancyConfigAndStatus.replace(
        ":vacancy_id",
        vacancyId.toString()
      )
    );
    const data = await response.json();
    console.log("data::", data);
    return data;
  } catch (error) {
    console.error("Error while fetching vacancy status:", error);
    return false;
  }
}

export async function startVacancyreview(
  vacancyId: string,
  payload: { reviewer: string }
) {
  try {
    const url = API_ENDPOINTS.startVacancyReview.replace(
      ":vacancy_id",
      vacancyId.toString()
    );
    const response = await postData(url, payload);
    return response;
  } catch (error) {
    console.error("Error while start vacancy review:", error);
    return false;
  }
}

export async function completeVacancyreview(
  vacancyId: string,
  payload: { reviewer: string }
) {
  try {
    const url = API_ENDPOINTS.completeVacancyReview.replace(
      ":vacancy_id",
      vacancyId.toString()
    );
    const response = await postData(url, payload);
    return response;
  } catch (error) {
    console.error("Error while start vacancy review:", error);
    return false;
  }
}

export async function updateWhyFit(data: any) {
  try {
    const url = API_ENDPOINTS.updateWhyFitData;
    const response = await postData(url, data);
    return response;
  } catch (error) {
    console.log("Error while updating whyfit: ", error);
    return false;
  }
}
export async function fetchEntitlements(
  email_id: string
): Promise<EntitlementResponse | false> {
  try {
    const isEntitlementEnabled = process.env.IS_ENTITLEMENT_ENABLED === "true";
    if (!isEntitlementEnabled) {
      return {
        error: false,
        code: "TR_01",
        message: "Successful",
        entitlement: {
          Work_force_Index: true,
          Sub_Catregory: true,
          Vacancy: true,
          Search_Match: true,
          Sc_Score_Config: true,
        },
      };
    }
    const portal_name = "recruiter";
    const url = `${API_ENDPOINTS.getEntitlements}?email_id=${encodeURIComponent(
      email_id
    )}&portal_name=${encodeURIComponent(portal_name)}`;
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Failed to fetch entitlements: ${response.statusText}`);
    }

    const data: EntitlementResponse = await response.json();
    return data;
  } catch (error) {
    console.error("Error fetching entitlement data:", error);
    return false;
  }
}
export async function getAllSubcategoryWeightConfigs() {
  try {
    const response = await fetch(API_ENDPOINTS.getAllSubcategoryWeightConfigs);
    const data = await response.json();
    return data?.subcategory_weight_configs || [];
  } catch (error) {
    console.error("Error fetching subcategories:", error);
    return [];
  }
}

export async function updateSubcategoryWeightConfig(
  subcategoryId: number,
  data: any
) {
  const url = API_ENDPOINTS.updateSubcategoryWeightConfig.replace(
    ":subcategory_id",
    subcategoryId.toString()
  );

  try {
    const response = await updateData(url, data);
    return response;
  } catch (error) {
    throw new Error("Failed to update SubcategoryWeightConfig");
  }
}

export async function regenerateCatalystMatch (vacancy_id: string, data: any) {
  const url = API_ENDPOINTS.regenerateCatalystMatch.replace(":vacancy_id", vacancy_id);
  try {
    // const response = await postData(url, data);
    // return response;
    return {
      "status": "queued",
      "initiated_at": "2025-06-30T20:05:16.010609Z",
      "completed_at": null,
      "initiated_by": "<EMAIL>"
    };
  } catch (error) {
    throw new Error("Failed to update SubcategoryWeightConfig");
  }
}

export async function getCatalystMatchStatus (vacancy_id: string) {
  const url = API_ENDPOINTS.catalystMatchStatus.replace(":vacancy_id", vacancy_id);
  try {
    // const response = await getData(url);
    // return response;
    // Mock response for testing - replace with actual API call
    return {
      "catalyst_match_status": {
        "status": "completed",
        "initiated_at": "2025-06-30T20:05:16.010609Z",
        "completed_at": "2025-06-30T20:06:30.123456Z",
        "initiated_by": "<EMAIL>"
      },
      "update_timestamps": {
        "data_last_updated_at": "2025-06-30T20:06:30.123456Z",
        "archived": false
      }
    };
  } catch (error) {
    throw new Error("Failed to get catalyst match status");
  }
}