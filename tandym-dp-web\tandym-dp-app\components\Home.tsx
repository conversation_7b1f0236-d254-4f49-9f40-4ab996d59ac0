import React, { useMemo } from "react";
import { Button } from "../components/ui/button";
import Link from "next/link";
import { useSession } from "next-auth/react";
import { TAB_ROUTE_MAP } from "@/utils/tabRoutes";
import { useEntitlement } from "@/context/EntitlementContext";
import { getAppInsights } from "@/library/appInsights";

interface InfoCardProps {
  title: string;
  description: string;
  buttonText: string;
  link: string;
  disable: boolean;
}

// This function renders the info card with the given props
// and an optional fullWidth parameter to determine if it should span the full width of the grid.
const renderInfoCard = (
  { title, description, buttonText, link, disable }: InfoCardProps,
  fullWidth = false
) => (
  <div
    key={title}
    className={`p-4 sm:p-6 bg-white flex flex-col justify-between ${
      fullWidth ? "sm:col-span-2" : ""
    }`}
  >
    <div>
      <p className="text-fluid-lg font-normal leading-none tracking-normal text-almostBlack">
        {title}
      </p>
      <p className="font-light text-fluid-sm align-middle mt-2 text-darkGray">
        {description}
      </p>
    </div>
    <div className="mt-10">
      <Link href={`${link}`}>
        <Button
          onClick={() => {
            getAppInsights()?.trackEvent({
              name: `FE_${buttonText}_Button_Clicked`,
              properties: { button: buttonText },
            });
          }}
          className="bg-primaryButton text-primaryButton-foreground"
          disabled={disable}
        >
          {buttonText}
        </Button>
      </Link>
    </div>
  </div>
);

const WelcomeCard = () => {
  const { data: session } = useSession(); // Get the session data
  const userName = session?.user?.name; // Get the user name from the session data
  return (
    <div className="relative w-full h-[270px] bg-black md:bg-[url(../images/home-banner.jpg)] bg-no-repeat bg-center bg-cover">
      <div className="absolute top-14 sm:left-8 p-4">
        <p className="text-4xl font-normal text-white">
          {`Welcome ${userName ?? ""}`}
        </p>
        <p className="text-2xl font-medium text-white mt-3">
          Find the Right Fit, Fast.
        </p>
        <p className="text-lg font-light text-white">
          Discover top talent that clicks with your culture.
        </p>
      </div>
    </div>
  );
};

const ABOUT_PORTAL_TITLE = "About Recruitment Portal";
const ABOUT_PORTAL_DESCRIPTION = `The recruiter portal helps the recruiter to view and organise the
information regarding the Vacancy Sub-Categories, Work force
Readiness, and matching candidates for Vacancies. The Recruiter
can go through and edit the sub Categories, view and modify the
matching candidates for a vacancy.`;

const Home = () => {
  const { entitlements } = useEntitlement();

  const parsedEntitlement = useMemo(() => {
    if (typeof entitlements === "string") {
      try {
        return JSON.parse(entitlements);
      } catch (error) {
        console.error("Failed to parse entitlement:", error);
        return {};
      }
    }
    return entitlements || {}; // Use entitlement directly if it's already an object
  }, [entitlements]);

  // static data for the info cards
  const infoCards: InfoCardProps[] = [
    {
      title: "Sub-Category Library",
      description:
        "The recruiter can go through the Soft Skills, Technical Skills, tools & Platform, Degree & Certification and Job Titles related to the subcategories and do some updates on them.",
      buttonText: "Sub-Category Library",
      link: parsedEntitlement?.Sub_Catregory ? TAB_ROUTE_MAP.skillsEditor : "",
      disable: !parsedEntitlement?.Sub_Catregory,
    },
    {
      title: "Workforce Readiness Index",
      description:
        "For each sub category how many candidates are fresh and available in Tandym database.",
      buttonText: "Workforce Readiness Index",
      link: parsedEntitlement?.Work_force_Index
        ? TAB_ROUTE_MAP.workforceIndex
        : "",
      disable: !parsedEntitlement?.Work_force_Index,
    },
    {
      title: "Vacancy",
      description:
        "The vacancy feature will provide the recruiters the list of matching candidates for vacancies. The recruiter will be able to see the Parsed resume, CV and other details of the matching candidates.",
      buttonText: "Vacancy",
      link: parsedEntitlement?.Vacancy ? TAB_ROUTE_MAP.Vacancy : "",
      disable: !parsedEntitlement?.Vacancy,
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-[22%_77%] sm:m-4 gap-4">
      <div className="block md:hidden">
        <WelcomeCard />
      </div>
      <div className="bg-white flex flex-col justify-between">
        <div>
          <div className="text-center">
            <p className="text-fluid-xl font-normal leading-none tracking-normal bg-black text-white p-4">
              {ABOUT_PORTAL_TITLE}
            </p>
          </div>
          <div className="p-5 bg-white">
            <p className="font-normal text-fluid-xs leading-5 tracking-normal align-middle text-darkerGray">
              {ABOUT_PORTAL_DESCRIPTION}
            </p>
          </div>
        </div>
      </div>
      <div>
        <div className="hidden md:block">
          <WelcomeCard />
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-5 mt-4">
          {infoCards.map((card, index) => {
            const isLastAndOdd =
              index === infoCards.length - 1 && infoCards.length % 2 !== 0;
            return renderInfoCard(card, isLastAndOdd);
          })}
        </div>
      </div>
    </div>
  );
};

export default Home;
