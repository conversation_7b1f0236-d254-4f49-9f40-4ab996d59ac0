import time
from typing import List, Optional, Any, Dict
from openai import OpenAI, APIError, RateLimitError, AuthenticationError, APITimeoutError, APIConnectionError
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
from .utils.appLogger2 import AppLogger2

# Define exceptions that should trigger a retry
RETRYABLE_OPENAI_EXCEPTIONS = (
    APIError,       # General API error
    RateLimitError,
    APITimeoutError,
    APIConnectionError,
    # Some 5xx errors might be included if OpenAI SDK doesn't map them to specific exceptions
)

@retry(
    wait=wait_exponential(multiplier=1, min=2, max=60), # Exponential backoff: 2s, 4s, 8s, ... up to 60s
    stop=stop_after_attempt(5), # Retry up to 5 times
    retry=retry_if_exception_type(RETRYABLE_OPENAI_EXCEPTIONS),
    reraise=True # Reraise the last exception if all retries fail
)
def get_embedding_with_retry(
    text_to_embed: str,
    client: OpenAI,
    model_name: str,
    dimensions: int,
    logger: AppLogger2
) -> Optional[List[float]]:
    """
    Fetches an embedding for the given text from OpenAI API with retry logic.
    """
    logger.debug(f"Attempting to get embedding for text (first 50 chars): '{text_to_embed[:50]}...' using model {model_name}")
    response = client.embeddings.create(
        input=[text_to_embed], 
        model=model_name, 
        dimensions=dimensions
    )
    if response.data and response.data[0].embedding:
        logger.debug(f"Successfully received embedding for text (first 50 chars): '{text_to_embed[:50]}...'")
        return response.data[0].embedding
    else:
        # This case should ideally not happen if API call was successful and data is expected
        logger.warning(f"OpenAI API call successful but no embedding data returned for text: '{text_to_embed[:50]}...' with model {model_name}")
        return None

def get_embedding(
    text: str,
    client: OpenAI,
    model_name: str,
    dimensions: int,
    logger: AppLogger2
) -> Optional[List[float]]:
    """
    Main function to get an embedding. It handles input validation and calls the retry-enabled function.
    """
    if not isinstance(text, str) or not text.strip():
        logger.warning("Attempted to get embedding for empty or non-string text.")
        return None
    if not client:
        logger.error("OpenAI client is not initialized.")
        return None
    if not model_name:
        logger.error("Embedding model name is not specified.")
        return None
    if not isinstance(dimensions, int) or dimensions <= 0:
        logger.error(f"Invalid dimensions specified: {dimensions}. Must be a positive integer.")
        return None

    clean_text = text.replace("\n", " ").strip()
    if not clean_text: # If text becomes empty after cleaning
        logger.warning("Text became empty after cleaning newlines and stripping whitespace.")
        return None

    try:
        return get_embedding_with_retry(clean_text, client, model_name, dimensions, logger)
    except AuthenticationError as e:
        logger.error(f"OpenAI Authentication Error: {e}. Check API key and permissions.", exc_info=True)
        return None
    except RateLimitError as e:
        logger.error(f"OpenAI Rate Limit Exceeded: {e}. All retries failed.", exc_info=True)
        return None
    except APIError as e: # Catch other OpenAI specific API errors if not caught by retryable
        logger.error(f"OpenAI API Error after retries for text '{clean_text[:50]}...': {e}", exc_info=True)
        return None
    except Exception as e: # Catch any other unexpected errors (including reraised from tenacity)
        logger.error(f"Unexpected error fetching embedding after retries for '{clean_text[:50]}...': {e}", exc_info=True)
        return None


def generate_vacancy_embeddings(
    vacancy_data: Dict[str, Any],
    effective_config: Dict[str, Any],
    openai_client: OpenAI,
    logger: AppLogger2
) -> Dict[str, Any]:
    """
    Generates embeddings for relevant items in the vacancy data.
    This version is more robust in handling missing configurations and data.
    """
    vacancy_embeddings_map: Dict[str, Any] = {}
    
    embedding_model_details = effective_config.get("embedding_model_details")
    if not isinstance(embedding_model_details, dict):
        logger.error("`embedding_model_details` missing or not a dict in effective_config. Cannot generate embeddings.")
        return {"error": "Missing embedding_model_details configuration"} # Return an error indicator

    model_name = embedding_model_details.get("model_name")
    dimensions = embedding_model_details.get("dimensions")

    if not model_name or not (isinstance(dimensions, int) and dimensions > 0):
        logger.error(f"Invalid embedding model details in config: model='{model_name}', dimensions='{dimensions}'.")
        return {"error": "Invalid embedding_model_details"}

    # Job Title Embedding
    job_title_list = vacancy_data.get('job title', []) # Expect a list
    job_title_str = ""
    if isinstance(job_title_list, list) and job_title_list:
        job_title_str = str(job_title_list[0]) # Take the first job title
    elif isinstance(job_title_list, str): # Handle case where it might be a string directly
        job_title_str = job_title_list
    
    if job_title_str.strip():
        title_embedding = get_embedding(job_title_str, openai_client, model_name, dimensions, logger)
        vacancy_embeddings_map["job_titles"] = title_embedding # Will be None if embedding failed
        if title_embedding:
            logger.info(f"Generated embedding for vacancy job title: '{job_title_str}'")
        else:
            logger.warning(f"Could not generate embedding for vacancy job title: '{job_title_str}'")
    else:
        logger.info("Vacancy has no job title string. 'job_titles' embedding will be None.")
        vacancy_embeddings_map["job_titles"] = None

    # Embeddings for other categories (skills, tools, industries, etc.)
    # These map to `attribute_embedding_map` keys from master_config
    # The `vacancy_key_map` defines how to find these items in the input `vacancy_data`
    vacancy_key_map = {
        "technical_skills": "technical_skills",
        "tools_platforms": "tools_and_platforms",
        "degrees_certs": "degrees_and_certifications",
        "industries": "client_name_industry" # This key in vacancy_data might hold a string or list of strings/dicts
    }
    
    scoring_params = effective_config.get("effective_scoring_parameters", {})
    # Use a robust way to get vacancy_item_weights_map, falling back to a default if necessary
    default_item_weights = {"high": 3, "medium": 2, "normal": 1}
    vacancy_item_weights = scoring_params.get("vacancy_item_weights_map", default_item_weights)
    if not isinstance(vacancy_item_weights, dict): # Fallback if config is malformed
        logger.warning("Malformed 'vacancy_item_weights_map' in config, using default weights.")
        vacancy_item_weights = default_item_weights

    default_degree_cert_weight = 1.0
    # Define a new default weight for industry items when they are strings and the feature flag is active
    default_industry_string_item_weight = 1.0 # You can adjust this value as needed
    vacancy_template_data = vacancy_data.get("job_template", {})

    for attribute_key, vacancy_field_name in vacancy_key_map.items():
        vacancy_embeddings_map[attribute_key] = [] # Initialize as list for items
        items_from_vacancy = vacancy_template_data.get(vacancy_field_name, [])
        
        # Ensure items_from_vacancy is always a list for uniform processing
        if not isinstance(items_from_vacancy, list):
            items_from_vacancy = [items_from_vacancy] if items_from_vacancy else []

        for item_data in items_from_vacancy:
            item_name: Optional[str] = None
            item_weight_str: str = "normal" # Default weight importance

            if isinstance(item_data, dict):
                item_name = item_data.get('name')
                item_weight_str = item_data.get('weight', 'normal')
            elif isinstance(item_data, str):
                item_name = item_data
            else:
                logger.warning(f"Skipping item in '{vacancy_field_name}' due to unexpected data type: {type(item_data)}")
                continue
            
            if not item_name or not isinstance(item_name, str) or not item_name.strip():
                logger.debug(f"Skipping empty or invalid item name in '{vacancy_field_name}'.")
                continue

            # Determine weight value
            weight_value: float
            # Get the feature flag from scoring_params; defaults to False if not present
            apply_specific_default_for_industry_string = scoring_params.get("apply_specific_default_to_industry_string_items", False)

            if attribute_key == "industries" and isinstance(item_data, str) and apply_specific_default_for_industry_string:
                # Apply the hardcoded default weight if it's an industry string and the flag is true
                weight_value = default_industry_string_item_weight
            elif attribute_key == "degrees_certs":
                weight_value = default_degree_cert_weight
            else:
                # Existing logic for:
                # - Non-industry, non-degrees_certs categories.
                # - Industry category if item_data is a dict.
                # - Industry category if item_data is a string BUT apply_specific_default_for_industry_string is False.
                weight_value = float(vacancy_item_weights.get(item_weight_str.lower(), 1.0))
            
            item_embedding = get_embedding(item_name, openai_client, model_name, dimensions, logger)
            if item_embedding:
                vacancy_embeddings_map[attribute_key].append({
                    "name": item_name,
                    "embedding": item_embedding,
                    "weight": weight_value,
                    "category": attribute_key
                })
                logger.debug(f"Generated embedding for vacancy item '{item_name}' (category: {attribute_key})")
            else:
                logger.warning(f"Could not get embedding for vacancy item '{item_name}' (category: {attribute_key}).")
    
    logger.info(f"Vacancy embeddings map generation complete. Job title embedding present: {vacancy_embeddings_map.get('job_titles') is not None}")
    return vacancy_embeddings_map