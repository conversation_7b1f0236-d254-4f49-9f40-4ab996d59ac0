# sendgrid_helper.py

import os
import logging
import sys
from typing import Union, List, Dict

from sendgrid import SendGridAPIClient
from sendgrid.helpers.mail import Mail as SGMail, Email, To, Content, Personalization
from jinja2 import Environment, FileSystemLoader, select_autoescape

# Add project root to Python path for imports
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_ROOT)

from common.appLogger import AppLogger

class SendGridEmailHelper:
    _instance = None

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super(SendGridEmailHelper, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def _initialize(self, logger=None):
        # Use provided logger or create a default one
        self.logger = logger if logger is not None else self._create_default_logger()

        # Load configuration
        self.api_key = os.environ.get('SENDGRID_API_KEY')
        if not self.api_key:
            raise ValueError("Missing SENDGRID_API_KEY environment variable")
        self.default_sender = os.environ.get('SENDGRID_DEFAULT_SENDER', '<EMAIL>')
        self.sg_client = SendGridAPIClient(api_key=self.api_key)

        # Jinja2 setup (expects templates in 'email_templates' folder)
        self.jinja_env = Environment(
            loader=FileSystemLoader("utils/email/templates"),
            autoescape=select_autoescape(['html', 'txt'])
        )

    def _create_default_logger(self):
        """Create a default logger if none is provided."""
        logger = logging.getLogger("SendGridEmailHelper")
        if not logger.hasHandlers():
            handler = logging.StreamHandler()
            formatter = logging.Formatter('[%(levelname)s] %(asctime)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger

    def __init__(self, logger=None):
        """Initialize the SendGrid Email Helper with an optional logger."""
        # Skip initialization if already done
        if self._initialized:
            return
            
        self._initialize(logger)
        self._initialized = True

    def send_email(
        self,
        subject: str,
        html_content: str,
        to_emails: Union[str, List[str]],
        from_email: str = None,
        plain_text: str = None,
        cc_emails: Union[str, List[str]] = None,
        bcc_emails: Union[str, List[str]] = None
    ) -> Dict:
        """
        Send an email using SendGrid with optional plain text fallback.
        """
        from_email = from_email or self.default_sender
        to_emails = to_emails if isinstance(to_emails, list) else [e.strip() for e in to_emails.split(';')]
        cc_emails = cc_emails if isinstance(cc_emails, list) else ([e.strip() for e in cc_emails.split(',')] if cc_emails else [])
        bcc_emails = bcc_emails if isinstance(bcc_emails, list) else ([e.strip() for e in bcc_emails.split(',')] if bcc_emails else [])

        message = SGMail(
            from_email=Email(from_email),
            subject=subject,
            html_content=Content("text/html", html_content)
        )

        if plain_text:
            message.add_content(Content("text/plain", plain_text))

        personalization = Personalization()
        for to in to_emails:
            personalization.add_to(To(to))
        for cc in cc_emails:
            personalization.add_cc(To(cc))
        for bcc in bcc_emails:
            personalization.add_bcc(To(bcc))

        message.add_personalization(personalization)

        try:
            self.logger.info(f"Sending email to {to_emails} with subject '{subject}'")
            response = self.sg_client.send(message)
            self.logger.info(f"SendGrid response: {response.status_code}")
            return {
                'status_code': response.status_code,
                'body': response.body.decode() if hasattr(response.body, 'decode') else str(response.body),
                'headers': dict(response.headers)
            }
        except Exception as e:
            self.logger.error(f"Error sending email: {e}")
            return {'error': str(e)}
        
    def send_templated_email(
        self,
        template_base: str,
        context: Dict,
        to_emails: Union[str, List[str]],
        subject_template: str,
        from_email: str = None,
        cc_emails: Union[str, List[str]] = None,
        bcc_emails: Union[str, List[str]] = None,
    ) -> Dict:
        """
        Sends an email using dynamically selected HTML and text templates + subject templating.

        :param template_base: Name of the template set (e.g. 'catalyst_match') — must match html/txt files.
        :param context: Dictionary for templating both body and subject.
        :param to_emails: Recipients
        :param subject_template: Jinja2 subject template string (e.g. '{{ vacancy_title }} – Match Results Ready')
        """
        try:
            html_template = f"{template_base}.html"
            txt_template = f"{template_base}.txt"

            html_content = self.render_template(html_template, context)
            plain_text = self.render_template(txt_template, context)
            subject = self.jinja_env.from_string(subject_template).render(context)

            return self.send_email(
                subject=subject,
                html_content=html_content,
                plain_text=plain_text,
                to_emails=to_emails,
                from_email=from_email,
                cc_emails=cc_emails,
                bcc_emails=bcc_emails
            )
        except Exception as e:
            self.logger.error(f"send_templated_email failed: {e}")
            return {'error': str(e)}


    def render_template(self, template_name: str, context: Dict) -> str:
        """
        Render a Jinja2 template with the provided context.
        """
        try:
            template = self.jinja_env.get_template(template_name)
            return template.render(context)
        except Exception as e:
            self.logger.error(f"Error rendering template '{template_name}': {e}")
            raise

    def test_send_email(
        self, 
        test_to_email: str, 
        logger=None,
        from_email: str = None,
        cc_emails: Union[str, List[str]] = None,
        bcc_emails: Union[str, List[str]] = None,
        custom_context: Dict = None,
        vacancy_id: str = None,
        vacancy_ref: str = None
    ) -> Dict:
        """
        Send a test email using both HTML and plain-text templates.
        
        Args:
            test_to_email (str): Email address to send test to
            logger: Optional logger instance to use for logging
            from_email (str, optional): Custom from email address
            cc_emails (Union[str, List[str]], optional): CC recipients
            bcc_emails (Union[str, List[str]], optional): BCC recipients
            custom_context (Dict, optional): Custom context to override default test context
            vacancy_id (str, optional): Vacancy ID for reference
            vacancy_ref (str, optional): Vacancy reference number for email subject and content
        """
        # Use provided logger or fall back to instance logger
        log = logger if logger is not None else self.logger
        
        # Default test context
        default_context = {
            "recruiter_name": "Ravi Prattipati",
            "vacancy_title": "Full Stack Engineer - Remote",
            "vacancy_url": "https://tandymgroup.crm.dynamics.com/main.aspx?appid=0ec72dfd-7eb1-ee11-a569-00224822704f&pagetype=entityrecord&etn=crimson_vacancy&id=ef432293-3750-f011-877a-7c1e5214c209",
            "support_email": "<EMAIL>"
        }
        
        # Update vacancy_url with vacancy_id if provided
        if vacancy_id:
            default_context["vacancy_url"] = f"https://tandymgroup.crm.dynamics.com/main.aspx?appid=0ec72dfd-7eb1-ee11-a569-00224822704f&pagetype=entityrecord&etn=crimson_vacancy&id={vacancy_id}"
        
        # Add vacancy_ref to context if provided
        if vacancy_ref:
            default_context["vacancy_ref"] = vacancy_ref
        
        # Use custom context if provided, otherwise use default
        context = custom_context if custom_context is not None else default_context
        
        # If custom context is provided but doesn't include vacancy_ref, add it
        if custom_context is not None and vacancy_ref and "vacancy_ref" not in custom_context:
            context["vacancy_ref"] = vacancy_ref
            
        # If custom context is provided but doesn't include vacancy_url and vacancy_id is provided, add it
        if custom_context is not None and vacancy_id and "vacancy_url" not in custom_context:
            context["vacancy_url"] = f"https://tandymgroup.crm.dynamics.com/main.aspx?appid=0ec72dfd-7eb1-ee11-a569-00224822704f&pagetype=entityrecord&etn=crimson_vacancy&id={vacancy_id}"

        # Build subject template with vacancy_ref if available
        subject_template = "{{ vacancy_title }} - Catalyst Match Results Ready"
        if vacancy_ref:
            subject_template = "{{ vacancy_ref }}: {{ vacancy_title }} - Catalyst Match Results Ready"

        try:
            response = self.send_templated_email(
                template_base="catalyst_match",
                context=context,
                to_emails=test_to_email,
                subject_template=subject_template,
                from_email=from_email,
                cc_emails=cc_emails,
                bcc_emails=bcc_emails
            )
        except Exception as e:
            log.error(f"Error sending test email: {e}")
            return {'error': str(e)}

        return response

def main():
    """Main function to demonstrate SendGrid helper usage with AppLogger."""
    
    # Create logger configuration 
    logger_config = {
        "level": "DEBUG",
        "log_to_stdout": True,
        "log_file": "/mnt/incoming/logs/sendgrid_email_helper.log",
    }
    logger = AppLogger(logger_config)
    
    logger.info("Initializing SendGrid Email Helper with AppLogger")
    
    try:
        # Initialize SendGrid helper with the logger
        email_helper = SendGridEmailHelper(logger=logger)
        
        logger.info("SendGrid Email Helper initialized successfully")
        
        # Example: Send a test email with enhanced parameters including vacancy details
        test_email = "<EMAIL>"
        cc_emails = ["<EMAIL>"]
        bcc_emails = ["<EMAIL>", "<EMAIL>"]
        vacancy_id = "ef432293-3750-f011-877a-7c1e5214c209"
        vacancy_ref = "CR/506215"
        
        logger.info(f"Sending test email to {test_email} with CC: {cc_emails}, BCC: {bcc_emails}")
        logger.info(f"Vacancy ID: {vacancy_id}, Vacancy Ref: {vacancy_ref}")
        
        result = email_helper.test_send_email(
            test_to_email=test_email,
            logger=logger,
            cc_emails=cc_emails,
            bcc_emails=bcc_emails,
            vacancy_id=vacancy_id,
            vacancy_ref=vacancy_ref
        )
        
        if 'error' in result:
            logger.error(f"Failed to send test email: {result['error']}")
        else:
            logger.info(f"Test email sent successfully. Status code: {result.get('status_code')}")
            
    except Exception as e:
        logger.error(f"Error initializing or using SendGrid Email Helper: {str(e)}")
        return 1
    
    logger.info("SendGrid Email Helper example completed successfully")
    return 0

if __name__ == '__main__':
    exit(main())





