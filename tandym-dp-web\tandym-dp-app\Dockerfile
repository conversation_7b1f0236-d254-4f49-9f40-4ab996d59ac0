# Use official Node.js image as base
FROM node:18-alpine

# Set working directory inside the container
WORKDIR /app

ARG NEXT_PUBLIC_AD_LOGIN
ARG NEXT_PUBLIC_PARSE_RESUME_FROM_MERCURY
ARG NEXT_PUBLIC_IS_WHY_FIT_EDITABLE
ARG NEXT_PUBLIC_IS_LOCK_FEATURE_DISABLED
# ARG NEXT_PUBLIC_APPINSIGHTS_CONNECTION_STRING
ENV NEXT_PUBLIC_AD_LOGIN=$NEXT_PUBLIC_AD_LOGIN
ENV NEXT_PUBLIC_PARSE_RESUME_FROM_MERCURY=$NEXT_PUBLIC_PARSE_RESUME_FROM_MERCURY
ENV NEXT_PUBLIC_IS_WHY_FIT_EDITABLE=false 
ENV NEXT_PUBLIC_IS_LOCK_FEATURE_DISABLED=false
# ENV NEXT_PUBLIC_APPINSIGHTS_CONNECTION_STRING=$NEXT_PUBLIC_APPINSIGHTS_CONNECTION_STRING

# Copy package.json and yarn.lock
COPY package.json yarn.lock ./

# Install dependencies
RUN yarn install --frozen-lockfile

# Copy the entire application code
COPY . .

# Expose the port that Next.js runs on
EXPOSE 3000

# Set environment variable to production
ENV NODE_ENV=production

# Build the Next.js app
RUN yarn build

# Start the Next.js app
CMD ["yarn", "start"]
