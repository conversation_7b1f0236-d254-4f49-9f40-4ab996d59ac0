trigger:
  branches:
    include:
      - develop
  paths:
    include:
      - tandym-dp-web

pr:
  branches:
    include:
      - develop
  paths:
    include:
      - tandym-dp-web
parameters:
  - name: location
    displayName: Location
    type: string
    values:
      - ue
    default: ue
  - name: environment
    displayName: Environment
    type: string
    values:
      - dv
      - qa
      - sbe
      - ua
    default: dv
  - name: Deploy
    displayName: Deploy?
    type: boolean
    default: true
  # - name: SonarRequired
  #   displayName: Sonar Required?
  #   type: boolean
  #   default: true

variables:
  - template: variables/${{ parameters.environment }}.yml
  - template: variables/global_variables.yml
    parameters:
      location: ${{ parameters.location }}
      environment: ${{ parameters.environment }}
      # Deploy: ${{ parameters.Deploy }}
  - name: IMAGE_REPOSITORY
    value: "tg${{ parameters.environment }}${{ parameters.location }}envcr001.azurecr.io/tandym-dp-web"
  - name: imageId
    ${{ if eq( parameters['environment'] , 'ua') }}:
      value: "$(Build.SourceBranchName)-$(Build.BuildId)"
    ${{ else }}:
      value: $(Build.BuildId)
  - name: BuildConfiguration
    value: Release
  # - group: SONAR_CONFIG

stages:
  - stage: Build
    displayName: Build Stage
    pool:
      name: tg-${{ parameters.environment }}-pool
    jobs:
      - job: Build
        displayName: Build_${{ parameters.environment }}_DP_WEB
        steps:
          - checkout: self
          - bash: |
              sudo apt update
              sudo apt install openjdk-17-jdk -y
              env | sort
              echo $JAVA_HOME
              java -version
            displayName: Check java versions
          # Fetch secrets from Key Vault
          - template: common/fetch_keyvault_secrets.yml
            parameters:
              armServiceConnection: ${{ variables.ServiceConnectionName }}
              keyVaultName: ${{ variables.KeyVaultName }}
              secretsToFetch: "AcrName,AcrUsername,AcrPassword,AcrUrl,Client-ID,Client-Secret,AzTenant-Id,Subscription-ID"
              runAsPreJob: true

          # Install .NET SDK
          # - task: UseDotNet@2
          #   displayName: Install .NET 8.0.x SDK
          #   inputs:
          #     version: "8.0.x"
          #     packageType: sdk
          # - task: SonarCloudPrepare@2
          #   displayName: "Prepare analysis on SonarCloud"
          #   condition: and(succeeded(), eq(${{ parameters.SonarRequired }}, true))
          #   inputs:
          #     SonarCloud: TandymSonar
          #     organization: $(SONAR_ORG)
          #     projectKey: $(SONAR_PROJECT_API)
          #     scannerMode: "MSBuild"

          # Build the project
          # - task: DotNetCoreCLI@2
          #   displayName: Build
          #   inputs:
          #     command: build
          #     arguments: --output $(System.DefaultWorkingDirectory)/output
          #     configuration: $(BuildConfiguration)
          #     projects: |
          #       $(System.DefaultWorkingDirectory)/tandym-dp-web/Tandym.Portal/Tandym.Service/Tandym.Service.Candidate/Tandym.Service.Candidate.API/Tandym.Service.Candidate.API.csproj
          #       $(System.DefaultWorkingDirectory)/tandym-dp-web/Tandym.Portal/Tests/**/*.csproj

          # Run tests
          # - task: DotNetCoreCLI@2
          #   displayName: Test
          #   inputs:
          #     command: test
          #     projects: |
          #       $(System.DefaultWorkingDirectory)/tandym-dp-web/Tandym.Portal/Tests/**/*.csproj
          #     publishTestResults: true
          #     arguments: '--collect:"XPlat Code Coverage" -- DataCollectionRunSettings.DataCollectors.DataCollector.Configuration.Format=cobertura'

          # Publish code coverage results
          # - task: PublishCodeCoverageResults@1
          #   displayName: Publish code coverage
          #   inputs:
          #     codeCoverageTool: Cobertura
          #     summaryFileLocation: "$(Agent.TempDirectory)/**/coverage.cobertura.xml"
          # - task: SonarCloudAnalyze@2
          #   condition: and(succeeded(), eq(${{ parameters.SonarRequired }}, true))

          # Docker installation (optional, usually unnecessary for Microsoft-hosted agents)
          - script: |
              sudo apt-get update
              sudo apt-get install -y docker.io
            displayName: Install Docker CLI
            condition: eq(variables['Agent.OS'], 'Linux')
          - script: |
              sudo chmod 666 /var/run/docker.sock
            displayName: "Adjust Docker permissions"
          - script: |
              docker info
            displayName: Check Docker Info

          - script: |
              docker system prune -af
              # docker build -f Dockerfile \
              # --build-arg NEXT_PUBLIC_AD_LOGIN=$(NEXT_PUBLIC_AD_LOGIN) \
              # --build-arg NEXT_PUBLIC_PARSE_RESUME_FROM_MERCURY=$(NEXT_PUBLIC_PARSE_RESUME_FROM_MERCURY) \
              # -t tandym-dp-web:$(Build.BuildId) .
            displayName: Clear Docker Space
            workingDirectory: tandym-dp-web/tandym-dp-app

          - task: Docker@2
            displayName: 'Build Docker Image'
            inputs:
              command: build
              Dockerfile: 'tandym-dp-web/tandym-dp-app/Dockerfile'
              repository: 'tandym-dp-web' 
              tags: |
                $(Build.BuildId)
              buildContext: tandym-dp-web/tandym-dp-app
              arguments: |
                --build-arg NEXT_PUBLIC_AD_LOGIN=$(NEXT_PUBLIC_AD_LOGIN)
                --build-arg NEXT_PUBLIC_PARSE_RESUME_FROM_MERCURY=$(NEXT_PUBLIC_PARSE_RESUME_FROM_MERCURY)

          - script: |
              echo "Branch name: $(Build.SourceBranchName)"
              echo "Image Id - $(imageId)"
              docker tag tandym-dp-web:$(Build.BuildId) $(AcrUrl)/tandym-dp-web:$(Build.BuildId)
            displayName: Tag Docker Image
            condition: ne('${{ parameters.environment }}', 'ua')

          - script: |
              echo "Branch name: $(Build.SourceBranchName)"
              echo "Image Id - $(imageId)"
              docker tag tandym-dp-web:$(Build.BuildId) $(AcrUrl)/tandym-dp-web:$(Build.SourceBranchName)-$(Build.BuildId)
            displayName: Tag Docker Image
            condition: eq('${{ parameters.environment }}', 'ua')

          - script: |
              echo $(AcrPassword) | docker login $(AcrUrl) --username $(AcrUsername) --password-stdin
            displayName: Login to ACR

          - script: |
              docker push tg${{ parameters.environment }}${{ parameters.location }}envcr001.azurecr.io/tandym-dp-web:$(Build.SourceBranchName)-$(Build.BuildId)
            displayName: Push Docker Image
            condition: eq('${{ parameters.environment }}', 'ua')

          - script: |
              docker push tg${{ parameters.environment }}${{ parameters.location }}envcr001.azurecr.io/tandym-dp-web:$(Build.BuildId)
            displayName: Push Docker Image
            condition: ne('${{ parameters.environment }}', 'ua')

  - stage: Deploy
    displayName: Deploy Stage
    dependsOn: Build
    condition: and(succeeded(), eq(${{ parameters.Deploy }}, true))
    pool:
      name: tg-${{ parameters.environment }}-pool
    jobs:
      - job: Deploy
        displayName: Deploy ${{ parameters.environment }} DP WEB
        steps:
          - checkout: self
          - script: |
              # Update package lists
              sudo apt-get update
              sudo apt-get install -y azure-cli postgresql-client

              # Install dependencies
              sudo apt-get install -y software-properties-common
              sudo add-apt-repository ppa:deadsnakes/ppa
              sudo apt-get update

              # Install PostgreSQL client
              sudo apt-get install -y postgresql-client

              # Install Azure CLI
              curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash
            displayName: "Install Azure CLI and PostgreSQL CLI"
          - script: |
              echo "Checking and installing unzip if needed"
              if ! command -v unzip &> /dev/null; then
                echo "unzip not found, installing..."
                sudo apt-get update
                sudo apt-get install -y unzip
              else
                echo "unzip is already installed"
              fi
            displayName: Check and Install unzip

          # Fetch secrets from Key Vault
          - template: common/fetch_keyvault_secrets.yml
            parameters:
              armServiceConnection: ${{ variables.ServiceConnectionName }}
              keyVaultName: ${{ variables.KeyVaultName }}
              secretsToFetch: "AcrName,AcrUsername,AcrPassword,AcrUrl,Client-ID,Client-Secret,AzTenant-Id,Subscription-ID,recruiter-sso-client-id,recruiter-sso-client-secret,nextauth-secret"
              runAsPreJob: true

          # Update deployment YAML
          - bash: |
              echo "Loading Azure Identity"
              envsubst < $(System.DefaultWorkingDirectory)/tandym-dp-web/manifests/deployment.yml > temp_data_deployment && mv temp_data_deployment $(System.DefaultWorkingDirectory)/tandym-dp-web/manifests/deployment.yml
              cat $(System.DefaultWorkingDirectory)/tandym-dp-web/manifests/deployment.yml
              envsubst < $(System.DefaultWorkingDirectory)/tandym-dp-web/manifests/service.yml > temp_data_service && mv temp_data_service $(System.DefaultWorkingDirectory)/tandym-dp-web/manifests/service.yml
              cat $(System.DefaultWorkingDirectory)/tandym-dp-web/manifests/service.yml
              envsubst < $(System.DefaultWorkingDirectory)/tandym-dp-web/manifests/autoscale.yml > temp_data_deployment && mv temp_data_deployment $(System.DefaultWorkingDirectory)/tandym-dp-web/manifests/autoscale.yml
              cat $(System.DefaultWorkingDirectory)/tandym-dp-web/manifests/autoscale.yml
            displayName: Loading Azure Identity
            env:
              AZURE_CLIENT_ID: $(Client-ID)
              AZURE_CLIENT_SECRET: $(Client-Secret)
              AZURE_TENANT_ID: $(AzTenant-Id)
              AZURE_SUBSCRIPTION_ID: $(Subscription-ID)
              AppConfigurationEndpoint: ${{ variables.AppConfigurationEndpoint }}
              webcontainerimage: $(webcontainerimage)
              WEB_LB_IP: ${{ variables.WebLBIP }}
              HPA_NAME: tandym-dp-web
              NAMESPACE: tandym-dp
              MIN_REPLICAS: ${{ variables.WebMinReplicas }}
              MAX_REPLICAS: ${{ variables.WebMaxReplicas }}
              DEPLOYMENT_NAME: tandym-dp-web
              MEM_PERCENTAGE: ${{ variables.WebMemThreshold }}
              CPU_PERCENTAGE: ${{ variables.WebCpuThreshold }}
              NEXT_PUBLIC_BASE_URL: http://${{ variables.ServerLBIP }}
              RECRUITER_SSO_CLIENT_ID: $(recruiter-sso-client-id)
              RECRUITER_SSO_CLIENT_SECRET: $(recruiter-sso-client-secret)
              NEXTAUTH_SECRET: $(nextauth-secret)
              NEXTAUTH_URL: ${{ variables.NEXTAUTH_URL }}
              DP_PORTAL_SERVICE: http://${{ variables.PortalLBIP }}
              NEXT_PUBLIC_PARSE_RESUME_FROM_MERCURY: ${{ variables.NEXT_PUBLIC_PARSE_RESUME_FROM_MERCURY }}
              NEXT_PUBLIC_AD_LOGIN: ${{ variables.NEXT_PUBLIC_AD_LOGIN }}
              NEXT_PUBLIC_AUTH_URL: ${{ variables.NEXT_PUBLIC_AUTH_URL }}
              NEXT_PUBLIC_APPINSIGHTS_CONNECTION_STRING: ${{ variables.NEXT_PUBLIC_APPINSIGHTS_CONNECTION_STRING }}
              CRM_URL: ${{ variables.CRM_URL }}

          # Deploy to AKS
          - template: ./common/aks-deploy.yml
            parameters:
              location: ${{ parameters.location }}
              environment: ${{ parameters.environment }}
              namespace: tandym-dp
              imagePullSecrets: tandym-dp-pull-secret
              manifests: |
                $(System.DefaultWorkingDirectory)/tandym-dp-web/manifests/deployment.yml
                $(System.DefaultWorkingDirectory)/tandym-dp-web/manifests/service.yml
                $(System.DefaultWorkingDirectory)/tandym-dp-web/manifests/autoscale.yml
              containers: |
                $(AcrUrl)/tandym-dp-web:$(imageId)
              sharepointSecret: $(${{ parameters.environment }}-sharepoint)
