from fastapi import APIRouter
from dp_portal.services.entitlement_service import EntitlementService
from dp_portal.appLogger import AppLogger
from dp_portal.db.postgres_connector import PostgresConnector
from dp_portal.services.historical_service import HistoricalService

import json
class Routes:
    """Encapsulates FastAPI routes with proper dependency injection for the Attribute Management API."""

    def __init__(self, 
                 logger: AppLogger, 
                 postgress_db_connector: PostgresConnector,
                 entitlement_service: EntitlementService,
                 historical_service: HistoricalService):
        """Initialize the router and service dependencies."""
        self.logger = logger
        self.postgress_db_connector = postgress_db_connector
        self.entitlement_service = entitlement_service
        self.historical_service = historical_service
        self.router = APIRouter()
        self._register_routes()

    def _register_routes(self):
        """Register API routes inside the router."""
        @self.router.get("/", response_model=dict)
        async def root():
            return {"message": "Welcome to the Entitlement Management API"}
            
        @self.router.get("/api/entitlement", response_model=dict)
        async def get_entitlement(email_id: str, portal_name: str):
            entitlement_response = self.entitlement_service.get_entitlement(email_id, portal_name)
            print("ℹ️ Entitlement Response: " + json.dumps(entitlement_response))
            return entitlement_response
        
        @self.router.get("/api/add_historical_data", response_model=dict)
        async def add_historical_data(email_id: str, portal_name: str, feature: str):
            """Insert a historical log entry."""
            if portal_name == '':
                portal_name = None
            result = self.historical_service.insert_historical_log(email_id, portal_name, feature)
            if result["message"] == "Historical log inserted successfully":                   
                return {
                    "error": False,
                    "code": "TR_HL_01",
                    "message": "Successful",                                     
                    }
            else:
                return {
                    "error": True,
                    "code": "TR_HL_ERR",
                    "message": f"Error inserting historical log",
                }

            return result   

    def get_router(self):
        """Return the configured router."""
        return self.router
