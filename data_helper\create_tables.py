import json
import csv
import os
import psycopg2
from common.db.postgres_connector import PostgresConnector, PostgresEnvironment
from common.appLogger import AppLogger

class AttributeDatabaseService:
    """Handles table management and data insertion for the attributes database."""
    
    def __init__(self, logger: AppLogger, db_connector: PostgresConnector):
        """Initialize with logger and a database connector."""
        self.logger = logger
        self.db_connector = db_connector
        self.schema = self.db_connector.schema #tandymdp or tandymdpprod

    def drop_tables(self, drop_queries):
        try:
            self.logger.info("Using existing connection for dropping tables")
            conn = self.db_connector.connection
            if conn is None:
                self.logger.error("Database connection failed.")
                return
            
            cur = conn.cursor()
            for query in drop_queries:
                self.logger.info(f"Executing table drop query: {query.strip()}")
                cur.execute(query)
                conn.commit()
            
            self.logger.info("All tables dropped successfully in PostgreSQL.")
            cur.close()
        except Exception as e:
            self.logger.error(f"Error while dropping tables: {e}")

    def drop_tables_attributes(self):
        """Drops existing tables for the attributes database."""
        drop_queries = [
            f"DROP TABLE IF EXISTS {self.schema}.weights CASCADE;",
            f"DROP TABLE IF EXISTS {self.schema}.attributes CASCADE;",
            f"DROP TABLE IF EXISTS {self.schema}.attribute_types CASCADE;",
            f"DROP TABLE IF EXISTS {self.schema}.subcategories CASCADE;",
            f"DROP TABLE IF EXISTS {self.schema}.categories CASCADE;"
            f"DROP TABLE IF EXISTS {self.schema}.subcategory_weight_config CASCADE;"
        ]
        self.drop_tables(drop_queries)

    def drop_tables_candidates(self):
        """Drops the candidates table."""
        drop_queries = [
            f"DROP TABLE IF EXISTS {self.schema}.candidates CASCADE;"
        ]
        self.drop_tables(drop_queries)

    def drop_tables_vacancies(self):
        """Drops the vacancies table."""
        drop_queries = [
            f"DROP TABLE IF EXISTS {self.schema}.vacancies CASCADE;"
        ]
        self.drop_tables(drop_queries)

    def drop_tables_vacancy_candidates(self):
        """Drops the vacancy_candidates table."""
        drop_queries = [
            f"DROP TABLE IF EXISTS {self.schema}.vacancy_candidates CASCADE;"
        ]
        self.drop_tables(drop_queries)

    def drop_tables_candidates_processed(self):
        """Drops the processed candidate tables."""
        drop_queries = [
            f"DROP TABLE IF EXISTS {self.schema}.candidate_matches CASCADE;",
            f"DROP TABLE IF EXISTS {self.schema}.candidate_classifications CASCADE;",
            f"DROP TABLE IF EXISTS {self.schema}.candidate_subcategories CASCADE;",
            f"DROP TABLE IF EXISTS {self.schema}.candidate_skills_matches CASCADE;"
        ]
        self.drop_tables(drop_queries)

    def create_tables(self, queries):
        try:
            self.logger.info("Using existing connection for table creation")
            conn = self.db_connector.connection
            if conn is None:
                self.logger.error("Database connection failed.")
                return
            
            cur = conn.cursor()
            for query in queries:
                self.logger.info(f"Executing table creation query:\n{query.strip()}")
                cur.execute(query)
                conn.commit()
            
            self.logger.info("All tables created successfully in PostgreSQL.")
            cur.close()
        except Exception as e:
            self.logger.error(f"Error while creating tables: {e}")

    def create_tables_attributes(self):
        """Creates the required tables for the attributes database."""
        queries = [
            # Categories table
            f"""
            CREATE TABLE IF NOT EXISTS {self.schema}.categories (
                id SERIAL PRIMARY KEY,
                name TEXT NOT NULL UNIQUE
            );
            """,
            # Subcategories table
            f"""
            CREATE TABLE IF NOT EXISTS {self.schema}.subcategories (
                id SERIAL PRIMARY KEY,
                name TEXT NOT NULL UNIQUE,
                category_id INTEGER NOT NULL,
                FOREIGN KEY (category_id) REFERENCES {self.schema}.categories (id) ON DELETE CASCADE
            );
            """,
            # Attribute types table
            f"""
            CREATE TABLE IF NOT EXISTS {self.schema}.attribute_types (
                id SERIAL PRIMARY KEY,
                name TEXT NOT NULL UNIQUE
            );
            """,
            # Attributes table
            f"""
            CREATE TABLE IF NOT EXISTS {self.schema}.attributes (
                id SERIAL PRIMARY KEY,
                name TEXT NOT NULL,
                weight TEXT,
                subcategory_id INTEGER NOT NULL,
                attribute_type_id INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                deleted_at TIMESTAMP DEFAULT NULL,
                deleted_by TEXT DEFAULT NULL,
                is_deleted BOOLEAN DEFAULT FALSE,
                UNIQUE (subcategory_id, attribute_type_id, name),
                FOREIGN KEY (subcategory_id) REFERENCES {self.schema}.subcategories (id) ON DELETE CASCADE,
                FOREIGN KEY (attribute_type_id) REFERENCES {self.schema}.attribute_types (id) ON DELETE CASCADE
            );
            """,
            # Weights table
            f"""
            CREATE TABLE IF NOT EXISTS {self.schema}.weights (
                id SERIAL PRIMARY KEY,
                subcategory_id INTEGER NOT NULL,
                attribute_type_id INTEGER NOT NULL,
                weight_level TEXT NOT NULL,
                weight_value INTEGER NOT NULL,
                UNIQUE (subcategory_id, attribute_type_id, weight_level),
                FOREIGN KEY (subcategory_id) REFERENCES {self.schema}.subcategories (id) ON DELETE CASCADE,
                FOREIGN KEY (attribute_type_id) REFERENCES {self.schema}.attribute_types (id) ON DELETE CASCADE
            );
            """
        ]
        self.create_tables(queries)


    def create_tables_candidates(self):
        """Creates the candidates table to store raw candidate data."""
        queries = [
            f"""
            CREATE OR REPLACE FUNCTION {self.schema}.update_updated_at_column()
            RETURNS trigger
            LANGUAGE plpgsql
            AS $function$
            BEGIN
            NEW.updated_at = NOW();
            RETURN NEW;
            END;
            $function$
            ;
            """,
            f"""
            CREATE OR REPLACE FUNCTION {self.schema}.trigger_set_timestamp()
            RETURNS trigger
            LANGUAGE plpgsql
            AS $function$
            BEGIN
            NEW.updated_at = NOW();
            RETURN NEW;
            END;
            $function$
            ;
            """,
            f"""
            CREATE OR REPLACE FUNCTION {self.schema}.set_created_at_column()
            RETURNS trigger
            LANGUAGE plpgsql
            AS $function$
            BEGIN
                IF NEW.created_at IS NULL THEN
                    NEW.created_at = CURRENT_TIMESTAMP;
                END IF;
                RETURN NEW;
            END;
            $function$
            ;
            """,
            f"""
            CREATE TABLE {self.schema}.candidates (
                id serial4 NOT NULL,
                contact_id uuid NOT NULL,
                "name" varchar(255) NULL,
                email varchar(255) NULL,
                phone varchar(100) NULL,
                resume_path text NULL,
                university text NULL,
                job_titles _text NULL,
                "attributes" _text NULL,
                work_experience jsonb NULL,
                resume_data jsonb NOT NULL,
                created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
                university_json jsonb NULL,
                vacancies_shortlisted jsonb NULL,
                job_tuples jsonb NULL,
                total_experience_in_months numeric NULL,
                updated_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
                CONSTRAINT candidates_contact_id_key UNIQUE (contact_id),
                CONSTRAINT candidates_pkey PRIMARY KEY (id)
            );
            CREATE INDEX candidates_resume_city_state_idx ON {self.schema}.candidates USING btree (((resume_data ->> 'city'::text)), ((resume_data ->> 'state'::text)));
            CREATE INDEX idx_candidates_job_tuples ON {self.schema}.candidates USING gin (job_tuples);
            CREATE INDEX idx_candidates_resume_city ON {self.schema}.candidates USING btree (((resume_data ->> 'city'::text)));
            CREATE INDEX idx_candidates_resume_city_not_null ON {self.schema}.candidates USING btree (((resume_data ->> 'city'::text))) WHERE ((resume_data ->> 'city'::text) IS NOT NULL);
            CREATE INDEX idx_candidates_resume_city_state_both_not_null ON {self.schema}.candidates USING btree (((((resume_data ->> 'city'::text) IS NOT NULL) AND ((resume_data ->> 'state'::text) IS NOT NULL)))) WHERE (((resume_data ->> 'city'::text) IS NOT NULL) AND ((resume_data ->> 'state'::text) IS NOT NULL));
            CREATE INDEX idx_candidates_resume_data ON {self.schema}.candidates USING gin (resume_data);
            CREATE INDEX idx_candidates_resume_state ON {self.schema}.candidates USING btree (((resume_data ->> 'state'::text)));
            CREATE INDEX idx_candidates_resume_state_not_null ON {self.schema}.candidates USING btree (((resume_data ->> 'state'::text))) WHERE ((resume_data ->> 'state'::text) IS NOT NULL);
            CREATE INDEX idx_candidates_total_experience ON {self.schema}.candidates USING btree (total_experience_in_months);
            CREATE INDEX idx_candidates_university_json ON {self.schema}.candidates USING gin (university_json);
            CREATE INDEX idx_candidates_vacancies_shortlisted ON {self.schema}.candidates USING gin (vacancies_shortlisted);
            CREATE INDEX idx_candidates_work_experience ON {self.schema}.candidates USING gin (work_experience);
            CREATE INDEX idx_uuid_original ON {self.schema}.candidates USING btree (contact_id);

            -- Table Triggers
            create trigger update_candidates_updated_at before
            update
                on
                {self.schema}.candidates for each row execute function update_updated_at_column();
            """
        ]
        self.create_tables(queries)

    def create_tables_vacancies(self):
        """Creates the vacancies table to store raw vacancy data along with searchable fields."""
        queries = [
            f"""
            CREATE TABLE IF NOT EXISTS {self.schema}.vacancies (
                id SERIAL PRIMARY KEY,
                vacancy_id UUID NOT NULL UNIQUE,
                refno VARCHAR(100),
                job_description TEXT,
                vacancy_data JSONB NOT NULL,
                job_location JSONB,
                years_of_experience JSONB,
                work_arrangement VARCHAR(100),
                job_title JSONB,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            """
        ]
        self.create_tables(queries)

    def create_tables_vacancy_candidates(self):
        """
        Creates the vacancy_candidates table which maps a vacancy to candidate resume data.
        Each candidate is stored as a separate row with key columns extracted and the entire candidate
        mapping stored as a JSON object.
        """
        queries = [
            f"""
            CREATE TABLE IF NOT EXISTS {self.schema}.vacancy_candidates (
                id SERIAL PRIMARY KEY,
                vacancy_refno VARCHAR(100),
                vacancy_id UUID NOT NULL,
                candidate_contactid UUID,
                candidate_email VARCHAR(255),
                candidate_data JSONB NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (vacancy_id) REFERENCES {self.schema}.vacancies(vacancy_id) ON DELETE CASCADE
            );
            """
        ]
        self.create_tables(queries)


    def create_tables_candidates_processed(self):
        """Creates the processed candidate tables for storing matches, classifications, subcategories, and skills matches."""
        queries = [
            # Processed Matches Table
            f"""
            CREATE TABLE IF NOT EXISTS {self.schema}.candidate_matches (
                id SERIAL PRIMARY KEY,
                candidate_id INTEGER NOT NULL,
                contact_id UUID NOT NULL,
                degree_matches JSONB,
                job_matches JSONB,
                FOREIGN KEY (candidate_id) REFERENCES {self.schema}.candidates (id) ON DELETE CASCADE,
                UNIQUE (candidate_id, contact_id)
            );
            """,
            # candidate Classifications Table
            f"""
            CREATE TABLE IF NOT EXISTS {self.schema}.candidate_classifications (
                id SERIAL PRIMARY KEY,
                candidate_id INTEGER NOT NULL,
                contact_id UUID NOT NULL,
                criterion TEXT NOT NULL,
                subcategory_id INTEGER NOT NULL,
                FOREIGN KEY (candidate_id) REFERENCES {self.schema}.candidates (id) ON DELETE CASCADE,
                FOREIGN KEY (subcategory_id) REFERENCES {self.schema}.subcategories (id) ON DELETE CASCADE,
                UNIQUE (candidate_id, contact_id, criterion, subcategory_id)
            );
            """,
            # candidate Subcategories Table
            f"""
            CREATE TABLE IF NOT EXISTS {self.schema}.candidate_subcategories (
                id SERIAL PRIMARY KEY,
                candidate_id INTEGER NOT NULL,
                contact_id UUID NOT NULL,
                subcategory_id INTEGER NOT NULL,
                score TEXT,
                FOREIGN KEY (candidate_id) REFERENCES {self.schema}.candidates (id) ON DELETE CASCADE,
                FOREIGN KEY (subcategory_id) REFERENCES {self.schema}.subcategories (id) ON DELETE CASCADE,
                UNIQUE (candidate_id, contact_id, subcategory_id)
            );
            """,
            # candidate Skills Matches Table
            f"""
            CREATE TABLE IF NOT EXISTS {self.schema}.candidate_skills_matches (
                id SERIAL PRIMARY KEY,
                candidate_id INTEGER NOT NULL,
                contact_id UUID NOT NULL,
                subcategory_id INTEGER NOT NULL,
                soft_skills_high INTEGER,
                soft_skills_normal INTEGER,
                technical_skills_high INTEGER,
                technical_skills_normal INTEGER,
                tools_platforms_high INTEGER,
                tools_platforms_normal INTEGER,
                title_score NUMERIC,
                degree_score NUMERIC,
                FOREIGN KEY (candidate_id) REFERENCES {self.schema}.candidates (id) ON DELETE CASCADE,
                FOREIGN KEY (subcategory_id) REFERENCES {self.schema}.subcategories (id) ON DELETE CASCADE,
                UNIQUE (candidate_id, contact_id, subcategory_id)
            );
            """
        ]
        self.create_tables(queries)

    def modify_attributes_table_v1(self):
        conn = self.db_connector.connection
        if conn is None:
            self.logger.error("Database connection failed.")
            return []
        
        cur = conn.cursor()
        query = f"""
        ALTER TABLE {self.schema}.attributes
            ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP DEFAULT NULL,
            ADD COLUMN IF NOT EXISTS deleted_by TEXT DEFAULT NULL,
            ADD COLUMN IF NOT EXISTS is_deleted BOOLEAN DEFAULT FALSE,
            ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT NULL,
            ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT NULL;
        """
        cur.execute(query)
        conn.commit()
        cur.close()
        
    def modify_attributes_table_v2(self):
        conn = self.db_connector.connection
        if conn is None:
            self.logger.error("Database connection failed.")
            return []
        
        cur = conn.cursor()
        query = f"""
        ALTER TABLE {self.schema}.attributes
            ADD COLUMN IF NOT EXISTS is_approved BOOLEAN DEFAULT FALSE,
            ADD COLUMN IF NOT EXISTS approved_by TEXT DEFAULT NULL;
        """
        cur.execute(query)
        conn.commit()
        cur.close()
        
    def modify_candidates_table_v1(self):
        conn = self.db_connector.connection
        if conn is None:
            self.logger.error("Database connection failed.")
            return []
        
        cur = conn.cursor()
        query = f"""
        ALTER TABLE {self.schema}.candidates
            ADD COLUMN IF NOT EXISTS industry_id INTEGER,
            ADD CONSTRAINT candidates_industry_id_fkey FOREIGN KEY (industry_id) 
                REFERENCES {self.schema}.industries(id);
        """
        cur.execute(query)
        conn.commit()
        cur.close()

    # --- Existing methods for attributes, job titles, weights, etc. ---
    def insert_categories_and_subcategories_postgres(self, csv_file: str):
        """
        Reads a CSV file and inserts unique categories and subcategories into the database.
        Skips the header row (if it contains "sub category" or "category") and ensures that each
        category and its associated subcategories are inserted only once so that the IDs are sequential.
        """
        try:
            self.logger.info(f"Starting insert for categories and subcategories from CSV: {csv_file}")
            conn = self.db_connector.connection
            if conn is None:
                self.logger.error("Database connection failed.")
                return

            # Read the CSV file and collect unique categories and subcategories.
            categories_dict = {}  # key: category, value: set of subcategories
            with open(csv_file, mode='r') as file:
                csv_reader = csv.reader(file)
                header = next(csv_reader, None)
                if header and (header[0].strip().lower() == "sub category" or header[1].strip().lower() == "category"):
                    self.logger.info(f"Skipping header row: {header}")
                else:
                    if header and len(header) == 2:
                        subcat, cat = header[0].strip(), header[1].strip()
                        categories_dict.setdefault(cat, set()).add(subcat)
                for row in csv_reader:
                    if len(row) != 2:
                        continue
                    subcategory, category = row[0].strip(), row[1].strip()
                    if subcategory.lower() == "sub category" or category.lower() == "category":
                        continue
                    categories_dict.setdefault(category, set()).add(subcategory)

            cur = conn.cursor()
            category_id_dict = {}
            for category in sorted(categories_dict.keys()):
                cur.execute(f"""
                    INSERT INTO {self.schema}.categories (name)
                    VALUES (%s)
                    RETURNING id;
                """, (category,))
                result = cur.fetchone()
                if result:
                    category_id = result[0]
                    category_id_dict[category] = category_id
                    self.logger.info(f"Inserted category '{category}' with ID {category_id}")
                else:
                    cur.execute(f"SELECT id FROM {self.schema}.categories WHERE name = %s", (category,))
                    row = cur.fetchone()
                    if row:
                        category_id_dict[category] = row[0]
            
            for category, subcat_set in categories_dict.items():
                cat_id = category_id_dict.get(category)
                if not cat_id:
                    self.logger.warning(f"Category '{category}' was not inserted; skipping its subcategories.")
                    continue
                for subcategory in sorted(subcat_set):
                    cur.execute(f"""
                        INSERT INTO {self.schema}.subcategories (name, category_id)
                        VALUES (%s, %s)
                        RETURNING id;
                    """, (subcategory, cat_id))
                    result = cur.fetchone()
                    if result:
                        self.logger.info(f"Inserted subcategory '{subcategory}' for category '{category}'")
                    else:
                        self.logger.warning(f"Subcategory '{subcategory}' for category '{category}' may already exist.")
            
            conn.commit()
            self.logger.info("Categories and subcategories inserted successfully.")
            cur.close()
        except Exception as e:
            self.logger.error(f"Error inserting categories and subcategories: {e}")

    def insert_attributes_postgres(self, json_folder: str):
        """
        Inserts attributes from JSON files into the database and prints a summary of insertions.
        The summary output is like:
            inserted 10 job titles for Anesthesiology
            inserted 52 soft skills for Accounting
        """
        try:
            self.logger.info(f"Starting insert for attributes")
            conn = self.db_connector.connection
            if conn is None:
                self.logger.error(f"Database connection failed.")
                return
            
            cur = conn.cursor()
            for filename in os.listdir(json_folder):
                if filename.startswith("skills_") and filename.endswith(".json"):
                    # Extract subcategory name from filename
                    subcategory_name = filename[len("skills_"):-len(".json")].strip()
                    
                    cur.execute(f"SELECT id FROM {self.schema}.subcategories WHERE name = %s", (subcategory_name,))
                    result = cur.fetchone()
                    if not result:
                        self.logger.warning(f"Subcategory '{subcategory_name}' not found. Skipping.")
                        continue
                    subcategory_id = result[0]
                    
                    filepath = os.path.join(json_folder, filename)
                    with open(filepath, 'r') as file:
                        data = json.load(file)
                        for attribute_type, attributes in data.items():
                            self.logger.info(f"Processing attribute type '{attribute_type}' for subcategory '{subcategory_name}'")
                            cur.execute(f"SELECT id FROM {self.schema}.attribute_types WHERE name = %s", (attribute_type,))
                            result = cur.fetchone()
                            if not result:
                                self.logger.warning(f"Attribute type '{attribute_type}' not found. Skipping.")
                                continue
                            attribute_type_id = result[0]
                            
                            inserted_count = 0
                            for attribute in attributes:
                                cur.execute(f"""
                                    INSERT INTO {self.schema}.attributes (
                                        subcategory_id, attribute_type_id, name, weight, 
                                        deleted_at, deleted_by, is_deleted
                                    ) VALUES (%s, %s, %s, %s, %s, %s, %s)
                                    ON CONFLICT (subcategory_id, attribute_type_id, name) DO NOTHING;
                                """, (
                                    subcategory_id, attribute_type_id, 
                                    attribute['name'].strip(), attribute['weight'].strip(),
                                    None,  # deleted_at is NULL initially
                                    None,  # deleted_by is NULL initially
                                    False  # is_deleted is False initially
                                ))
                                if cur.rowcount > 0:
                                    inserted_count += 1
                            if inserted_count > 0:
                                self.logger.info(f"Inserted {inserted_count} {attribute_type} for {subcategory_name}")
            
            conn.commit()
            self.logger.info(f"Attributes inserted successfully.")
            cur.close()
        except Exception as e:
            self.logger.error(f"Error inserting attributes: {e}")

    def insert_job_titles_postgres(self, job_titles_folder: str):
        """
        Inserts job titles from JSON files into the database.
        For each file matching the pattern "jobtitles_*.json", this method:
          - Extracts the subcategory name from the filename.
          - Looks up the corresponding subcategory ID.
          - Inserts each job title (with a default weight of 'normal') into {self.schema}.attributes,
            using the attribute_type_id for "job titles".
        Logs a summary message for each subcategory processed.
        """
        try:
            self.logger.info(f"Starting insert for job titles")
            conn = self.db_connector.connection
            if conn is None:
                self.logger.error(f"Database connection failed.")
                return
            
            cur = conn.cursor()
            # Retrieve attribute_type_id for 'job titles'
            cur.execute(f"SELECT id FROM {self.schema}.attribute_types WHERE name = %s", ("job titles",))
            result = cur.fetchone()
            if not result:
                self.logger.error(f"Attribute type 'job titles' not found in attribute_types table.")
                cur.close()
                return
            job_titles_attribute_type_id = result[0]
            
            for filename in os.listdir(job_titles_folder):
                if filename.startswith("jobtitles_") and filename.endswith(".json"):
                    # Extract subcategory name from the filename
                    subcategory_name = filename[len("jobtitles_"):-len(".json")].strip()
                    
                    # Get subcategory ID
                    cur.execute(f"SELECT id FROM {self.schema}.subcategories WHERE name = %s", (subcategory_name,))
                    result = cur.fetchone()
                    if not result:
                        self.logger.warning(f"Subcategory '{subcategory_name}' not found in the database. Skipping.")
                        continue
                    subcategory_id = result[0]
                    
                    filepath = os.path.join(job_titles_folder, filename)
                    with open(filepath, 'r') as file:
                        job_titles = json.load(file)
                        inserted_count = 0
                        for job_title in job_titles:
                            try:
                                cur.execute(f"""
                                    INSERT INTO {self.schema}.attributes (subcategory_id, attribute_type_id, name, weight)
                                    VALUES (%s, %s, %s, %s)
                                    ON CONFLICT DO NOTHING;
                                """, (subcategory_id, job_titles_attribute_type_id, job_title.strip(), 'normal'))
                                if cur.rowcount > 0:
                                    inserted_count += 1
                            except Exception as e:
                                self.logger.warning(f"Failed to insert job title '{job_title}': {e}")
                        if inserted_count > 0:
                            self.logger.info(f"Inserted {inserted_count} job titles for {subcategory_name}")
            
            conn.commit()
            self.logger.info("Job titles inserted successfully.")
            cur.close()
        except Exception as e:
            self.logger.error(f"Error inserting job titles: {e}")

    def insert_weights_postgres(self, weights_file: str):
        """Inserts weight mappings from a JSON file into the database."""
        try:
            self.logger.info(f"Starting insert for weights")
            conn = self.db_connector.connection
            if conn is None:
                self.logger.error(f"Database connection failed.")
                return
            
            cur = conn.cursor()
            with open(weights_file, 'r') as file:
                weights_data = json.load(file)
                if isinstance(weights_data, list):
                    for mapping in weights_data:
                        subcategory = mapping.get("Sub Category")
                        if not subcategory:
                            self.logger.warning(f"No subcategory found in mapping. Skipping entry.")
                            continue
                        
                        cur.execute(f"SELECT id FROM {self.schema}.subcategories WHERE name = %s", (subcategory,))
                        result = cur.fetchone()
                        if not result:
                            self.logger.warning(f"Subcategory '{subcategory}' not found. Skipping.")
                            continue
                        subcategory_id = result[0]
                        
                        for attribute_type, levels in mapping.items():
                            if attribute_type == "Sub Category":
                                continue
                            
                            cur.execute(f"SELECT id FROM {self.schema}.attribute_types WHERE LOWER(name) = %s", 
                                        (attribute_type.strip().lower(),))
                            result = cur.fetchone()
                            if not result:
                                self.logger.warning(f"Attribute type '{attribute_type}' not found. Skipping.")
                                continue
                            attribute_type_id = result[0]
                            
                            for level, value in levels.items():
                                cur.execute(f"""
                                    INSERT INTO {self.schema}.weights (subcategory_id, attribute_type_id, weight_level, weight_value)
                                    VALUES (%s, %s, %s, %s)
                                    ON CONFLICT DO NOTHING;
                                """, (subcategory_id, attribute_type_id, level, value))
            
            conn.commit()
            self.logger.info(f"Weights inserted successfully.")
            cur.close()
        except Exception as e:
            self.logger.error(f"Error inserting weights: {e}")

    def get_subcategories_for_category_postgres(self, category_name: str):
        try:
            self.logger.info(f"Fetching subcategories for category: {category_name}")
            conn = self.db_connector.connection
            if conn is None:
                self.logger.error("Database connection failed.")
                return []
            
            cur = conn.cursor()
            query = f"""
                SELECT {self.schema}.subcategories.name
                FROM {self.schema}.subcategories
                JOIN {self.schema}.categories ON {self.schema}.subcategories.category_id = {self.schema}.categories.id
                WHERE {self.schema}.categories.name = %s;
            """
            cur.execute(query, (category_name,))
            subcategories = [row[0] for row in cur.fetchall()]
            cur.close()
            return subcategories
        except Exception as e:
            self.logger.error(f"Error fetching subcategories: {e}")
            return []

    def print_all_categories_and_subcategories_postgres(self):
        try:
            self.logger.info("Fetching all categories and their subcategories")
            conn = self.db_connector.connection
            if conn is None:
                self.logger.error("Database connection failed.")
                return
            
            cur = conn.cursor()
            cur.execute(f"SELECT name FROM {self.schema}.categories;")
            categories = [row[0] for row in cur.fetchall()]
            for category in categories:
                subcategories = self.get_subcategories_for_category_postgres(category)
                self.logger.info(f"Category: {category}, Subcategories: {', '.join(subcategories) if subcategories else 'None'}")
            cur.close()
        except Exception as e:
            self.logger.error(f"Error printing categories and subcategories: {e}")

    def print_categories_postgres(self):
        try:
            self.logger.info("Printing all categories and their subcategories")
            self.print_all_categories_and_subcategories_postgres()
        except Exception as e:
            self.logger.error(f"Error printing categories: {e}")

    def populate_attribute_types_postgres(self):
        attribute_types = [
            (1, 'soft skills'),
            (2, 'technical skills'),
            (3, 'tools and platforms'),
            (4, 'degrees and certifications'),
            (5, 'job titles')
        ]
        
        try:
            self.logger.info("Populating attribute types table")
            conn = self.db_connector.connection
            if conn is None:
                self.logger.error("Database connection failed.")
                return
            
            cur = conn.cursor()
            cur.executemany(
                f"INSERT INTO {self.schema}.attribute_types (id, name) VALUES (%s, %s) ON CONFLICT DO NOTHING",
                attribute_types
            )
            conn.commit()
            self.logger.info("Attribute types populated successfully.")
            cur.close()
        except Exception as e:
            self.logger.error(f"Error populating attribute types: {e}")
    
    def create_tables_subcategory_weight_config(self):
        """Creates the subcategory_weight_config table to store weight scores for subcategories."""
        queries = [
            f"""
            CREATE TABLE IF NOT EXISTS {self.schema}.subcategory_weight_config (
                id SERIAL PRIMARY KEY,
                subcategory_id INTEGER NOT NULL,
                total_score_weight NUMERIC DEFAULT 1,
                job_title_score_weight NUMERIC DEFAULT 0,
                soft_skills_score_weight NUMERIC DEFAULT 0,
                technical_skills_score_weight NUMERIC DEFAULT 0,
                tools_and_platforms_score_weight NUMERIC DEFAULT 0,
                degrees_certs_score_weight NUMERIC DEFAULT 0,
                industry_experience_score_weight NUMERIC DEFAULT 0,
                job_title_recency_score_weight NUMERIC DEFAULT 0,
                experience_score_weight NUMERIC DEFAULT 0,
                industry_recency_score_weight NUMERIC DEFAULT 0,
                params JSONB DEFAULT '{}'::jsonb,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_by TEXT DEFAULT NULL,
                is_deleted BOOLEAN DEFAULT FALSE
            );
            """
        ]
        self.create_tables(queries)
        
    def insert_subcategory_weight_configs(self):
        """Inserts default weight scores for each unique subcategory into subcategory_weight_config."""
        try:
            self.logger.info("Starting insertion into subcategory_weight_config.")
            conn = self.db_connector.connection
            if conn is None:
                self.logger.error("Database connection failed.")
                return
            
            cur = conn.cursor()

            # Fetch all subcategory ids
            cur.execute(f"SELECT id FROM {self.schema}.subcategories;")
            subcategory_ids = [row[0] for row in cur.fetchall()]

            for subcat_id in subcategory_ids:
                cur.execute(f"""
                    INSERT INTO {self.schema}.subcategory_weight_config (
                        subcategory_id
                    )
                    SELECT %s
                    WHERE NOT EXISTS (
                        SELECT 1 FROM {self.schema}.subcategory_weight_config WHERE subcategory_id = %s
                    );
                """, (subcat_id, subcat_id))

            conn.commit()
            cur.close()
            self.logger.info("Inserted subcategory weight scores successfully.")

        except Exception as e:
            self.logger.error(f"Error inserting subcategory weight scores: {e}")



    def create_tables_company_industries(self):
        """Create tables for company industries."""
        try:
            # Create industries table
            self.cursor.execute(f"""
                CREATE TABLE IF NOT EXISTS {self.schema}.industries (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(255) NOT NULL UNIQUE,
                    naics_code VARCHAR(6)
                );
            """)
            
            # Create companies table with exact structure and constraints
            self.cursor.execute(f"""
                CREATE TABLE IF NOT EXISTS {self.schema}.companies (
                    id SERIAL PRIMARY KEY,
                    original_name VARCHAR(255) NOT NULL,
                    normalized_name VARCHAR(255) NOT NULL,
                    industry_id INTEGER,
                    CONSTRAINT companies_original_name_key UNIQUE (original_name),
                    CONSTRAINT companies_pkey PRIMARY KEY (id),
                    CONSTRAINT companies_industry_id_fkey FOREIGN KEY (industry_id) 
                        REFERENCES {self.schema}.industries(id)
                );
            """)
            
            self.connection.commit()
            self.logger.info("Created company industries tables successfully")
        except Exception as e:
            self.connection.rollback()
            self.logger.error(f"Error creating company industries tables: {str(e)}")
            raise

    def drop_tables_company_industries(self):
        """Drops the company industries tables."""
        drop_queries = [
            f"DROP TABLE IF EXISTS {self.schema}.companies;",
            f"DROP TABLE IF EXISTS {self.schema}.industries;"
        ]
        self.drop_tables(drop_queries)

def database_changes_v0(postgres_env=PostgresEnvironment.DEV):
    # Configure the logger
    logger_config = {
        "level": "DEBUG",
        "log_to_stdout": True
    }
    logger = AppLogger(logger_config)
    
    # Create and connect the PostgresConnector (using DEV environment as an example)
    postgres_connector = PostgresConnector(env=postgres_env, logger=logger)
    if not postgres_connector.connect():
        logger.error("Failed to connect to the database. Exiting.")
        return

    # Instantiate the service and run the database setup
    db_service = AttributeDatabaseService(logger, postgres_connector)
    db_service.logger.info("Starting database setup and data population.")

    csv_file = "data/Subcategory - Category.csv"
    json_folder = "data/skills_w_weights"
    weights_file = "data/gendata/sub_category_weights.json"
    job_titles_folder = "data/jobtitles"
    
    db_service.drop_tables_attributes()
    db_service.create_tables_attributes()

    db_service.populate_attribute_types_postgres()
    db_service.insert_categories_and_subcategories_postgres(csv_file)
    # db_service.print_categories_postgres()  # Uncomment to log categories for debugging

    db_service.insert_attributes_postgres(json_folder)
    db_service.insert_weights_postgres(weights_file)
    db_service.insert_job_titles_postgres(job_titles_folder)

    db_service.logger.info("Database setup and data population completed successfully.")

def database_changes_v1(postgres_env=PostgresEnvironment.DEV):
    logger_config = {
        "level": "DEBUG",
        "log_to_stdout": True
    }
    logger = AppLogger(logger_config)
    
    # Create and connect the PostgresConnector (using DEV environment as an example)
    postgres_connector = PostgresConnector(env=postgres_env, logger=logger)
    if not postgres_connector.connect():
        logger.error("Failed to connect to the database. Exiting.")
        return

    # Instantiate the service and run the database setup
    db_service = AttributeDatabaseService(logger, postgres_connector)
    db_service.logger.info("Starting database setup and data population.")

    #db_service.drop_tables_candidates()
    db_service.create_tables_candidates()
    db_service.modify_candidates_table_v1()
    #db_service.drop_tables_candidates_processed()
    db_service.create_tables_candidates_processed()
    
def database_changes_v2(postgres_env=PostgresEnvironment.DEV):
    logger_config = {
        "level": "DEBUG",
        "log_to_stdout": True
    }
    logger = AppLogger(logger_config)
    
    # Create and connect the PostgresConnector (using DEV environment as an example)
    postgres_connector = PostgresConnector(env=postgres_env, logger=logger)
    if not postgres_connector.connect():
        logger.error("Failed to connect to the database. Exiting.")
        return

    # Instantiate the service and run the database setup
    db_service = AttributeDatabaseService(logger, postgres_connector)
    db_service.logger.info("Starting database setup and data population.")

    db_service.drop_tables_vacancies()
    db_service.create_tables_vacancies()

def database_changes_v3(postgres_env=PostgresEnvironment.DEV):
    logger_config = {
        "level": "DEBUG",
        "log_to_stdout": True
    }
    logger = AppLogger(logger_config)
    
    # Create and connect the PostgresConnector (using DEV environment as an example)
    postgres_connector = PostgresConnector(env=postgres_env, logger=logger)
    if not postgres_connector.connect():
        logger.error("Failed to connect to the database. Exiting.")
        return

    # Instantiate the service and run the database setup
    db_service = AttributeDatabaseService(logger, postgres_connector)
    db_service.logger.info("Starting database setup and data population.")

    db_service.drop_tables_vacancy_candidates()
    db_service.create_tables_vacancy_candidates()
    
def database_changes_v4(postgres_env=PostgresEnvironment.DEV):
    logger_config = {
        "level": "DEBUG",
        "log_to_stdout": True
    }
    logger = AppLogger(logger_config)
    
    # Create and connect the PostgresConnector (using DEV environment as an example)
    postgres_connector = PostgresConnector(env=postgres_env, logger=logger)
    if not postgres_connector.connect():
        logger.error("Failed to connect to the database. Exiting.")
        return
    # Instantiate the service and run the database setup
    db_service = AttributeDatabaseService(logger, postgres_connector)
    db_service.modify_attributes_table_v2()

def database_changes_v5(postgres_env=PostgresEnvironment.DEV_VECTOR):
    """Creates tables for job title and candidate embeddings."""
    logger_config = {
        "level": "DEBUG",
        "log_to_stdout": True
    }
    logger = AppLogger(logger_config)
    
    # Create and connect the PostgresConnector
    postgres_connector = PostgresConnector(env=postgres_env, logger=logger)
    if not postgres_connector.connect():
        logger.error("Failed to connect to the database. Exiting.")
        return

    # Instantiate the service
    db_service = AttributeDatabaseService(logger, postgres_connector)
    db_service.logger.info("Starting database setup for embedding tables.")

    queries = [
        # Historic Candidate for creating candidate pools based on sub categories <->  Job Title Embedding Table
        f"""
        CREATE TABLE IF NOT EXISTS {db_service.schema}.historic_candidate_job_title_embedding (
            contact_id UUID NOT NULL,
            job_title TEXT NOT NULL,
            job_title_embedding vector(1536),
            PRIMARY KEY (contact_id, job_title)
        );

        CREATE INDEX IF NOT EXISTS idx_historic_candidate_job_title_embedding_title ON {db_service.schema}.historic_candidate_job_title_embedding (job_title);
        --CREATE INDEX IF NOT EXISTS idx_historic_candidate_job_title_embedding ON {db_service.schema}.historic_candidate_job_title_embedding USING hnsw (job_title_embedding vector_cosine_ops);
        """,

        # Candidate  <->  Job Title Embedding Table
        f"""
        CREATE TABLE IF NOT EXISTS {db_service.schema}.candidate_job_title_embedding (
            contact_id UUID NOT NULL,
            job_title TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            job_title_embedding vector(1536),
            experience_index NUMERIC,
            PRIMARY KEY (contact_id, job_title)
        );

        CREATE INDEX IF NOT EXISTS idx_candidate_job_title_embedding_title ON {db_service.schema}.candidate_job_title_embedding (job_title);
        CREATE INDEX IF NOT EXISTS idx_candidate_job_title_embedding ON {db_service.schema}.candidate_job_title_embedding USING hnsw (job_title_embedding vector_cosine_ops);
        """,
        
        # Candidate  <-> Tech Skill Embedding Table
        f"""
        CREATE TABLE IF NOT EXISTS {db_service.schema}.candidate_tech_skill_embedding (
            contact_id UUID NOT NULL,
            tech_skill TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            tech_skill_embedding vector(1536),
            PRIMARY KEY (contact_id, tech_skill)
        );

        CREATE INDEX IF NOT EXISTS idx_candidate_tech_skill_name ON {db_service.schema}.candidate_tech_skill_embedding (tech_skill);
        CREATE INDEX IF NOT EXISTS idx_candidate_tech_skill_embedding ON {db_service.schema}.candidate_tech_skill_embedding USING hnsw (tech_skill_embedding vector_cosine_ops);
        """,

        # Candidate  <-> Soft Skill Embedding Table
        f"""
        CREATE TABLE IF NOT EXISTS {db_service.schema}.candidate_soft_skill_embedding (
            contact_id UUID NOT NULL,
            soft_skill TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            soft_skill_embedding vector(1536),
            PRIMARY KEY (contact_id, soft_skill)
        );

        CREATE INDEX IF NOT EXISTS idx_candidate_soft_skill_name ON {db_service.schema}.candidate_soft_skill_embedding (soft_skill);
        CREATE INDEX IF NOT EXISTS idx_candidate_soft_skill_embedding ON {db_service.schema}.candidate_soft_skill_embedding USING hnsw (soft_skill_embedding vector_cosine_ops);
        """,

        # Candidate  <-> Degree and Certification Embedding Table
        f"""
        CREATE TABLE IF NOT EXISTS {db_service.schema}.candidate_degree_certification_embedding (
            contact_id UUID NOT NULL,
            degree_certification TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            degree_certification_embedding vector(1536),
            PRIMARY KEY (contact_id, degree_certification)
        );

        CREATE INDEX IF NOT EXISTS idx_candidate_degree_certification_name ON {db_service.schema}.candidate_degree_certification_embedding (degree_certification);
        CREATE INDEX IF NOT EXISTS idx_candidate_degree_certification_embedding ON {db_service.schema}.candidate_degree_certification_embedding USING hnsw (degree_certification_embedding vector_cosine_ops);
        """,

        # Candidate <-> Industry Embedding Table
        f"""
        CREATE TABLE IF NOT EXISTS {db_service.schema}.candidate_industry_embedding (
            contact_id UUID NOT NULL,
            industry TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            industry_embedding vector(1536),
            experience_index NUMERIC,
            PRIMARY KEY (contact_id, industry)
        );

        CREATE INDEX IF NOT EXISTS idx_candidate_industry_name ON {db_service.schema}.candidate_industry_embedding (industry);
        CREATE INDEX IF NOT EXISTS idx_candidate_industry_embedding ON {db_service.schema}.candidate_industry_embedding USING hnsw (industry_embedding vector_cosine_ops);
        """,

        # Candidate <-> Tool And Platform Embedding Table
        f"""
        CREATE TABLE IF NOT EXISTS {db_service.schema}.candidate_tool_and_platform_embedding (
            contact_id UUID NOT NULL,
            tool_and_platform TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            tool_and_platform_embedding vector(1536),
            PRIMARY KEY (contact_id, tool_and_platform)
        );

        CREATE INDEX IF NOT EXISTS idx_candidate_tool_and_platform_name ON {db_service.schema}.candidate_tool_and_platform_embedding (tool_and_platform);
        CREATE INDEX IF NOT EXISTS idx_candidate_tool_and_platform_embedding ON {db_service.schema}.candidate_tool_and_platform_embedding USING hnsw (tool_and_platform_embedding vector_cosine_ops);
        """,

        # Candidate <-> Degree Cert Complete Embedding Table
        f"""
        CREATE TABLE IF NOT EXISTS {db_service.schema}.candidate_degree_certification_complete_object_embedding (
            contact_id UUID NOT NULL,
            tool_and_platform TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            degree_certification_embedding vector(1536),
            PRIMARY KEY (contact_id, tool_and_platform)
        );

        CREATE INDEX IF NOT EXISTS idx_candidate_degree_certification_complete_object ON {db_service.schema}.candidate_degree_certification_complete_object_embedding (degree_certification);
        CREATE INDEX IF NOT EXISTS idx_candidate_degree_certification_complete_object_embedding ON {db_service.schema}.candidate_degree_certification_complete_object_embedding USING hnsw (degree_certification_embedding vector_cosine_ops);
        """,

        # Candidate <-> Vacancy Shortlists Related Tables
        # this table captures the candidates that have been shortlisted for a vacancy which will be displayed in the recruiter portal. This table may be deprecated in the future.
        f"""
        CREATE TABLE {db_service.schema}.candidate_application_shortlists (
            application_id uuid DEFAULT gen_random_uuid() NOT NULL,
            vacancy_id uuid NOT NULL,
            contact_id uuid NOT NULL,
            category_raw_scores jsonb NULL,
            category_intermediate_scores jsonb NULL,
            total_raw_score numeric(10, 4) NULL,
            total_intermediate_score numeric(10, 4) NULL,
            match_evidence jsonb NULL,
            calculated_normalized_score numeric(10, 4) NULL,
            match_evidence_json_version numeric DEFAULT 0.1 NULL,
            category_raw_scores_json_version numeric DEFAULT 0.1 NULL,
            category_intermediate_scores_json_version numeric DEFAULT 0.1 NULL,
            created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
            updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
            archived bool DEFAULT false NULL,
            CONSTRAINT candidate_application_shortlists_pkey PRIMARY KEY (application_id),
            CONSTRAINT candidate_application_shortlists_vacancy_id_contact_id_key UNIQUE (vacancy_id, contact_id)
        );
        CREATE INDEX idx_cand_app_archived ON {db_service.schema}.candidate_application_shortlists USING btree (archived);
        CREATE INDEX idx_cand_app_category_intermediate_scores ON {db_service.schema}.candidate_application_shortlists USING gin (category_intermediate_scores);
        CREATE INDEX idx_cand_app_category_raw_scores ON {db_service.schema}.candidate_application_shortlists USING gin (category_raw_scores);
        CREATE INDEX idx_cand_app_contact_id ON {db_service.schema}.candidate_application_shortlists USING btree (contact_id);
        CREATE INDEX idx_cand_app_match_evidence ON {db_service.schema}.candidate_application_shortlists USING gin (match_evidence);
        CREATE INDEX idx_cand_app_vacancy_id ON {db_service.schema}.candidate_application_shortlists USING btree (vacancy_id);

        -- Table Triggers

        create trigger set_timestamp_candidate_application_shortlists before
        update
            on
            {db_service.schema}.candidate_application_shortlists for each row execute function trigger_set_timestamp();
        create trigger set_updated_at_candidate_application_shortlists before
        update
            on
            {db_service.schema}.candidate_application_shortlists for each row execute function update_updated_at_column();
        """,
        
        # this table is used to capture the different configurations for the vacancy shortlists
        f"""
        CREATE TABLE IF NOT EXISTS {db_service.schema}.run_configurations_staging (
            config_hash text NOT NULL,
            cleaned_effective_config_json jsonb NOT NULL,
            first_seen_timestamp timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
            CONSTRAINT run_configurations_staging_pkey PRIMARY KEY (config_hash)
        );
        """,

        # this table is used to capture the different experiement runs for the vacancy shortlists
        f"""
        CREATE TABLE IF NOT EXISTS {db_service.schema}.experiment_runs_staging (
            run_id serial4 NOT NULL,
            run_tag text NOT NULL,
            config_hash text NOT NULL,
            run_timestamp timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
            CONSTRAINT experiment_runs_staging_pkey PRIMARY KEY (run_id),
            CONSTRAINT experiment_runs_staging_run_tag_config_hash_key UNIQUE (run_tag, config_hash)
        );

        ALTER TABLE {db_service.schema}.experiment_runs_staging ADD CONSTRAINT experiment_runs_staging_config_hash_fkey FOREIGN KEY (config_hash) REFERENCES {db_service.schema}.run_configurations_staging(config_hash);
        """,

        # This table captures the vacancys that need to be displayed to the recruiters for getting feedback from the recruiter portal
        f"""

        CREATE TABLE IF NOT EXISTS {db_service.schema}.vacancy_shortlist_processed (
            vacancy_id uuid NOT NULL,
            subcategory text NULL,
            refno text NULL,
            vacancy_data jsonb NULL,
            vacancy_data_json_version numeric DEFAULT 0.1 NULL,
            created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
            updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
            reviewer_config jsonb NULL,
            is_locked bool DEFAULT false NULL,
            archived bool DEFAULT false NULL,
            locked_by text NULL,
            run_id int4 NULL,
            current_search_result_generated_at timestamptz NULL,
	        search_result_data_last_updated_at timestamptz NULL,
            CONSTRAINT vacancy_shortlist_processed_pkey PRIMARY KEY (vacancy_id)
        );

        CREATE INDEX IF NOT EXISTS idx_vacancy_shortlist_processed_locked_by ON {db_service.schema}.vacancy_shortlist_processed USING btree (locked_by);
        CREATE INDEX IF NOT EXISTS idx_vacancy_shortlist_processed_reviewer_config ON {db_service.schema}.vacancy_shortlist_processed USING gin (reviewer_config);
        CREATE INDEX IF NOT EXISTS idx_vacancy_shortlist_processed_vacancy_data ON {db_service.schema}.vacancy_shortlist_processed USING gin (vacancy_data);
        CREATE INDEX IF NOT EXISTS idx_vacancy_shortlist_processed_archived ON {db_service.schema}.vacancy_shortlist_processed USING btree (archived);

        ALTER TABLE {db_service.schema}.vacancy_shortlist_processed ADD CONSTRAINT vacancy_shortlist_processed_experiment_runs_staging_fk FOREIGN KEY (run_id) REFERENCES {db_service.schema}.experiment_runs_staging(run_id) ON DELETE SET NULL;

        create trigger set_updated_at before
        update
            on
            {db_service.schema}.vacancy_shortlist_processed for each row execute function update_updated_at_column();
        """,

        # This table captures the vacancy details and its associated max possible score a candidate can get for a vacancy.
        f"""
        CREATE TABLE {db_service.schema}.vacancy_category_max_score_staging (
            setting_id uuid DEFAULT gen_random_uuid() NOT NULL,
            vacancy_id uuid NOT NULL,
            category_scores jsonb NULL,
            run_id int4 NULL,
            refno text NULL,
            subcategory text NULL,
            vacancy_data_used_for_run jsonb NULL,
            vacancy_data_json_version_for_run numeric NULL,
            max_score_for_vacancy numeric(10, 4) NOT NULL,
            created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
            updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
            vacancy_category_score_json_version numeric DEFAULT 0.1 NULL,
            promoted bool NULL,
            CONSTRAINT vacancy_category_max_score_staging_pkey PRIMARY KEY (setting_id),
            CONSTRAINT vacancy_category_max_score_staging_vacancy_id_run_id_key UNIQUE (run_id, vacancy_id)
        );
        CREATE INDEX idx_vacancy_category_max_score_staging_category_scores ON {db_service.schema}.vacancy_category_max_score_staging USING gin (category_scores);
        CREATE INDEX idx_vacancy_category_max_score_staging_max_score_for_vacancy ON {db_service.schema}.vacancy_category_max_score_staging USING btree (max_score_for_vacancy);
        CREATE INDEX idx_vacancy_category_max_score_staging_vacancy_data ON {db_service.schema}.vacancy_category_max_score_staging USING gin (vacancy_data_used_for_run);
        CREATE INDEX idx_vacancy_category_max_score_staging_vacancy_id ON {db_service.schema}.vacancy_category_max_score_staging USING btree (vacancy_id);

        -- Table Triggers

        create trigger set_timestamp_vacancy_category_max_score_staging before
        update
            on
            {db_service.schema}.vacancy_category_max_score_staging for each row execute function trigger_set_timestamp();
        create trigger set_updated_at_vacancy_category_max_score_staging before
        update
            on
            {db_service.schema}.vacancy_category_max_score_staging for each row execute function update_updated_at_column();

        -- Foreign Key Constraints
        ALTER TABLE {db_service.schema}.vacancy_category_max_score_staging ADD CONSTRAINT vacancy_category_max_score_staging_experiment_runs_staging_fk FOREIGN KEY (run_id) REFERENCES {db_service.schema}.experiment_runs_staging(run_id);
        """,

        # This table is similar to the above table but will be deprecated in the future. This table does not capture the run id associated with the vacancy. 
        f"""
        CREATE TABLE {db_service.schema}.vacancy_category_max_score (
            setting_id uuid DEFAULT gen_random_uuid() NOT NULL,
            vacancy_id uuid NOT NULL,
            category_scores jsonb NULL,
            max_score_for_vacancy numeric(10, 4) NOT NULL,
            created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
            updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
            vacancy_category_score_json_version numeric DEFAULT 0.1 NULL,
            run_id int4 NULL,
            CONSTRAINT vacancy_category_max_score_pkey PRIMARY KEY (setting_id),
            CONSTRAINT vacancy_category_max_score_vacancy_id_key UNIQUE (vacancy_id)
        );
        CREATE INDEX idx_vacancy_category_max_score_category_scores ON {db_service.schema}.vacancy_category_max_score USING gin (category_scores);
        CREATE INDEX idx_vacancy_category_max_score_max_score_for_vacancy ON {db_service.schema}.vacancy_category_max_score USING btree (max_score_for_vacancy);
        CREATE INDEX idx_vacancy_category_max_score_vacancy_id ON {db_service.schema}.vacancy_category_max_score USING btree (vacancy_id);

        -- Table Triggers
        ALTER TABLE {db_service.schema}.vacancy_category_max_score ADD CONSTRAINT vacancy_category_max_score_experiment_runs_staging_fk FOREIGN KEY (run_id) REFERENCES {db_service.schema}.experiment_runs_staging(run_id);
        """,

        # this table captures the candidates that are shortlisted for a vacancy along with the run id. This is the primary table containing the results of various experiments generated by the search match framework.
        f"""
        CREATE TABLE {db_service.schema}.candidate_application_shortlists_staging (
            application_id uuid DEFAULT gen_random_uuid() NOT NULL,
            vacancy_id uuid NOT NULL,
            contact_id uuid NOT NULL,
            category_raw_scores jsonb NULL,
            category_intermediate_scores jsonb NULL,
            total_raw_score numeric(10, 4) NULL,
            total_intermediate_score numeric(10, 4) NULL,
            match_evidence jsonb NULL,
            calculated_normalized_score numeric(10, 4) NULL,
            match_evidence_json_version numeric DEFAULT 0.1 NULL,
            category_raw_scores_json_version numeric DEFAULT 0.1 NULL,
            category_intermediate_scores_json_version numeric DEFAULT 0.1 NULL,
            created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
            updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
            run_id int4 NULL,
            "rank" int4 NULL,
            CONSTRAINT candidate_application_shortlists_staging_pkey PRIMARY KEY (application_id),
            CONSTRAINT candidate_application_shortlists_staging_vacancy_id_contact_id_ UNIQUE (vacancy_id, contact_id, run_id)
        );
        CREATE INDEX idx_cand_app_staging_category_intermediate_scores ON {db_service.schema}.candidate_application_shortlists_staging USING gin (category_intermediate_scores);
        CREATE INDEX idx_cand_app_staging_category_raw_scores ON {db_service.schema}.candidate_application_shortlists_staging USING gin (category_raw_scores);
        CREATE INDEX idx_cand_app_staging_contact_id ON {db_service.schema}.candidate_application_shortlists_staging USING btree (contact_id);
        CREATE INDEX idx_cand_app_staging_match_evidence ON {db_service.schema}.candidate_application_shortlists_staging USING gin (match_evidence);
        CREATE INDEX idx_cand_app_staging_vacancy_id ON {db_service.schema}.candidate_application_shortlists_staging USING btree (vacancy_id);

        -- Table Triggers

        create trigger set_timestamp_candidate_application_shortlists_staging before
        update
            on
            {db_service.schema}.candidate_application_shortlists_staging for each row execute function trigger_set_timestamp();
        create trigger set_updated_at_candidate_application_shortlists_staging before
        update
            on
            {db_service.schema}.candidate_application_shortlists_staging for each row execute function update_updated_at_column();

        ALTER TABLE {db_service.schema}.candidate_application_shortlists_staging ADD CONSTRAINT fk_run_id FOREIGN KEY (run_id) REFERENCES {db_service.schema}.experiment_runs_staging(run_id);
        """,

        # This table captures the feedbacks given by the recruiters for the candidates shortlisted for a vacancy (thumbs up or thumbs down along with the comment).
        f"""
        CREATE TABLE {db_service.schema}.candidate_application_shortlists_feedback (
            vacancy_id uuid NOT NULL,
            contact_id uuid NOT NULL,
            feedbacks jsonb NULL,
            feedbacks_json_version numeric DEFAULT 0.1 NULL,
            created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
            updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
            CONSTRAINT candidate_application_shortlists_feedback_vacancy_id_contact_id PRIMARY KEY (vacancy_id, contact_id)
        );
        CREATE INDEX idx_candidate_application_shortlists_feedback_contact_id ON {db_service.schema}.candidate_application_shortlists_feedback USING btree (contact_id);
        CREATE INDEX idx_candidate_application_shortlists_feedback_feedbacks ON {db_service.schema}.candidate_application_shortlists_feedback USING gin (feedbacks);
        CREATE INDEX idx_candidate_application_shortlists_feedback_vacancy_id ON {db_service.schema}.candidate_application_shortlists_feedback USING btree (vacancy_id);

        -- Table Triggers

        create trigger set_timestamp_candidate_application_shortlists_feedback before
        update
            on
            {db_service.schema}.candidate_application_shortlists_feedback for each row execute function trigger_set_timestamp();
        create trigger set_updated_at_candidate_application_shortlists_feedback before
        update
            on
            {db_service.schema}.candidate_application_shortlists_feedback for each row execute function update_updated_at_column();
        """,
        # This table captures the why fit reason and any other details associated with a candidate that has been shortlisted for a vacancy.
        f"""
        CREATE TABLE {db_service.schema}.candidate_application_shortlists_detail (
            vacancy_id uuid NOT NULL,
            contact_id uuid NOT NULL,
            fitness_reason jsonb NULL,
            fitness_reason_json_version numeric DEFAULT 0.1 NULL,
            created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
            updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
            info_bot_response text NULL,
            info_bot_response_date date NULL,
            shortlisted_json_version numeric DEFAULT 0.1 NOT NULL,
	        shortlisted jsonb DEFAULT '{"status": false, "shortlisted_at": "", "shortlisted_by": ""}'::jsonb NOT NULL,
            CONSTRAINT candidate_application_shortlists_detail_vacancy_id_contact_id PRIMARY KEY (vacancy_id, contact_id)
        );
        CREATE INDEX idx_candidate_application_shortlists_detail_contact_id ON {db_service.schema}.candidate_application_shortlists_detail USING btree (contact_id);
        CREATE INDEX idx_candidate_application_shortlists_detail_vacancy_id ON {db_service.schema}.candidate_application_shortlists_detail USING btree (vacancy_id);
        CREATE INDEX idx_ccandidate_application_shortlists_detail_feedbacks ON {db_service.schema}.candidate_application_shortlists_detail USING gin (fitness_reason);
        CREATE INDEX idx_candidate_application_shortlists_detail_info_bot_response_date ON {db_service.schema}.candidate_application_shortlists_detail (info_bot_response_date);
        CREATE INDEX idx_candidate_application_shortlists_detail_info_bot_response ON {db_service.schema}.candidate_application_shortlists_detail (info_bot_response);
        CREATE INDEX idx_ccandidate_application_shortlists_detail_shortlisted ON {db_service.schema}.candidate_application_shortlists_detail USING gin (shortlisted);

        -- Table Triggers

        create trigger set_timestamp_candidate_application_shortlists_detail before
        update
            on
            {db_service.schema}.candidate_application_shortlists_detail for each row execute function trigger_set_timestamp();
        create trigger set_updated_at_candidate_application_shortlists_detail before
        update
            on
            {db_service.schema}.candidate_application_shortlists_detail for each row execute function update_updated_at_column();
        """,
        # This table captures the candidates for which embedding need to be generated. This is a queue which stores the candidate contact_id as soon as their resume status is set to "Done Processing" in the candidate table. 
        # There is a trigger that inserts the contact_id into this table when the resume status is set to "Done Processing".
        f"""
        CREATE TABLE {db_service.schema}.candidate_embedding_generation_processing_queue (
            id serial4 NOT NULL,
            contact_id uuid NOT NULL,
            status text DEFAULT 'pending'::text NULL,
            created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
            processed_at timestamp NULL,
            job_title_embedding_status text NULL,
            tools_platform_embedding_status text NULL,
            tech_skills_embedding_status text NULL,
            soft_skills_embedding_status text NULL,
            industry_embedding_status text NULL,
            degees_certification_embedding_status text NULL,
            degrees_certification_as_json_object_embedding_status text NULL,
            CONSTRAINT candidate_embedding_generation_processing_queue_pkey PRIMARY KEY (id)
        );
        CREATE INDEX idx_candidate_embedding_generation_processing_queue_status ON {db_service.schema}.candidate_embedding_generation_processing_queue USING btree (status, id);
        CREATE UNIQUE INDEX idx_unique_pending_contact_id ON {db_service.schema}.candidate_embedding_generation_processing_queue USING btree (contact_id) WHERE (status = 'pending'::text);

        -- foreign keys

        ALTER TABLE {db_service.schema}.candidate_embedding_generation_processing_queue ADD CONSTRAINT fk_contact_id FOREIGN KEY (contact_id) REFERENCES {db_service.schema}.candidates(contact_id) ON DELETE CASCADE;
        """,
        f"""
        CREATE OR REPLACE FUNCTION {db_service.schema}.enqueue_done_processing_candidate_for_embedding_generation()
        RETURNS trigger
        LANGUAGE plpgsql
        AS $function$
        BEGIN
            -- Check if NEW.resume_data is not NULL and the new resume_status is 'Done Processing'.
            -- The (NEW.resume_data ->> 'resume_status') check implicitly handles if the key exists.
            -- If the key doesn't exist, ->> returns NULL, and NULL = 'Done Processing' is false (actually NULL).
            IF NEW.resume_data IS NOT NULL AND (NEW.resume_data ->> 'resume_status') = 'Done Processing' THEN

                IF TG_OP = 'INSERT' THEN
                    -- For INSERT operations, if a new record is created with resume_status = 'Done Processing',
                    -- it's considered a change from non-existence to 'Done Processing', so enqueue.
                    INSERT INTO {db_service.schema}.candidate_embedding_generation_processing_queue (contact_id, status)
                    VALUES (NEW.contact_id, 'pending')
                    ON CONFLICT (contact_id) WHERE status = 'pending'
                    DO NOTHING; -- Or DO UPDATE SET created_at = CURRENT_TIMESTAMP

                ELSIF TG_OP = 'UPDATE' THEN
                    -- For UPDATE operations, enqueue only if the resume_status *specifically changed to* 'Done Processing'.
                    -- This means the old status was NOT 'Done Processing'.
                    -- We use "IS DISTINCT FROM" to correctly handle cases where OLD.resume_data might be NULL,
                    -- or the 'resume_status' key might not have existed in OLD.resume_data (->> returns NULL),
                    -- or its value was different.
                    --   - NULL IS DISTINCT FROM 'Done Processing' -> TRUE
                    --   - 'SomeOtherStatus' IS DISTINCT FROM 'Done Processing' -> TRUE
                    --   - 'Done Processing' IS DISTINCT FROM 'Done Processing' -> FALSE
                    IF (OLD.resume_data ->> 'resume_status') IS DISTINCT FROM 'Done Processing' THEN
                        INSERT INTO {db_service.schema}.candidate_embedding_generation_processing_queue (contact_id, status)
                        VALUES (NEW.contact_id, 'pending')
                        ON CONFLICT (contact_id) WHERE status = 'pending'
                        DO NOTHING; -- Or DO UPDATE SET created_at = CURRENT_TIMESTAMP
                    END IF;
                END IF;
            END IF;

            RETURN NEW; -- Return value is typically ignored for AFTER triggers but good practice.
        END;
        $function$
        ;
        """,
        f"""
            create trigger candidates_resume_status_trigger after
            insert
                or
            update
                of resume_data on
                {db_service.schema}.candidates for each row execute function enqueue_done_processing_candidate_for_embedding_generation();
        """,
        f"""
            CREATE SEQUENCE IF NOT EXISTS {db_service.schema}.catalyst_match_job_processing_queue_seq
                AS BIGINT
                START WITH 1
                INCREMENT BY 1
                NO MINVALUE
                NO MAXVALUE
                CACHE 1;

            -- 2. Create the table
            CREATE TABLE {db_service.schema}.catalyst_match_job_processing_queue (
                sequence_number int8 DEFAULT nextval('catalyst_match_job_processing_queue_seq'::regclass) PRIMARY KEY NOT NULL,
                vacancy_id uuid NOT NULL,
                search_match_status text NULL,
                created_at timestamptz DEFAULT now() NOT NULL,
                updated_at timestamptz DEFAULT now() NOT NULL,
                search_match_completed_at timestamptz NULL,
                search_match_metadata jsonb DEFAULT '{"initiated_by": "system"}'::jsonb NULL,
                email_notification_status text NULL,
                email_notification_metadata jsonb NULL,
                "type" text DEFAULT 'catalyst_match'::text NOT NULL
            );

            -- 3. Create indexes on every column
            -- Sequence number
            CREATE INDEX IF NOT EXISTS idx_catalyst_match_job_processing_queue_sequence_number
                ON {db_service.schema}.catalyst_match_job_processing_queue(sequence_number);

            -- Vacancy ID
            CREATE INDEX IF NOT EXISTS idx_catalyst_match_job_processing_queue_vacancy_id
                ON {db_service.schema}.catalyst_match_job_processing_queue(vacancy_id);

            -- Search match status
            CREATE INDEX IF NOT EXISTS idx_catalyst_match_job_processing_queue_search_match_status
                ON {db_service.schema}.catalyst_match_job_processing_queue(search_match_status);

            -- Created timestamp
            CREATE INDEX IF NOT EXISTS idx_catalyst_match_job_processing_queue_created_at
                ON {db_service.schema}.catalyst_match_job_processing_queue(created_at);

            -- Updated timestamp
            CREATE INDEX IF NOT EXISTS idx_catalyst_match_job_processing_queue_updated_at
                ON {db_service.schema}.catalyst_match_job_processing_queue(updated_at);

            -- Completed timestamp
            CREATE INDEX IF NOT EXISTS idx_catalyst_match_job_processing_queue_search_match_completed_at
                ON {db_service.schema}.catalyst_match_job_processing_queue(search_match_completed_at);

            -- Email notification status
            CREATE INDEX IF NOT EXISTS idx_catalyst_match_job_processing_queue_email_notification_status
                ON {db_service.schema}.catalyst_match_job_processing_queue(email_notification_status);

            -- JSONB metadata columns (GIN for better JSON querying)
            CREATE INDEX IF NOT EXISTS idx_catalyst_match_job_processing_queue_search_match_metadata
                ON {db_service.schema}.catalyst_match_job_processing_queue
                USING GIN (search_match_metadata);

            CREATE INDEX IF NOT EXISTS idx_catalyst_match_job_processing_queue_email_notification_metadata
                ON {db_service.schema}.catalyst_match_job_processing_queue
                USING GIN (email_notification_metadata)
            
            CREATE UNIQUE INDEX uniq_active_job_per_vacancy
            ON {db_service.schema}.catalyst_match_job_processing_queue(vacancy_id)
            WHERE
                "type" = 'catalyst_match'
                AND search_match_status IN ('queued', 'inprocess');
        """,
        # Job Title  <-> Sub Category Embedding Table
        f"""
        CREATE TABLE IF NOT EXISTS {db_service.schema}.job_title_embedding (
            job_title_id SERIAL PRIMARY KEY,
            job_title TEXT,
            subcategory TEXT,
            category TEXT,
            subcategory_id INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            job_title_embedding vector(1536)
        );

        CREATE INDEX IF NOT EXISTS idx_job_title_embedding ON {db_service.schema}.job_title_embedding USING ivfflat (job_title_embedding vector_cosine_ops);
        """,
        # Degree <-> Subcategory Embedding Table
        f"""
        CREATE TABLE IF NOT EXISTS {db_service.schema}.degree_certification_embedding (
            degree_certification_id serial PRIMARY KEY,
            degree_cert_name TEXT,
            subcategory TEXT,
            category TEXT,
            subcategory_id INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            degree_cert_embedding vector(1536)
        );

        CREATE INDEX IF NOT EXISTS idx_degree_cert_embedding ON {db_service.schema}.degree_certification_embedding USING ivfflat (degree_cert_embedding vector_cosine_ops);
        """,     

        # Soft Skill <-> Subcategory Embedding Table
        f"""
        CREATE TABLE IF NOT EXISTS {db_service.schema}.soft_skill_embedding (
            soft_skill_id SERIAL PRIMARY KEY,
            soft_skill_name TEXT,
            subcategory TEXT,
            category TEXT,
            subcategory_id INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            soft_skill_embedding vector(1536)
        );

        CREATE INDEX IF NOT EXISTS idx_soft_skill_embedding ON {db_service.schema}.soft_skill_embedding USING ivfflat (soft_skill_embedding vector_cosine_ops);
        """,

        # Technical Skill  <-> Subcategory Embedding Table
        f"""
        CREATE TABLE IF NOT EXISTS {db_service.schema}.technical_skill_embedding (
            technical_skill_id SERIAL PRIMARY KEY,
            technical_skill_name TEXT,
            subcategory TEXT,
            category TEXT,
            subcategory_id INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            technical_skill_embedding vector(1536)
        );

        CREATE INDEX IF NOT EXISTS idx_technical_skill_embedding ON {db_service.schema}.technical_skill_embedding USING ivfflat (technical_skill_embedding vector_cosine_ops);
        """,

        # Tools and Platforms  <-> Subcategory Embedding Table
        f"""
        CREATE TABLE IF NOT EXISTS {db_service.schema}.tool_and_platform_embedding (
            tool_and_platform_id SERIAL PRIMARY KEY,
            tool_and_platform_name TEXT,
            subcategory TEXT,
            category TEXT,
            subcategory_id INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            tool_and_platform_embedding vector(1536)
        );

        CREATE INDEX IF NOT EXISTS idx_tool_and_platform_embedding ON {db_service.schema}.tool_and_platform_embedding USING ivfflat (tool_and_platform_embedding vector_cosine_ops);
        """
    ]
    
    try:
        db_service.create_tables(queries)
        db_service.logger.info("Embedding tables created successfully.")
    except Exception as e:
        db_service.logger.error(f"Error creating embedding tables: {e}")


def database_changes_v6(postgres_env=PostgresEnvironment.DEV):
    """Creates tables for historic candidate subcategory classification."""
    logger_config = {
        "level": "DEBUG",
        "log_to_stdout": True
    }
    logger = AppLogger(logger_config)
    
    # Create and connect the PostgresConnector
    postgres_connector = PostgresConnector(env=postgres_env, logger=logger)
    if not postgres_connector.connect():
        logger.error("Failed to connect to the database. Exiting.")
        return

    # Instantiate the service
    db_service = AttributeDatabaseService(logger, postgres_connector)
    db_service.logger.info("Starting database setup for historic candiate subcategory classification.")

    queries = [
        # Candidate Category Job Title Classification Table
        f"""
        CREATE TABLE  {db_service.schema}.candidate_subcategory_classification (
            contact_id uuid NOT NULL,
            email_address text NULL,
            full_name text NULL,
            classification_results jsonb NULL,
            processed_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
            last_updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
            CONSTRAINT candidate_subcategory_classification_pkey PRIMARY KEY (contact_id)
        );

        CREATE INDEX IF NOT EXISTS idx_candidate_subcategory_classification ON {db_service.schema}.candidate_subcategory_classification USING gin (classification_results);
        """
    ]
    
    try:
        db_service.create_tables(queries)
        db_service.logger.info("Embedding tables created successfully.")
    except Exception as e:
        db_service.logger.error(f"Error creating embedding tables: {e}")

def database_changes_v7(postgres_env=PostgresEnvironment.DEV):
    logger_config = {
        "level": "DEBUG",
        "log_to_stdout": True
    }
    logger = AppLogger(logger_config)

    postgres_connector = PostgresConnector(env=postgres_env, logger=logger)
    postgres_connector.connect()

    db_service = AttributeDatabaseService(logger, postgres_connector)
    db_service.logger.info("Starting creation of subcategory_weight_config table.")
    db_service.create_tables_subcategory_weight_config()
    db_service.logger.info("subcategory_weight_config table created successfully.")

def database_changes_v8(postgres_env=PostgresEnvironment.DEV):
    logger_config = {
        "level": "DEBUG",
        "log_to_stdout": True
    }
    logger = AppLogger(logger_config)

    postgres_connector = PostgresConnector(env=postgres_env, logger=logger)
    postgres_connector.connect()

    db_service = AttributeDatabaseService(logger, postgres_connector)
    db_service.logger.info("Starting population of subcategory_weight_config.")
    db_service.insert_subcategory_weight_configs()
    db_service.logger.info("subcategory_weight_config population completed.")



def main():
    env = PostgresEnvironment.DEV
    # database_changes_v0(env)
    # database_changes_v1(env)
    # database_changes_v2(env)
    # database_changes_v3(env)
    # database_changes_v4(env)
    # database_changes_v5(PostgresEnvironment.DEV_VECTOR)
    # database_changes_v6(env)
    # database_changes_v7(env)
    # database_changes_v8(env)
    

if __name__ == "__main__":
    from common.secrets_env import load_secrets_env_variables
    load_secrets_env_variables()
    #main()
    env = PostgresEnvironment.DEV
    # database_changes_v0(env)
    # database_changes_v1(env)
    # database_changes_v2(env)
    # database_changes_v4(env)
    #database_changes_v4(env)
    #database_changes_v5(PostgresEnvironment.DEV_VECTOR)
    # database_changes_v6(env)
    # database_changes_v7(env)
    # database_changes_v8(env)
