import {
  <PERSON>Header,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table";
import { ArrowUpDown, FileText } from "lucide-react";
import Loading from "@/components/Loading";
import WhyFitAction from "../candidates/WhyFitAction";
import AppToolTip from "../AppToolTip";
import ThumbAction from "../candidates/ThumbAction";
import { Candidate, Vacancy } from "@/app/candidates/helper";
import { unFormattedDateWithBrowserTimezoneInDDMMYY } from "@/utils/utils";
import { useState } from "react";
import Modal from "../Modal";

interface TableProps {
  handleCandidateSort: (name: string) => void;
  isResumeModalOpen: boolean;
  loading?: boolean;
  paginatedCandidates: Candidate[];
  selectedVacancy: Vacancy;
  candidates: Candidate[];
  setCandidates: React.Dispatch<React.SetStateAction<Candidate[]>>;
  vacancyCandidates: {
    [key: string]: Candidate[];
  } | null;
  setVacancyCandidates: React.Dispatch<
    React.SetStateAction<{
      [key: string]: Candidate[];
    } | null>
  >;
  fetchResume: (name: Candidate) => void;
  showSelectBox?: boolean;
  handleCheckboxClick?: (id: number) => void;
  selectedRows?: number[];
  mercuryPortal: boolean;
  IS_LOCK_FEATURE_DISABLED: string;
}

const CandidateTable = ({
  handleCandidateSort,
  isResumeModalOpen,
  loading,
  paginatedCandidates,
  selectedVacancy,
  candidates,
  setCandidates,
  vacancyCandidates,
  setVacancyCandidates,
  fetchResume,
  showSelectBox,
  handleCheckboxClick,
  selectedRows,
  mercuryPortal,
  IS_LOCK_FEATURE_DISABLED,
}: TableProps) => {
  const [existingEdit, setExistingEdit] = useState(false);
  const [candidateEditId, setCandidateEditId] = useState<string>("");
  const [candidateEditId2, setCandidateEditId2] = useState<string>("");
  const [expandPopupOpen, setExpandPopupOpen] = useState(false);
  const [hideDislikedCandidates, setHideDislikedCandidates] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [mercuryURL, setMercuryURL] = useState<string>("");
  const handleHideDislikedCandidates = () => {
    setHideDislikedCandidates(!hideDislikedCandidates);
  };

  // Filter candidates based on hide disliked state
  const filteredCandidates = hideDislikedCandidates
    ? paginatedCandidates.filter(
        (candidate) =>
          candidate?.candidate_data?.recruiter_review_decision?.vote !==
          "dislike"
      )
    : paginatedCandidates;

  // const handleActionGuard = (action: () => void) => {
  //   if (existingEdit && candidateEditId) {
  //     setCandidateEditId2("targetCandidateId");
  //   } else {
  //     action(); // Safe to proceed
  //   }
  // };
  function getEnvType(envCode: string): "sandbox" | "prod" | "uat" {
    const code = envCode.toLowerCase();
    if (["qa", "dv", "sb"].includes(code)) {
      return "sandbox";
    }
    if (code === "ua") {
      return "uat";
    }
    if (code === "prod") {
      return "prod";
    }
    return "sandbox";
  }

  const getDomainClient = () => {
    const url = window.location.href;
    const match = url.match(
      /^https:\/\/recruiter(?:\.([^.]+))?\.tandymgroup\.com/
    );
    const result = match && match[1] ? match[1] : "prod";

    const envType = getEnvType(result);
    const crmDomain =
      envType === "prod"
        ? "tandymgroup.crm.dynamics.com"
        : `tandymgroup-${envType}.crm.dynamics.com`;

    return crmDomain;
  };

  const handleOpenPopup = async (contactId: string, mercuryPortal: boolean) => {
    const crmDomain = getDomainClient();
    let tandymURL = "";
    try {
      tandymURL = window.top?.location?.href || "";
    } catch (err) {
      console.warn(
        "Unable to access top window location due to cross-origin:",
        err
      );
      tandymURL = window.location.href;
    }
    const url = `https://${crmDomain}/main.aspx?appid=0ec72dfd-7eb1-ee11-a569-00224822704f&forceUCI=1&pagetype=entityrecord&etn=contact&id=${contactId}`;
    url && window.open(url, "_blank");
  };

  return (
    <div
      className={`max-h-[80vh] rounded-lg ${
        expandPopupOpen ? "overflow-hidden" : "overflow-auto"
      }`}
    >
      <table className="w-full caption-bottom text-sm">
        <TableHeader className="rounded-lg sticky top-0 z-[10]">
          <TableRow className="bg-gray-900 rounded-lg hover:bg-gray-900 text-white text-[12px]">
            {showSelectBox && (
              <TableHead className="p-2 text-white w-[100px] rounded-tl-lg">
                Select
              </TableHead>
            )}
            <TableHead
              className={`p-2 text-white w-[250px] cursor-pointer ${
                !showSelectBox ? "rounded-tl-lg" : ""
              }`}
            >
              Name
            </TableHead>
            <TableHead
              onClick={() => handleCandidateSort("city")}
              className="p-2 text-white w-[90px] cursor-pointer"
            >
              Location
              <ArrowUpDown className="inline w-3 h-3 ml-1 cursor-pointer" />
            </TableHead>
            <TableHead
              onClick={() => handleCandidateSort("availability")}
              className="p-2 text-white w-[90px] cursor-pointer"
            >
              Availability Date
              <ArrowUpDown className="inline w-3 h-3 ml-1 cursor-pointer" />
            </TableHead>
            <TableHead className="p-2 text-white w-[90px] cursor-pointer">
              AI Agent Status
            </TableHead>
            <TableHead
              onClick={() => handleCandidateSort("freshness_index")}
              className="p-2 text-white w-[90px] cursor-pointer"
            >
              Freshness Index
              <ArrowUpDown className="inline w-3 h-3 ml-1 cursor-pointer" />
            </TableHead>
            <TableHead
              className="p-2 text-white w-[70px] cursor-pointer"
              onClick={() => handleCandidateSort("overallscore")}
            >
              Total Score
              <ArrowUpDown className="inline w-3 h-3 ml-1 cursor-pointer" />
            </TableHead>
            <TableHead className="p-2 text-white w-[80px]">
              Parsed Resume
            </TableHead>
            <TableHead className="p-2 text-white w-[85px]">
              <div className="flex flex-col items-center">
                <span className="text-xs mb-1">Rating</span>
                <button
                  onClick={handleHideDislikedCandidates}
                  className={`flex items-center gap-1 px-2 py-1 text-xs border border-white rounded transition-colors ${
                    hideDislikedCandidates
                      ? "bg-white text-gray-900"
                      : "bg-transparent hover:bg-white hover:text-gray-900"
                  }`}
                >
                  <span>{hideDislikedCandidates ? "View" : "Hide"}</span>
                  <svg className="w-3 h-3 fill-red-500" viewBox="0 0 24 24">
                    <path d="M15 3H6c-.83 0-1.54.5-1.84 1.22l-3.02 7.05c-.09.23-.14.47-.14.73v2c0 1.1.9 2 2 2h6.31l-.95 4.57-.03.32c0 .41.17.79.44 1.06L9.83 23l6.59-6.59c.36-.36.58-.86.58-1.41V5c0-1.1-.9-2-2-2zm4 0v12h4V3h-4z" />
                  </svg>
                </button>
              </div>
            </TableHead>

            <TableHead className="p-2 text-white w-[280px] text-left">
              Why Fit
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {loading && !isResumeModalOpen ? (
            <TableRow>
              <TableCell colSpan={9} className="p-4 text-center">
                <Loading height="h-[50vh]" />
              </TableCell>
            </TableRow>
          ) : filteredCandidates.length > 0 ? (
            filteredCandidates.map((candidate, ind) => (
              <TableRow
                key={candidate.candidate_data.contactid}
                className={ind % 2 === 1 ? "bg-gray-100" : "bg-white"}
              >
                {showSelectBox && selectedRows && handleCheckboxClick && (
                  <TableCell className="p-2 text-[14px] w-[100px]">
                    <input
                      type="checkbox"
                      checked={selectedRows.includes(candidate.id)}
                      onChange={() => handleCheckboxClick(candidate.id)}
                      className="accent-gray-800 cursor-pointer"
                    />
                  </TableCell>
                )}
                <TableCell
                  className="p-2 text-[14px] w-[250px]"
                  onClick={() =>
                    handleOpenPopup(
                      candidate.candidate_contactid,
                      mercuryPortal
                    )
                  }
                >
                  <AppToolTip
                    text={candidate.candidate_data.name}
                    header={
                      <p className="truncate md:max-w-[250px]">
                        {candidate.candidate_data.name}
                      </p>
                    }
                    direction="top"
                  />
                  {/* <AppToolTip
                      text={candidate.candidate_data.email}
                      header={
                        <p className="truncate md:max-w-[300px] 2xl:max-w-full">
                          ({candidate.candidate_data.email})
                        </p>
                      }
                      direction="top"
                    /> */}
                </TableCell>
                <TableCell className="p-2 text-[14px] w-[90px]">
                  {(() => {
                    const { city, state } = candidate.candidate_data;
                    const isMissing = (val: string | null | undefined) =>
                      !val || val === "Missing";

                    if (isMissing(city) && isMissing(state)) return "";
                    if (!isMissing(city) && !isMissing(state))
                      return `${city}, ${state}`;
                    return !isMissing(city) ? city : state;
                  })()}
                </TableCell>
                <TableCell className="p-2 text-[14px] w-[90px]">
                  {(() => {
                    const availability = candidate.candidate_data.availability;
                    if (!availability || availability === "Missing") return "";
                    return unFormattedDateWithBrowserTimezoneInDDMMYY(
                      availability
                    );
                  })()}
                </TableCell>
                <TableCell className="p-2 text-[14px] w-[70px]">
                  {candidate.candidate_data["info_bot_response"]}
                  <br />
                  {candidate.candidate_data["info_bot_response_date"] != null
                    ? unFormattedDateWithBrowserTimezoneInDDMMYY(
                        candidate.candidate_data["info_bot_response_date"]
                      )
                    : null}
                </TableCell>
                <TableCell className="p-2 text-[13px] w-[70px]">
                  {candidate.candidate_data["freshness_index"]}
                </TableCell>
                <TableCell className="p-2 text-[14px] w-[70px]">
                  {candidate.candidate_data[
                    "classification score"
                  ]?.overallscore.toFixed(2)}
                </TableCell>
                <TableCell className="p-2 space-x-2 h-14 w-[50px] sticky right-[0px]">
                  <AppToolTip
                    header={
                      <FileText
                        size={18}
                        className="text-gray-700"
                        onClick={() => fetchResume(candidate)}
                        aria-disabled={!candidate.candidate_contactid}
                      />
                    }
                    text="View Resume"
                  />
                </TableCell>
                <TableCell className="p-2 space-x-2 h-14 w-[85px]">
                  <ThumbAction
                    candidate={candidate}
                    setCandidates={setCandidates}
                    candidates={candidates}
                    vacancyId={selectedVacancy?.vacancy_id}
                    vacancyRefNo={selectedVacancy?.refno}
                    vacancyCandidates={vacancyCandidates || {}}
                    setVacancyCandidates={setVacancyCandidates}
                    selectedVacancy={selectedVacancy}
                    IS_LOCK_FEATURE_DISABLED={IS_LOCK_FEATURE_DISABLED}
                    mercuryPortal={mercuryPortal}
                  />
                </TableCell>

                <TableCell className="p-2 pl-0 space-x-2 h-14 w-[280px] 2xl:w-[550px]">
                  <div className={`flex gap-[5px]`}>
                    <WhyFitAction
                      candidate={candidate}
                      setCandidates={setCandidates}
                      selectedVacancy={selectedVacancy}
                      existingEdit={existingEdit}
                      setExistingEdit={setExistingEdit}
                      candidateEditId={candidateEditId}
                      candidateEditId2={candidateEditId2}
                      setCandidateEditId={setCandidateEditId}
                      setCandidateEditId2={setCandidateEditId2}
                      setExpandPopupOpen={setExpandPopupOpen}
                    />
                  </div>
                </TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={9} className="p-4 text-center">
                No candidates found
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </table>
    </div>
  );
};

export default CandidateTable;
