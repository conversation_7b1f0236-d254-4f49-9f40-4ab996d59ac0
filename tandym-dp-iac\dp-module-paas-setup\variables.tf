
variable "location" {
  description = "The location for the resources."
  type        = string
  default     = "eastus"
}

variable "environment" {
  type        = string
  description = "Environment Name for the resources"
  default     = "sbe"
}
# variable "file_path" {
#   description = "Path to the file"
#   type        = string
#   # Optionally, you can set a default value
#   # default     = "default/path/to/file"
# }
variable "k8s_dns_prefix" {
  description = "DNS prefix to use with hosted Kubernetes"
  type        = string
  default     = "Tandym"
}
variable "label" {
  type    = string
  default = "TandymRecruiterPortal"
}
variable "adminlabel" {
  type    = string
  default = "TandymAdmin"
}

variable "organization" {
  description = "The name of the publisher"
  type        = string
  default     = "tg"
}

variable "host_name" {
  type        = string
  description = "hostname"
  default     = "tg-sbeue-apim-apim001.azure-api.net"
}
variable "candidate_host_name" {
  type        = string
  description = "hostname"
  default     = "tg-sbeue-apim-apim001.azure-api.net"
}
variable "client_host_name" {
  type        = string
  description = "hostname"
  default     = "tg-sbeue-apim-apim001.azure-api.net"
}

### App Config variables ###

variable "AZURE_SQLSERVER_SERVER" {
  type        = string
  description = "AZURE_SQLSERVER_SERVER"
  default     = "az-azSqlDev-01.database.windows.net"
}

variable "AZURE_SQLSERVER_DATABASE" {
  type        = string
  description = "AZURE_SQLSERVER_DATABASE"
  default     = "dvSync.Sandbox"
}

variable "AZURE_SHAREPOINT_SITE_URL" {
  type        = string
  description = "AZURE_SHAREPOINT_SITE_URL"
  default     = "https://execusearchgroup.sharepoint.com/sites/MercurySandbox"
}

variable "AZURE_DATAVERSE_RESOURCE_URL" {
  type        = string
  description = "AZURE_DATAVERSE_RESOURCE_URL"
  default     = "https://tandymgroup-sandbox.crm.dynamics.com"
}

variable "AZURE_DATAVERSE_CRM_URL" {
  type        = string
  description = "AZURE_DATAVERSE_CRM_URL"
  default     = "https://tandymgroup-sandbox.crm.dynamics.com/api/data/v9.0/"
}

variable "NEXTAUTH_URL" {
  type        = string
  description = "NEXTAUTH_URL"
  default     = "http://localhost:3000"
}

# Shared DB Connection #
variable "DBSharedConnectionUsername" {
  type        = string
  description = "DBSharedConnectionUsername"
  default     = "tandymdp"
}

variable "DBSharedConnectionURL" {
  type        = string
  description = "DBSharedConnectionURL"
  default     = "tgdvueenvpsqlflex001.postgres.database.azure.com"
}

variable "DBSharedConnectionDatabase" {
  type        = string
  description = "DBSharedConnectionDatabase"
  default     = "tg-dvue-env-psqldb001"
}

variable "DBSharedConnectionPort" {
  type        = string
  description = "DBSharedConnectionPort"
  default     = "5432"
}

# variable "NEXT_PUBLIC_AUTH_URL" {
#   type        = string
#   description = "NEXTAUTH_URL"
#   default     = "http://localhost:3000"
# }

# variable "NEXT_APPINSIGHTS_CONNECTION_STRING" {
#   type        = string
#   description = "NEXTAUTH_URL"
#   default     = "InstrumentationKey=a2333a49-cb49-4a2c-8888-d5a4bc1ed97a;IngestionEndpoint=https://eastus-3.in.applicationinsights.azure.com/;LiveEndpoint=https://eastus.livediagnostics.monitor.azure.com/;ApplicationId=5edf80f7-54c1-42c5-910c-1c767571a849"
# }


