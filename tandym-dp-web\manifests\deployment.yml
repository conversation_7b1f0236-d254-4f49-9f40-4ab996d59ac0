apiVersion: apps/v1
kind: Deployment
metadata:
  name: tandym-dp-web
  labels:
    app: tandym-dp-web
spec:
  replicas: 1
  selector:
    matchLabels:
      app: tandym-dp-web
  template:
    metadata:
      labels:
        app: tandym-dp-web
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: "workload-type"
                    operator: In
                    values:
                      - "dp-apps"
      tolerations:
        - key: "node.kubernetes.io/unreachable"
          operator: "Exists"
          effect: "NoSchedule"
        - key: "node.cloudprovider.kubernetes.io/shutdown"
          operator: "Exists"
          effect: "NoSchedule"
        - key: "node.kubernetes.io/out-of-service"
          operator: "Exists"
          effect: "NoExecute"
        - key: "node.kubernetes.io/unreachable"
          operator: "Exists"
          effect: "NoExecute"
      volumes:
        - name: sharepoint-secret-volume
          secret:
            secretName: sharepoint-secret
      containers:
        - name: tandym-dp-web
          image: $webcontainerimage
          volumeMounts:
            - name: sharepoint-secret-volume
              mountPath: /certs
          ports:
            - containerPort: 3000
          env:
            - name: AppConfigurationEndpoint
              value: $AppConfigurationEndpoint
            - name: AZURE_CLIENT_ID
              value: $AZURE_CLIENT_ID
            - name: AZURE_CLIENT_SECRET
              value: $AZURE_CLIENT_SECRET
            - name: AZURE_TENANT_ID
              value: $AZURE_TENANT_ID
            - name: AZURE_SUBSCRIPTION_ID
              value: $AZURE_SUBSCRIPTION_ID
            - name: NEXT_PUBLIC_BASE_URL
              value: $NEXT_PUBLIC_BASE_URL
            - name: RECRUITER_SSO_CLIENT_ID
              value: $RECRUITER_SSO_CLIENT_ID
            - name: RECRUITER_SSO_CLIENT_SECRET
              value: $RECRUITER_SSO_CLIENT_SECRET
            - name: NEXTAUTH_SECRET
              value: $NEXTAUTH_SECRET
            - name: NEXTAUTH_URL
              value: $NEXTAUTH_URL
            - name: NEXT_PUBLIC_AD_LOGIN
              value: "$NEXT_PUBLIC_AD_LOGIN"
            - name: NEXT_PUBLIC_AZURE_TENANT_ID
              value: $AZURE_TENANT_ID
            - name: DP_PORTAL_SERVICE
              value: $DP_PORTAL_SERVICE
            - name: NEXT_PUBLIC_PARSE_RESUME_FROM_MERCURY
              value: "$NEXT_PUBLIC_PARSE_RESUME_FROM_MERCURY"
            - name: IS_ENTITLEMENT_ENABLED
              value: "true"
            - name: NEXT_PUBLIC_IS_WHY_FIT_EDITABLE
              value: "false" 
            - name: NEXT_PUBLIC_IS_LOCK_FEATURE_DISABLED
              value: "true" 
            - name: CRM_URL
              value: "$CRM_URL"
            - name: NEXT_PUBLIC_APPINSIGHTS_CONNECTION_STRING
              value: "$NEXT_PUBLIC_APPINSIGHTS_CONNECTION_STRING"
            - name: NEXT_PUBLIC_AUTH_URL
              value: "$NEXT_PUBLIC_AUTH_URL"
          resources:
            requests:
              memory: "500Mi"
              cpu: "250m"
            limits:
              memory: "500Mi"
              cpu: "250m"
          livenessProbe:
            httpGet:
              path: /api/health-check
              port: 3000
            initialDelaySeconds: 60
            periodSeconds: 20
            timeoutSeconds: 10
            failureThreshold: 3
            successThreshold: 1
          readinessProbe:
            httpGet:
              path: /api/health-check
              port: 3000
            initialDelaySeconds: 60
            periodSeconds: 20
            timeoutSeconds: 30
            failureThreshold: 3
            successThreshold: 1
      imagePullSecrets:
        - name: tandym-dp-pull-secret
