"use client";
import React, { useEffect, useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Pencil, Save, ArrowUpDown, Undo2 } from "lucide-react";
import Loading from "@/components/Loading";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useNotification } from "@/hooks/useNotification";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import Modal from "@/components/Modal";
import type {
  SortConfig,
  SubcategoryWeightConfig,
} from "@/types/subcategory-configTypes";

const SubcategoryWeightConfigsPage = () => {
  const { showNotification } = useNotification();
  const [data, setData] = useState<SubcategoryWeightConfig[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [sortConfig, setSortConfig] = useState<SortConfig>({
    key: null,
    direction: null,
  });
  const [filteredData, setFilteredData] = useState<SubcategoryWeightConfig[]>(
    []
  );
  const [selectedId, setSelectedId] = useState<number[]>([]);
  const [overLimit, setOverLimit] = useState(false);
  const [search, setSearch] = useState("");
  const [discardChanges, setDiscardChanges] = useState(false);
  const [saveDiscardChanges, setSaveDiscardChanges] = useState(true);

  useEffect(() => {
    const filtered = data?.filter((item) =>
      item?.subcategory_name?.toLowerCase()?.includes(search?.toLowerCase())
    );
    setFilteredData(filtered);
  }, [search, data]);

  // table header names
  const scoreFields = [
    "job_title_score_weight",
    "soft_skills_score_weight",
    "technical_skills_score_weight",
    "tools_and_platforms_score_weight",
    "degrees_certs_score_weight",
    "industry_experience_score_weight",
    "job_title_recency_score_weight",
    "experience_score_weight",
    "industry_recency_score_weight",
  ];

  // filter out the selected data from the table
  const updatedData = filteredData?.filter((item) =>
    selectedId?.includes(item?.id)
  );

  const fetchData = async () => {
    try {
      setIsLoading(true);
      const res = await fetch("/api/subcategory-settings");
      const result = await res.json();
      setData(JSON.parse(JSON.stringify(result)));
      setFilteredData(JSON.parse(JSON.stringify(result)));
      setSaveDiscardChanges(true);
    } catch (error) {
      console.error(error);
      showNotification(`Failed to Fetch Data`);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  // calculate the total score weight
  const totalScoreWeight = (item: SubcategoryWeightConfig) => {
    const fields = scoreFields as (keyof SubcategoryWeightConfig)[];
    const totalScore = fields?.reduce(
      (sum, key) => sum + (Number(item[key]) || 0.0),
      0.0
    );
    return totalScore;
  };

  // sort the table data
  const handleSort = (
    key:
      | keyof SubcategoryWeightConfig
      | "params.combine_tp_tnp"
      | "params.only_US_degrees"
  ) => {
    let direction: "asc" | "desc" = "asc";
    if (sortConfig?.key === key && sortConfig?.direction === "asc") {
      direction = "desc";
    }
    setSortConfig({ key, direction });

    const getValue = (item: SubcategoryWeightConfig) => {
      if (key === "params.combine_tp_tnp") {
        // Only sort by combine_tp_tnp
        const val = item?.params?.combine_tp_tnp;
        if (val === true) return 2;
        if (val === false) return 1;
        return 0;
      }
      if (key === "params.only_US_degrees") {
        // Only sort by only_US_degrees
        const val = item?.params?.only_US_degrees;
        if (val === true) return 2;
        if (val === false) return 1;
        return 0;
      }
      return item[key as keyof SubcategoryWeightConfig] ?? 0;
    };

    const sorted = [...filteredData]?.sort((a, b) => {
      const aValue = getValue(a);
      const bValue = getValue(b);

      if (
        (key === "params.combine_tp_tnp" || key === "params.only_US_degrees") &&
        typeof aValue === "number" &&
        typeof bValue === "number"
      ) {
        return direction === "asc" ? aValue - bValue : bValue - aValue;
      }

      if (typeof aValue === "string" && typeof bValue === "string") {
        return direction === "asc"
          ? aValue?.localeCompare(bValue)
          : bValue?.localeCompare(aValue);
      }
      return direction === "asc"
        ? Number(aValue) - Number(bValue)
        : Number(bValue) - Number(aValue);
    });

    setFilteredData(sorted);
  };

  // handle the edit pencil icon click
  const handleEdit = (id: number) => {
    setSelectedId([...selectedId, id]);
  };

  const handleChange = (
    field: keyof SubcategoryWeightConfig,
    value: string | SubcategoryWeightConfig["params"],
    id: number
  ) => {
    const newData = [...filteredData];
    const rowIndex = newData?.findIndex((item) => item?.id === id);
    if (rowIndex !== -1) {
      // Deep clone the object before mutating
      const foundItem = JSON.parse(JSON.stringify(newData[rowIndex]));
      if (field === "params" && typeof value === "object") {
        foundItem.params = value;
      } else {
        (foundItem as any)[field] = value;
      }
      newData[rowIndex] = foundItem;
    }
    setFilteredData(newData);
  };

  // ParamSelect for params fields, only allows true/false (no N/A)
  const ParamSelect = ({
    value,
    onChange,
  }: {
    value: boolean | null | undefined;
    onChange: (val: boolean) => void;
  }) => (
    <Select
      value={value === true ? "yes" : "no"}
      onValueChange={(val) => onChange(val === "yes")}
    >
      <SelectTrigger>
        <SelectValue />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          <SelectItem value="yes">yes</SelectItem>
          <SelectItem value="no">no</SelectItem>
        </SelectGroup>
      </SelectContent>
    </Select>
  );

  useEffect(() => {
    const hasInvalidTotal = updatedData?.some((item) => {
      const total = Number(totalScoreWeight(item)?.toFixed(2));
      return (total > 1.0 || total < 1.0) && total !== 0;
    });
    setOverLimit(hasInvalidTotal);
  }, [filteredData, selectedId]);

  // handle the input values
  const inputValues = (
    field: keyof SubcategoryWeightConfig,
    id: number,
    item: SubcategoryWeightConfig
  ) => {
    return (
      <Input
        type="number"
        min={0}
        max={1}
        step={0.01}
        placeholder="0.00"
        value={
          typeof item[field] === "boolean"
            ? String(item[field])
            : typeof item[field] === "object"
            ? ""
            : Number(item[field] ?? 0.0)?.toString()
        }
        onChange={(e) => {
          // Accept keyboard input and allow empty string for editing
          const val = e.target.value;
          // Allow empty input for editing, otherwise parse as float
          handleChange(field, val === "" ? "" : String(parseFloat(val)), id);
        }}
        className="w-13 text-start text-sm"
      />
    );
  };

  const handleUndoChanges = (id: number) => {
    setSelectedId(selectedId?.filter((itemId) => itemId !== id));
    const newData = [...filteredData];
    const originalData = data?.find((item) => item?.id === id);
    const rowIndex = newData?.findIndex((item) => item?.id === id);
    if (originalData && rowIndex !== -1) {
      newData[rowIndex] = JSON.parse(JSON.stringify(originalData)); // deep clone
    }
    setSaveDiscardChanges(true);
    setFilteredData(newData);
  };

  const updatedWeights = updatedData
    ?.filter((i: SubcategoryWeightConfig) => i.total_score_weight !== 0)
    ?.map(({ total_score_weight, ...rest }) => rest);

  // handle the save changes api
  const handleSaveChanges = async () => {
    try {
      setIsLoading(true);
      const resp = await Promise.allSettled(
        updatedWeights?.map((row: any) =>
          fetch(`/api/subcategory-settings/${row.subcategory_id}`, {
            method: "PUT",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(row),
          })
        )
      );
      if (resp[0].status === "fulfilled") {
        fetchData();
        showNotification(`Weights updated successfully`, "success");
        setSelectedId([]);
        setSaveDiscardChanges(true);
      }
    } catch (error: any) {
      showNotification(`Failed to update the weights`, "error");
      console.error("Error::", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDiscardAllChanges = () => {
    setSelectedId([]);
    setFilteredData(JSON.parse(JSON.stringify(data))); // deep clone
    setOverLimit(false);
    setSaveDiscardChanges(true);
  };

  useEffect(() => {
    // If overLimit is true, always disable save/discard buttons
    if (overLimit) {
      setSaveDiscardChanges(true);
      return;
    }

    // Only compare the rows that are being edited (selectedId)
    if (selectedId.length === 0) {
      setSaveDiscardChanges(true);
      return;
    }

    const editedFilteredRows = filteredData?.filter((item) =>
      selectedId?.includes(item.id)
    );
    const editedOriginalRows = data?.filter((item) =>
      selectedId?.includes(item.id)
    );

    if (
      JSON.stringify(editedFilteredRows) !== JSON.stringify(editedOriginalRows)
    ) {
      setSaveDiscardChanges(false); // Enable buttons
    } else {
      setSaveDiscardChanges(true); // Disable buttons
    }
  }, [filteredData, data, overLimit, selectedId]);

  // loading state
  if (isLoading) return <Loading />;

  return (
    <div className="w-full lg:w-[1260px] 2xl:w-[95%] mx-auto mt-5 p-5 pb-2 bg-white shadow-lg rounded-lg overflow-hidden">
      <h2 className="text-2xl font-bold mb-10 text-center capitalize items-center justify-center pt-2">
        Subcategory config settings
      </h2>
      {overLimit && (
        <p className="text-red-600 mb-4 font-medium ml-5">
          ⚠️ Total must be exactly 1. Please adjust the values where subcategory
          and total marked red.
        </p>
      )}
      <div className="w-[97%] mx-auto">
        <div className="flex justify-between items-center">
          <Input
            type="text"
            placeholder="Search Subcategory..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="w-1/3 border border-gray-300 rounded-lg p-2"
          />
          <div className="flex justify-end my-2 gap-2">
            <Button
              onClick={() => setDiscardChanges(true)}
              className="bg-green-600 hover:bg-green-700"
              disabled={saveDiscardChanges}
            >
              Discard All
            </Button>
            <Button
              onClick={handleSaveChanges}
              className="bg-green-600 hover:bg-green-700"
              disabled={saveDiscardChanges}
            >
              <Save /> Save All
            </Button>
          </div>
        </div>
        <div className="bg-gray-300">
          <Table className="w-full overflow-auto">
            <TableHeader className="bg-gray-900">
              <TableRow className="bg-gray-900">
                <TableHead className="bg-gray-900 text-white">
                  Subcategory
                  <ArrowUpDown
                    onClick={() => handleSort("subcategory_name")}
                    className="cursor-pointer inline ml-1 h-4 w-4"
                  />
                </TableHead>
                <TableHead className="bg-gray-900 text-white py-2">
                  Combine tp tnp
                  <ArrowUpDown
                    onClick={() => handleSort("params.combine_tp_tnp")}
                    className="cursor-pointer inline ml-1 h-4 w-4"
                  />
                </TableHead>
                <TableHead className="bg-gray-900 text-white py-2">
                  Only US Degrees
                  <ArrowUpDown
                    onClick={() => handleSort("params.only_US_degrees")}
                    className="cursor-pointer inline ml-1 h-4 w-4"
                  />
                </TableHead>
                <TableHead className="bg-gray-900 text-white">
                  Total Weight
                </TableHead>
                {scoreFields?.map((field) => (
                  <TableHead
                    className="capitalize bg-gray-900 text-white break-words"
                    key={field}
                  >
                    {field?.replace(/_/g, " ")}
                    <ArrowUpDown
                      onClick={() =>
                        handleSort(field as keyof SubcategoryWeightConfig)
                      }
                      className="inline ml-1 h-4 w-4 cursor-pointer"
                    />
                  </TableHead>
                ))}
                <TableHead className="bg-gray-900 text-white"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody className="overflow-auto">
              {filteredData?.map((item: any, index) => {
                return (
                  <TableRow
                    key={index}
                    className={`${index % 2 === 1 ? "bg-gray-50" : ""}`}
                  >
                    <TableCell>{item?.subcategory_name}</TableCell>
                    <TableCell>
                      {selectedId?.includes(item.id) ? (
                        <ParamSelect
                          value={item.params?.combine_tp_tnp}
                          onChange={(newValue) =>
                            handleChange(
                              "params",
                              { ...item?.params, combine_tp_tnp: newValue },
                              item?.id
                            )
                          }
                        />
                      ) : item.params?.combine_tp_tnp === null ||
                        item.params?.combine_tp_tnp === undefined ? (
                        "N/A"
                      ) : item.params?.combine_tp_tnp ? (
                        "yes"
                      ) : (
                        "no"
                      )}
                    </TableCell>
                    <TableCell>
                      {selectedId?.includes(item?.id) ? (
                        <ParamSelect
                          value={item?.params?.only_US_degrees}
                          onChange={(newValue) =>
                            handleChange(
                              "params",
                              { ...item?.params, only_US_degrees: newValue },
                              item?.id
                            )
                          }
                        />
                      ) : item?.params?.only_US_degrees === null ||
                        item?.params?.only_US_degrees === undefined ? (
                        "N/A"
                      ) : item?.params?.only_US_degrees ? (
                        "yes"
                      ) : (
                        "no"
                      )}
                    </TableCell>
                    <TableCell
                      className={`font-semibold
                        ${
                          Number(totalScoreWeight(item)?.toFixed(2)) > 1.0 ||
                          (Number(totalScoreWeight(item)?.toFixed(2)) < 1.0 &&
                            Number(totalScoreWeight(item)?.toFixed(2)) !== 0)
                            ? "text-red-600"
                            : ""
                        }
                      `}
                    >
                      {totalScoreWeight(item)?.toFixed(2)}
                    </TableCell>
                    {scoreFields?.map((field, fieldIdx) => (
                      <TableCell key={fieldIdx}>
                        {selectedId?.includes(item?.id)
                          ? inputValues(
                              field as keyof SubcategoryWeightConfig,
                              item?.id,
                              item
                            )
                          : `${
                              isNaN(
                                Number(
                                  item[field as keyof SubcategoryWeightConfig]
                                )
                              )
                                ? "0.00"
                                : Number(
                                    item[field as keyof SubcategoryWeightConfig]
                                  )?.toFixed(2)
                            }`}
                      </TableCell>
                    ))}
                    <TableCell>
                      {selectedId?.includes(item?.id) ? (
                        <Undo2
                          className="text-blue-600 cursor-pointer"
                          onClick={() => handleUndoChanges(item.id)}
                        />
                      ) : (
                        <Pencil
                          className="text-blue-600 cursor-pointer"
                          onClick={() => handleEdit(item.id)}
                          size={16}
                        />
                      )}
                    </TableCell>
                  </TableRow>
                );
              })}
              {filteredData?.length === 0 && (
                <TableRow>
                  <TableCell colSpan={13} className="text-center py-8">
                    No data found
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
          <Modal
            isOpen={discardChanges}
            onClose={() => setDiscardChanges(false)}
          >
            <p className="mb-5">
              Are you sure, you want to Discard All the Changes?
            </p>
            <div className="flex justify-end gap-4">
              <Button
                onClick={() => setDiscardChanges(false)}
                className=""
                style={{
                  color: "#e95151",
                  border: "1px solid #e95151",
                  background: "#fff",
                }}
              >
                Cancel
              </Button>
              <Button
                onClick={() => {
                  handleDiscardAllChanges();
                  setDiscardChanges(false);
                }}
                style={{ background: "#e95151" }}
                className="bg-[#cc4949] hover:bg-[#e95151]"
              >
                Discard All
              </Button>
            </div>
          </Modal>
        </div>
      </div>
    </div>
  );
};

export default SubcategoryWeightConfigsPage;
