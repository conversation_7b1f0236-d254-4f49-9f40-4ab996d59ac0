variables:
  WebLBIP: ************
  ServerLBIP: ************
  PortalLBIP: ************
  ReactAppEnv: sbe
  WebMinReplicas: 1
  WebMaxReplicas: 1
  WebMemThreshold: 80
  WebCpuThreshold: 80
  ServerMinReplicas: 1
  ServerMaxReplicas: 1
  ServerMemThreshold: 80
  ServerCpuThreshold: 80
  PortalMinReplicas: 1
  PortalMaxReplicas: 1
  PortalMemThreshold: 80
  PortalCpuThreshold: 80
  NEXTAUTH_URL: https://recruiter.sb.tandymgroup.com
  NEXT_PUBLIC_AD_LOGIN: true
  NEXT_PUBLIC_PARSE_RESUME_FROM_MERCURY: true
  NEXT_PUBLIC_APPINSIGHTS_CONNECTION_STRING: 10a2fa35-5df1-4728-b76d-50b7548e507f;IngestionEndpoint=https://eastus-5.in.applicationinsights.azure.com/;LiveEndpoint=https://eastus.livediagnostics.monitor.azure.com/;ApplicationId=8b3040c3-62e0-42e5-9bef-b49ffa496183
  NEXT_PUBLIC_AUTH_URL: recruiter.sb.tandymgroup.com
  CRM_URL: https://tandymgroup-sandbox.crm.dynamics.com
