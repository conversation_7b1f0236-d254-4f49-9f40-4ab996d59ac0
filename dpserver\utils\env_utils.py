import os
import json

class EnvUtils:
    @staticmethod
    def get_clean_app_insights_connection_string():
        """
        Extracts or constructs a clean App Insights connection string.
        Handles raw InstrumentationKey and filters out irrelevant fields.
        """
        full_conn = os.getenv("APP_INSIGHTS_CONNECTION_STRING", "").strip()

        parts = full_conn.split(";")
        key = None
        endpoint = None

        for part in parts:
            if part.startswith("InstrumentationKey="):
                key = part
            elif part.startswith("IngestionEndpoint="):
                endpoint = part
            elif "=" not in part and "-" in part and len(part) > 30:
                # Raw instrumentation key without any prefix
                key = f"InstrumentationKey={part}"

        if key and endpoint:
            return f"{key};{endpoint}"
        else:
            return None
    
    @staticmethod
    def load_config(config_path: str, default_log_name="DEFAULT_LOGGER") -> dict:
        if os.path.exists(config_path):
            with open(config_path, "r") as f:
                return json.load(f)
        else:
            print(f"Config file not found at {config_path}, using defaults.")
            return {
                "logging": {
                    "name": default_log_name
                }
            }
