import { JobLocation } from "@/components/CandidateTable/helper";

export interface Skill {
  name: string;
  weight: "high" | "medium" | "normal";
}

interface ReviewerHistoryItem {
  reviewer: string;
  review_start_timestamp: string; // ISO date string
  review_end_timestamp: string | null;
}

export interface ReviewerConfig {
  current: ReviewerHistoryItem;
  history: ReviewerHistoryItem[];
  review_complete: boolean;
}

export interface Vacancy {
  id: number;
  vacancy_id: string;
  refno: string;
  job_description: string;
  vacancy_data: {
    refno: string;
    "job title": string[];
    vacancy_id: string;
    "soft skills": Skill[];
    "technical skills": Skill[];
    "tools and platforms": Skill[];
    "degrees and certifications": Skill[];
    job_description: string;
    "years of experience": { years: string; weight: string }[];
    "joblocation": JobLocation[]; 
  };
  created_at: string;
  is_locked: boolean;
  locked_by: null | string;
  reviewer_config: ReviewerConfig;
}

export interface RecruiterReview {
  candidate_contact_id: string;
  vacancy_refno: string;
  vote: "like" | "maybe" | "dislike" | null;
  comment: string;
  reviewer_email: string | null;
  feedback_timestamp?: string | null;
}

export interface Candidate {
  id: number;
  vacancy_refno: string;
  candidate_contactid: string;
  candidate_data: {
    email: string;
    name: string;
    contactid: string;
    resume_url: string;
    "classification score": {
      overallscore: number;
      jobtitlescore: number;
      softskillsscore: number;
      "technical skills": number;
      toolsplatformsscore: number;
      "degrees and certifications": number;
      relevantexperiencescore: number;
      overallexperiencescore: number;
      industryexperiencescore: number;
      jobtitle_recency_score: number;
    };
    recruiter_review_decision: RecruiterReview | null;
    current_fitness_reason: CurrentFitnessReason | null;
    availability: string;
    city: string;
    state: string;
    freshness_index: string;
    info_bot_response: string;
    info_bot_response_date: string;
  };
}

export type CurrentFitnessReason = {
  reason: string;
  author: string;
  timestamp: string;
};

export type ResumeData = {
  candidate_id: number;
  status: number;
  description: string;
  candidate: {
    name: string;
    email: string;
    phone: string;
    contact: {
      resume: {
        name: string;
        email: string;
        phone: string;
        easyOCR: boolean;
      };
    };
    created: string;
    md5hash: string;
    modified: string;
    contactid: string;
    failedpdf: string;
    "job title": string[];
    skip_phone: boolean;
    "resume file": string;
    "soft skills": string[];
    "tools and platforms": string[];
    "technical skills": string[];
    "degrees and certifications": string[] | DegreeAndCertification[];
    "work experience": WorkExperience[];
    resume_path_wo_extension: string;
    resume_file: string;
    city: string;
    state: string;
  };
};

type DegreeAndCertification = {
  degree?: string;
  field_of_study?: string;
  school?: string;
  year?: string;
  authority?: string;
  certification?: string;
  url?: string;
};

export type WorkExperience = {
  title: string;
  end_date: string;
  start_date: string;
  company: string;
  description: string;
};

export type WhyFitReasonPayload = {
  candidate_contact_id: string;
  vacancy_refno: string;
  fitness_reason_text: string;
  author_email: string | null;
};
