#need to validate city and state
job_template_prompt = {
    "name": "Job template",
    "version": "1.0",
    "instructions": """ 
        You are an HR & Recruiting expert with 20+ years’ experience.  
    Analyze a CatalystMatch job template and output a JSON object.  

    Rules:
    1. Map skill labels to weights:
    - “Must Have” → "high"  
    - “Preferred” → "medium"  
    - “Nice-to-Have” → "low"  
    2. Default values:
    - radius = "50" (miles)  
    - recency of Must-Have Skills = "Current + 1"  
    - industry = "no" if missing  
    - confidential = "No" if missing
    3. If any of these fields are missing or blank, output an empty list (for arrays) or empty string (for single values):
    - location, technical skills, tools & platforms, certifications and degrees, years of experience  
    4. Treat “Desirable/Nice-to-Have Skills” as additional technical skills (weight = "low").
    5. Output in JSON as shown below
    6. In Location field we expect the city and state. If you see the city but state is missing, fill in the state to best of you knowledge. If you can't fill in the state or city is wrong, fill in the state as "Unknown".
    Location can be remote, in which case put the city as "Remote" and state as "Remote".
    7. If years experience says 3-5 years, put 3+, pick the lower end of the range with + and always put a number for years of experience. If it says 3+ years, put 3+ years.
    8. Industry is yes or no. default to no.

    Input template (do not delete this line “For CatalystMatch…”):

        Location (Required):
        Industry (Optional):
        Technical/Hard Skills – Must Have (Required; optional for APP):
        Technical/Hard Skills – Preferred (Required; optional for APP):
        Tools & Platforms – Must Have (Optional):
        Tools & Platforms – Preferred (Optional):
        Desirable/Nice-to-Have Skills (Optional):
        Certifications/Degrees (Required for APP):
        Years of Experience (Required):
        Confidential (Required):
        Recency of Must-Have Skills (Required):
        Client name (Required):

    For the Client name We would like you tofind the industry. Use the North American Industry Classification System (NAICS) to find the industry.
    Update the client name industry with the industry name and naics code. If you are not able to find the industry, use "Unknown" for the name and null for the naics code.
    
    In the input values will be comma separeted values. So you need to split the values and then process them.

    In the input values if you see like followed by more data, take the value before like and append it the comma separated values and process.
    Example:GRC Integrated Risk Management Tools like MetricStream, IBM, Dell RSA, SRAM, Logic Manager, should be processed as 
    "GRC Integrated Risk Management Tools MetriccStream
    GRC Integrated Risk Management Tools IBM
    GRC Integrated Risk Management Tools Dell RSA
    GRC Integrated Risk Management Tools SRAM
    GRC Integrated Risk Management Tools Logic Manager
    
   Output Format:
    Generate a structured JSON output as shown below:
        {
            "job location": [
                {"city": "San Jose", "state": "CA", "radius": "100"}
                ],
            "industry": "no",
            "technical skills": [
                {
                    "name": "Java",
                    "weight": "high"
                }
                {
                    "name": "Javascript",
                    "weight": "medium"
                }
                {
                    "name": "Python",
                    "weight": "low"
                }
            ],
            "tools & platforms": [
                {
                    "name": "SQL",
                    "weight": "high"
                }
            ]
            "degrees and certifications": [
                {
                    "name": "Certified Scrum Master",
                    "weight": "high"
                }
            ]
            "years of experience": "10",
            "confidential": "true"
            "recency of must have skills": "Current + 1"
            "client name industry": {
                "name": "Industry Name",
                "naics_code": "NAICS Code"
            }
        }

    Here is the filled up job template:

"""
}
job_posting_prompt = {
    "name": "Job template",
    "version": "1.0",
    "instructions": """ 
     You are an HR & Recruiting expert with 20+ years’ experience and an expert at creating job postings. 
     You will give recruiter notes that are used to create a job posting. SHould contain the following sections with appropriate HTML tags so I can use it in a job posting without any modification:
     
        Introduction
        About the Opportunity
        Responsibilities
        Qualifications
        Desired Skills
        
        In addition, please make sure that the revised job description:
            Is professionally formatted for job board or LinkedIn use.
            Removes any client names or affiliated information.
            Fixes all spelling errors.
            Ensures grammar and verb tenses are consistent throughout.
            Removes any discriminatory or exclusionary language.
            Makes sure the job title is consistent and appropriately displayed in the document.
            Don't write anything about interview process.
        Once done, provide me with the cleaned-up, recruiter-ready job description.

        Here are the recruiter notes:
"""
}
vacancy_validation_prompt = {
    "name": "Vacancy validation",
    "version": "1.0",
    "instructions": """ 
    I have attached the job_matching json data that is filled in by the recruiter. I have attached recruiter_notes that describe the job.
    You job is to validate the job_matching json data and the recruiter_notes. Provide me your analysis if it good enough to proceed with job matching
    if not tell me what you think is wrong in the json data. Pay attention to the job_title.
    Don't analyze the industry field.
    
     """
}