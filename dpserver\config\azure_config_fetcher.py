import os
import json
from azure.identity import DefaultAzureCredential
from azure.appconfiguration import AzureAppConfigurationClient
from azure.keyvault.secrets import SecretClient

# Your config_fields remain unchanged
def get_config_fields(app_env):
    # if False:
    #     postgres_variables = [
    #         {
    #             "azure_key": "DB:Url",
    #             "label": "TandymDP",
    #             "is_secret": False,
    #             "env_name": "DB_URL",
    #         },
    #         {
    #             "azure_key": "DB:Port",
    #             "label": "TandymDP",
    #             "is_secret": False,
    #             "env_name": "DB_PORT",
    #         },
    #         {
    #             "azure_key": "DB:Username",
    #             "label": "TandymDP",
    #             "is_secret": False,
    #             "env_name": "DB_USERNAME",
    #         },
    #         {
    #             "azure_key": "DB:Database",
    #             "label": "TandymDP",
    #             "is_secret": <PERSON>alse,
    #             "env_name": "DB_NAME",
    #         },
    #         {
    #             "azure_key": "DB:Password",
    #             "label": "TandymDP",
    #             "is_secret": True,
    #             "env_name": "DB_PASSWORD",
    #         },
    #     ]
    # else:
    #     postgres_variables = [
    #         {
    #             "azure_key": "DBSharedConnectionUrl",
    #             "label": "TandymRecruiterPortal",
    #             "is_secret": False,
    #             "env_name": "DB_URL",
    #         },
    #         {
    #             "azure_key": "DBSharedConnectionPort",
    #             "label": "TandymRecruiterPortal",
    #             "is_secret": False,
    #             "env_name": "DB_PORT",
    #         },
    #         {
    #             "azure_key": "DBSharedConnectionUsername",
    #             "label": "TandymRecruiterPortal",
    #             "is_secret": False,
    #             "env_name": "DB_USERNAME",
    #         },
    #         {
    #             "azure_key": "DBSharedConnectionDatabase",
    #             "label": "TandymRecruiterPortal",
    #             "is_secret": False,
    #             "env_name": "DB_NAME",
    #         },
    #         {
    #             "azure_key": "DBSharedConnectionPassword",
    #             "label": "TandymRecruiterPortal",
    #             "is_secret": True,
    #             "env_name": "DB_PASSWORD",
    #         },
    #     ]

    return [
       
        {
            "azure_key": "AZURE_SQLSERVER_SERVER",
            "label": "TandymRecruiterPortal",
            "is_secret": False,
            "env_name": "AZURE_SQLSERVER_SERVER",
        },
        {
            "azure_key": "AZURE_SQLSERVER_DATABASE",
            "label": "TandymRecruiterPortal",
            "is_secret": False,
            "env_name": "AZURE_SQLSERVER_DATABASE",
        },
        {
            "azure_key": "AZURE_SQLSERVER_TENANTID",
            "label": "TandymRecruiterPortal",
            "is_secret": True,
            "env_name": "AZURE_SQLSERVER_TENANTID",
        },
        {
            "azure_key": "AZURE_SQLSERVER_CLIENTID",
            "label": "TandymRecruiterPortal",
            "is_secret": True,
            "env_name": "AZURE_SQLSERVER_CLIENTID",
        },
        {
            "azure_key": "AZURE_SQLSERVER_CLIENTSECRET",
            "label": "TandymRecruiterPortal",
            "is_secret": True,
            "env_name": "AZURE_SQLSERVER_CLIENTSECRET",
        },
        {
            "azure_key": "AZURE_SHAREPOINT_SITE_URL",
            "label": "TandymRecruiterPortal",
            "is_secret": False,
            "env_name": "AZURE_SHAREPOINT_SITE_URL",
        },
        {
            "azure_key": "AZURE_SHAREPOINT_CLIENT_ID",
            "label": "TandymRecruiterPortal",
            "is_secret": True,
            "env_name": "AZURE_SHAREPOINT_CLIENT_ID",
        },
        {
            "azure_key": "AZURE_SHAREPOINT_CLIENT_SECRET",
            "label": "TandymRecruiterPortal",
            "is_secret": True,
            "env_name": "AZURE_SHAREPOINT_CLIENT_SECRET",
        },
        {
            "azure_key": "AZURE_DATAVERSE_TENANT_ID",
            "label": "TandymRecruiterPortal",
            "is_secret": True,
            "env_name": "AZURE_DATAVERSE_TENANT_ID",
        },
        {
            "azure_key": "AZURE_DATAVERSE_CLIENT_ID",
            "label": "TandymRecruiterPortal",
            "is_secret": True,
            "env_name": "AZURE_DATAVERSE_CLIENT_ID",
        },
        {
            "azure_key": "AZURE_DATAVERSE_CLIENT_SECRET",
            "label": "TandymRecruiterPortal",
            "is_secret": True,
            "env_name": "AZURE_DATAVERSE_CLIENT_SECRET",
        },
        {
            "azure_key": "AZURE_DATAVERSE_RESOURCE_URL",
            "label": "TandymRecruiterPortal",
            "is_secret": False,
            "env_name": "AZURE_DATAVERSE_RESOURCE_URL",
        },
        {
            "azure_key": "AZURE_DATAVERSE_CRM_URL",
            "label": "TandymRecruiterPortal",
            "is_secret": False,
            "env_name": "AZURE_DATAVERSE_CRM_URL",
        },
        {
            "azure_key": "OPENAI_API_KEY",
            "label": "TandymRecruiterPortal",
            "is_secret": True,
            "env_name": "OPENAI_API_KEY",
        },
        {
            "azure_key": "DBSharedConnectionURL",
            "label": "TandymRecruiterPortal",
            "is_secret": False,
            "env_name": "DB_URL",
        },
        {
            "azure_key": "DBSharedConnectionPort",
            "label": "TandymRecruiterPortal",
            "is_secret": False,
            "env_name": "DB_PORT",
        },
        {
            "azure_key": "DBSharedConnectionUsername",
            "label": "TandymRecruiterPortal",
            "is_secret": False,
            "env_name": "DB_USERNAME",
        },
        {
            "azure_key": "DBSharedConnectionDatabase",
            "label": "TandymRecruiterPortal",
            "is_secret": False,
            "env_name": "DB_NAME",
        },
        {
            "azure_key": "DBSharedConnectionPassword",
            "label": "TandymRecruiterPortal",
            "is_secret": True,
            "env_name": "DB_PASSWORD",
        },
         {
            "azure_key": "APP_INSIGHTS_CONNECTION_STRING",
            "label": "TandymRecruiterPortal",
            "is_secret": True,
            "env_name": "APP_INSIGHTS_CONNECTION_STRING",
        }
    ]

def fetch_config_from_azure():
    """Fetch config values from Azure App Configuration and set as env vars."""
    app_config_endpoint = os.getenv("AppConfigurationEndpoint")
    if not app_config_endpoint:
        raise ValueError("❌ AppConfigurationEndpoint not found in environment variables!")

    if os.getenv("APP_ENV", "local") == "local":
        print("⚠️ Local environment detected. Not fetching configurations.")
        return

    credential = DefaultAzureCredential()
    app_config_client = AzureAppConfigurationClient(app_config_endpoint, credential)
    print("***********Starting to fetch config from Azure App Configuration***********")
    
    config_fields = get_config_fields(os.getenv("APP_ENV", "local"))
    for field in config_fields:
        key = field["azure_key"]
        label = field["label"]
        is_secret = field["is_secret"]
        env_name = field["env_name"]
        print("ℹ️ Retrieving value for field: " + key + " " + label)

        setting = app_config_client.get_configuration_setting(key=key, label=label)
        if not setting:
            raise ValueError(f"❌ Setting for key '{key}' with label '{label}' not found.")

        value = setting.value
        print("ℹ️ value of " + key + ": " + str(value))
        if not value:
            raise ValueError(f"❌ Value for key '{key}' is empty.")

        if is_secret:
            try:
                secret_data = json.loads(value)
                secret_uri = secret_data["uri"]
                key_vault_url = "/".join(secret_uri.split("/")[:3])
                secret_client = SecretClient(vault_url=key_vault_url, credential=credential)

                segments = secret_uri.strip("/").split("/")
                secret_name = segments[-1] if segments[-2].lower() == "secrets" else segments[-2]

                value = secret_client.get_secret(secret_name).value
            except Exception as e:
                raise ValueError(f"❌ Failed to fetch secret '{key}': {e}")
        os.environ[env_name] = value
        print(f"✅ {env_name} set {'(secret)' if is_secret else ''}")

