from fastapi import FastAPI
from dp_portal.apis.routes import Routes
from dp_portal.db.postgres_connector import PostgresConnector, PostgresEnvironment
from dp_portal.appLogger import AppLogger
from dp_portal.services.entitlement_service import EntitlementService
import uvicorn
import json
from fastapi.middleware.cors import CORSMiddleware
from dp_portal.services.historical_service import HistoricalService
import os

class AppServer:
    """Encapsulates the FastAPI server setup, database connection, and lifecycle management."""

    def __init__(self, app_config_file):
        """Initialize the FastAPI application and configure lifecycle events."""
        with open(app_config_file, "r") as f:
            config = json.load(f)
        
        self.config = config
              
        db_env = PostgresEnvironment.AZ_APP_VAULT
       
        logger_config = config.get("logging", {})
        self.logger = AppLogger(logger_config)

        app_config = config.get("app", {})
        self.host = app_config.get("host")
        self.port = app_config.get("port")
        self.app = FastAPI(title="Attribute Management API")


        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],  # Change this to your frontend URL in production
            allow_credentials=True,
            allow_methods=["*"],  # Allow all HTTP methods
            allow_headers=["*"],  # Allow all headers
        )
        
        self.db_env = db_env
        self.db_connector = PostgresConnector(self.db_env, logger=self.logger)
        self.db_connector.connect()

        # Initialize services
        self.entitlement_service = EntitlementService(self.logger, self.db_connector)
        self.historical_service = HistoricalService(self.logger, self.db_connector)
        
        # Initialize Routes class and include its router
        self.routes = Routes(self.logger, self.db_connector,self.entitlement_service,self.historical_service)
        self.app.include_router(self.routes.router)

        # Register lifecycle events
        self.app.add_event_handler("startup", self.startup)
        self.app.add_event_handler("shutdown", self.shutdown)

    def startup(self):
        """Initialize database connection when FastAPI starts."""
        self.logger.info("Starting application...")
        
        # Initialize PostgresConnector with logger and environment
        self.db_connector = PostgresConnector(env=self.db_env, logger=self.logger)
        self.db_connector.connect()

    def shutdown(self):
        """Close database connection when FastAPI shuts down."""
        self.logger.info("Shutting down application...")
        if self.db_connector:
            self.db_connector.close()

    def run(self):
        """Run the FastAPI server with Uvicorn."""
        uvicorn.run(self.app, host=self.host, port=self.port)

def test_config():
    db_env = PostgresEnvironment.DEV
    config_path = "dp_portal/config/appconfig.json"

    with open(config_path, "r") as f:
        config = json.load(f)

    db = config.get("db", {})
    env = db.get("env", "DEV")
    print(env)
    if env == "QA":
        db_env = PostgresEnvironment.QA
    if env == "UAT":
        db_env = PostgresEnvironment.UAT
    if env == "PROD":
        db_env = PostgresEnvironment.PROD

    print(db_env)

def test():
    """Test FastAPI startup and shutdown events."""
    config_path = "dp_portal/config/appconfig.json"
    server = AppServer(config_path)  # Using DEV for testing
    logger = server.logger
    
    logger.info("Testing FastAPI server startup...")
    server.startup()
    
    logger.info("Testing database connection...")
    if server.db_connector:
        logger.info("Database connection established successfully.")
    else:
        logger.info("Database connection failed.")
    
    logger.info("Testing FastAPI server shutdown...")
    server.shutdown()
    
    logger.info("Test function executed successfully.")


def main():
    app_config_path = "dp_portal/config/appconfig.json"
    server = AppServer(app_config_path)  # Using DEV for testing
    server.run()

if __name__ == "__main__":
    #test_config()
    #test()
    main()
    pass
    