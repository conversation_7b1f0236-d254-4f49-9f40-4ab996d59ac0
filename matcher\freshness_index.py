from datetime import datetime, timedelta
from typing import Optional, Union
from data_helper.resume_ingestion import ResumeIngestionService
from generator.get_files_from_sharepoint import get_file_from_sharepoint
from common.appLogger import AppLogger, getGlobalAppLogger
from common.db.global_dbconnector import GlobalDBConnector
from common.db.config_postgres import PostgresEnvironment
from common.secrets_env import load_secrets_env_variables
import json
from common.utils.helpers import Helpers

def compute_freshness_index(
    availability_date: Optional[Union[str, datetime]],
    resume_received_date: Optional[Union[str, datetime]]
) -> str:
    """
    Compute freshness index based on:
    - availability_date (High if in future)
    - resume_received_date (High/Medium/Low depending on recency)
    """
    today = datetime.now()

    # Parse strings to datetime if necessary
    if isinstance(availability_date, str):
        try:
            availability_date = datetime.fromisoformat(availability_date)
        except ValueError:
            availability_date = None

    if isinstance(resume_received_date, str):
        try:
            resume_received_date = datetime.fromisoformat(resume_received_date)
        except ValueError:
            resume_received_date = None

    print(f"availability_date: {availability_date}, resume_received_date: {resume_received_date}")

    # Check availability date
    if availability_date and availability_date > today:
        return "High"

    # Check resume received date
    if resume_received_date:
        delta_days = (today - resume_received_date).days
        if delta_days <= 28:
            return "High"
        elif delta_days <= 84:
            return "Medium"
        else:
            return "Low"

    return "Low"


def test_fresh_availability_date():
    today = datetime.now()
    assert compute_freshness_index(
        availability_date=today + timedelta(days=2),
        resume_received_date=None
    ) == "High"

def test_high_resume_received_date():
    today = datetime.now()
    assert compute_freshness_index(
        availability_date=None,
        resume_received_date=today - timedelta(days=10)
    ) == "High"

def test_medium_resume_received_date():
    today = datetime.now()
    assert compute_freshness_index(
        availability_date=None,
        resume_received_date=today - timedelta(days=50)
    ) == "Medium"

def test_low_resume_received_date():
    today = datetime.now()
    assert compute_freshness_index(
        availability_date=None,
        resume_received_date=today - timedelta(days=100)
    ) == "Low"

def test_availability_overrides_resume():
    today = datetime.now()
    assert compute_freshness_index(
        availability_date=today + timedelta(days=5),
        resume_received_date=today - timedelta(days=100)
    ) == "High"

def run_freshness_tests():
    test_fresh_availability_date()
    test_high_resume_received_date()
    test_medium_resume_received_date()
    test_low_resume_received_date()
    test_availability_overrides_resume()
    print("✅ Freshness logic passed all tests")

if __name__ == "__main__":
    load_secrets_env_variables()
    logger_config = {
        "log_level": "DEBUG",
        "log_to_stdout": True,
    }
    logger = AppLogger(logger_config)

    logger.info("--------------------------------")
    run_freshness_tests()
