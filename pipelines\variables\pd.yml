variables:
  WebLBIP: ************
  ServerLBIP: ************
  PortalLBIP: ************
  ReactAppEnv: pd
  WebMinReplicas: 2
  WebMaxReplicas: 4
  WebMemThreshold: 80
  WebCpuThreshold: 80
  ServerMinReplicas: 2
  ServerMaxReplicas: 4
  ServerMemThreshold: 80
  ServerCpuThreshold: 80
  PortalMinReplicas: 2
  PortalMaxReplicas: 4
  PortalMemThreshold: 80
  PortalCpuThreshold: 80
  NEXTAUTH_URL: https://recruiter.tandymgroup.com
  NEXT_PUBLIC_AD_LOGIN: true
  NEXT_PUBLIC_PARSE_RESUME_FROM_MERCURY: true
  NEXT_PUBLIC_APPINSIGHTS_CONNECTION_STRING: cb49f8e1-9368-42f9-95d6-5ab16b42c75a;IngestionEndpoint=https://eastus-0.in.applicationinsights.azure.com/;LiveEndpoint=https://eastus.livediagnostics.monitor.azure.com/;ApplicationId=1fe4a6f4-7eee-4cbe-9e7c-ed77e57398bf
  NEXT_PUBLIC_AUTH_URL: recruiter.tandymgroup.com
  CRM_URL: https://tandymgroup.crm.dynamics.com
