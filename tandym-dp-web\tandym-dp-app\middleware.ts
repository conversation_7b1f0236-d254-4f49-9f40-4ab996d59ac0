import { NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";
import type { NextRequest } from "next/server";
import { isADLogin } from "@/api/config";

export async function middleware(req: NextRequest) {
  const url = req.nextUrl;
  const pathname = url.pathname;
  const isADlogin = isADLogin();

  const isCandidateTuning = pathname.startsWith(
    "/CandidateTuning/For_Mercury_Portal"
  );
  const referer = req.headers.get("referer") || "";
  const isFromCRM = referer.startsWith(process.env.CRM_URL || "");
  // Skip AD auth if accessed from iframe or CRM referer
 if (isCandidateTuning && (isFromCRM)) {
  return NextResponse.next();
}

  // Token fetched once
  const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });

  const protectedRoutes = [
    "/",
    "/candidates",
    "/jobs",
    "/skills-editor",
    "/workforce-index",
    "/vacancy",
    "/experiments",
    "/subcategory-config",
    // "/subcategory-config",
    "/CandidateTuning/For_Mercury_Portal",
  ];

  const isProtected = protectedRoutes.some((path) => pathname.startsWith(path));

  if (isADlogin && isProtected) {
    // if (isCandidateTuning) {
    //   const noAccessUrl = new URL("/no-access", req.url);
    //   return NextResponse.redirect(noAccessUrl);
    // }
    if (!token) {
      // Avoid recursive callback loops by using the actual page URL
      const signInUrl = new URL("/api/auth/signin/azure-ad", req.url);
      signInUrl.searchParams.set("callbackUrl", req.url);

      const response = NextResponse.redirect(signInUrl);

      return response;
    }
  }

  // Default fallback
  return NextResponse.next();
}

export const config = {
  matcher: [
    "/",
    "/candidates",
    "/jobs",
    "/skills-editor",
    "/workforce-index",
    "/vacancy",
    "/vacancy/:path*",
    "/experiments",
    // "/subcategory-config",
    "/CandidateTuning/For_Mercury_Portal",
  ], // Add other protected routes
};
