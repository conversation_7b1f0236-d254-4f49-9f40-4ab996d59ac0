from common.db.postgres_connector import PostgresConnector
from common.appLogger import A<PERSON><PERSON>ogger
import random
import hashlib
import uuid
import json
import os
from datetime import datetime, timezone
from dpserver.utils.utils import read_latest_json_files_from_dir
from dpserver.utils.utils import read_json_file
import psycopg2 # Ensure psycopg2 is imported for error handling if not already
from matcher.freshness_index import compute_freshness_index
from dataverse_helper.token_manager import get_token_for_env, get_dataverse_credentials_for_env, Environment
from dataverse_helper.dv_common import read_fields_from_dataverse
from dataverse_helper.dv_vacancy import VacancyDataverseHelper
from common.CustomExceptions import DataverseError

class VacancyService:
    def __init__(self, logger: AppLogger, db_connector: PostgresConnector, env: Environment = Environment.PROD):
        """Initialize database connection using environment-specific credentials."""
        self.logger = logger
        self.db = db_connector
        self.schema = self.db.schema
        self.token = get_token_for_env(env, logger=self.logger)
        self.credentials = get_dataverse_credentials_for_env(env, logger=self.logger)
        self.dataverse_url = self.credentials["RESOURCE_URL"]
        self.vacancy_dataverse_helper = VacancyDataverseHelper(self.token, self.dataverse_url, self.logger)

    def _get_active_db_connection(self):
        """
        Ensures an active DB connection is available, attempting to (re)connect if necessary.
        This logic is now within the service, using the provided PostgresConnector.
        """
        conn = self.db.connection
        if not conn or conn.closed:
            self.logger.info(f"DB connection for {self.db.config.get('dbname', 'N/A')} is None or closed. Attempting to connect.")
            conn = self.db.connect() # connect() returns None on failure in your PostgresConnector
            if not conn: # Check if connect() failed
                self.logger.error(f"Failed to establish initial DB connection to {self.db.config.get('dbname', 'N/A')}.")
                raise Exception("Database service unavailable at the moment.")
        else:
            # If connection exists, try a quick check
            try:
                with conn.cursor() as cur_ping:
                    cur_ping.execute("SELECT 1")
                self.logger.debug(f"Existing DB connection to {self.db.config.get('dbname', 'N/A')} is active.")
            except (psycopg2.OperationalError, psycopg2.InterfaceError) as ping_error:
                self.logger.warning(f"DB connection test failed for {self.db.config.get('dbname', 'N/A')} ('{ping_error}'). Attempting to reconnect.")
                try:
                    self.db.close() # Close the potentially stale connection
                except Exception as close_e:
                    self.logger.warning(f"Error closing stale DB connection: {close_e}")
                conn = self.db.connect() # Attempt to reconnect
                if not conn: # Check if reconnect failed
                    self.logger.error(f"Failed to re-establish DB connection to {self.db.config.get('dbname', 'N/A')}.")
                    raise Exception("Database service unavailable after reconnect attempt.")
        
        # Final check, though self.db.connect() returning None should be caught above
        if not conn or conn.closed:
             self.logger.critical(f"Unable to obtain an active DB connection to {self.db.config.get('dbname', 'N/A')}.")
             raise Exception("Critical: Database connection could not be established.")
        return conn


    def get_vacancies(self):
        """Fetch all vacancies from the database."""
        self.logger.info("Fetching vacancies from the database.")
        self.db.connect()
        conn = self._get_active_db_connection()
        try:
            with conn.cursor() as cur: # Use 'with' statement for cursor management
                query = f"""
                    SELECT 
                        vacancy_id, 
                        subcategory, 
                        refno, 
                        vacancy_data,
                        reviewer_config,
                        is_locked,
                        locked_by,
                        archived
                    FROM {self.schema}.vacancy_shortlist_processed
                    WHERE archived = FALSE;
                """
                cur.execute(query)
                rows = cur.fetchall()
            
                # Extract column names dynamically
                col_names = [desc[0] for desc in cur.description] if cur.description else []
            
                # Convert to list of dictionaries
                vacancies = [dict(zip(col_names, row)) for row in rows] if col_names else []

                # Process each vacancy
                for vacancy in vacancies:
                    # Remove refno from vacancy_data if it exists
                    if isinstance(vacancy.get("vacancy_data"), dict):
                        if "refno" in vacancy["vacancy_data"]:
                            del vacancy["vacancy_data"]["refno"]
                        if "vacancy_id" in vacancy["vacancy_data"]:
                            del vacancy["vacancy_data"]["vacancy_id"]
                
                    # Ensure reviewer_config is properly formatted
                    if vacancy.get("reviewer_config") is None:
                        vacancy["reviewer_config"] = {
                            "current": {},
                            "history": [],
                            "review_complete": False
                        }
                            
            return {"vacancies": vacancies}
        except (psycopg2.Error, Exception) as e: # Catch specific psycopg2 errors and general exceptions
            self.logger.error(f"Error fetching vacancies: {e}", exc_info=True)
            # No conn.rollback() needed here if operations are read-only or handled by 'with conn:'
            # However, if _get_active_db_connection raises HTTPException, it will be caught by FastAPI
            # For other DB errors, re-raise as HTTPException or return error response
            raise Exception(f"Error fetching vacancies: {str(e)}")
        # 'finally' block for cur.close() is not needed due to 'with' statement for cursor
        # Connection closing is handled by the PostgresConnector's lifecycle or higher up

    def get_vacancies_from_files(self):
        """Fetch all vacancies from the files and enrich with recruiter review decisions."""
        self.logger.info("Fetching vacancies from the files.")
        try:
            dir_path = "/mnt/incoming/match-results/openai-match-results"
            vacancy_dir = "/mnt/incoming/vacancy-prod"

            # Path to recruiter review decision file (migrated version)
            review_file_path = "/mnt/incoming/recruiter-ui/vacancies/resumes/reviews"

            # Load recruiter review decisions from the migrated file
            recruiter_reviews = {}
            if os.path.exists(review_file_path):
                with open(review_file_path, "r") as file:
                    recruiter_reviews = json.load(file)

            if not isinstance(recruiter_reviews, dict):
                recruiter_reviews = {}  # Ensure it's a dictionary structure

            # Read vacancies data
            response = read_latest_json_files_from_dir(dir_path)

            for item in response:
                # Enrich vacancy data
                vacancy_id = item.get("vacancy", {}).get("id")
                vacancy_ref = item.get("vacancy", {}).get("refno")
                if vacancy_id:
                    vacancy_file_path = os.path.join(vacancy_dir, f"{vacancy_id}.json")
                    vacancy_data = read_json_file(vacancy_file_path)  # Fetching vacancy details
                    if vacancy_data:
                        item["vacancy"]["vacancy_data"] = vacancy_data
                        item["vacancy"]["refno"] = vacancy_ref
                        item["vacancy"]["vacancy_id"] = vacancy_id

                # Modify candidate objects and add reviews
                if "candidates" in item and isinstance(item["candidates"], list):
                    modified_candidates = []
                    for index, candidate in enumerate(item["candidates"]):
                        candidate_contactid = str(candidate.get("contactid")).strip().lower()

                        # Fetch review decision if available
                        review_decision = None
                        if vacancy_ref in recruiter_reviews and candidate_contactid in recruiter_reviews[vacancy_ref]:
                            review_decision = recruiter_reviews[vacancy_ref][candidate_contactid]

                        modified_candidate = {
                            "id": index + 1,
                            "vacancy_refno": item["vacancy"]["refno"],
                            "candidate_contactid": candidate_contactid,
                            "candidate_data": {
                                "email": candidate.get("email"),
                                "contactid": candidate_contactid,
                                "resume_url": candidate.get("resume_url"),
                                "classification score": {
                                    "overallscore": candidate.get("classification score", {}).get("overallscore", 0),
                                    "jobtitlescore": candidate.get("classification score", {}).get("jobtitlescore", 0),
                                    "softskillsscore": candidate.get("classification score", {}).get("softskillsscore", 0),
                                    "technical skills": candidate.get("classification score", {}).get("technical skills", 0),
                                    "toolsplatformsscore": candidate.get("classification score", {}).get("toolsplatformsscore", 0),
                                    "degrees and certifications": candidate.get("classification score", {}).get("degrees and certifications", 0),
                                    "relevantexperiencescore": candidate.get("classification score", {}).get("relevantexperiencescore", 0),
                                    "overallexperiencescore": candidate.get("classification score", {}).get("overallexperiencescore", 0),
                                    "industryexperiencescore": candidate.get("classification score", {}).get("industryexperiencescore", 0)
                                },
                                "recruiter_review_decision": review_decision
                            }
                        }
                        modified_candidates.append(modified_candidate)

                    item["candidates"] = modified_candidates

            return {"vacancies": response}

        except Exception as e:
            self.logger.error(f"Error fetching vacancies files: {e}")
            return {"error": "Error fetching vacancies files"}

    def get_candidate_resume_from_file(self,contact_id: str):
        """Fetch candidate resume from the file."""
        self.logger.info("Fetching candidate resume from the file.{contact_id}")
        try:
            # contact_id
            file_path = "/mnt/incoming/classify-prod/" + contact_id + '.json'
            response = read_json_file(file_path)
            return {"candidate": response}
        except Exception as e:
            self.logger.error(f"Error fetching candidate resume: {e}")
            return {"error": "Error fetching candidate resume"}

    def get_candidates_by_vacancy_id(self, vacancy_id: str, reviewer_email: str):
        """Fetch candidates by vacancy id from the database using new tables."""
        self.logger.info(f"Fetching candidates for vacancy_id: {vacancy_id} using new table structure.")
        self.db.connect()
        conn = self._get_active_db_connection()
        try:
            cur = conn.cursor()
            # Query to join candidate_application_shortlists with candidates,
            # vacancy_shortlist_processed, candidate_application_shortlists_feedback,
            # and candidate_application_shortlists_detail.
            # Added condition to fetch only non-archived candidates.
            # Added info_bot_response, info_bot_response_date, and shortlisted from casd
            query = f"""
            SELECT
                cas.contact_id AS candidate_contactid,
                c.email,
                c.name AS candidate_name,
                c.resume_data,
                cas.category_intermediate_scores,
                casf.feedbacks AS feedbacks,
                casd.fitness_reason,
                casd.info_bot_response,
                casd.info_bot_response_date,
                casd.shortlisted,
                cas.calculated_normalized_score,
                cas.total_intermediate_score,
                vsp.refno AS vacancy_refno
            FROM
                {self.schema}.candidate_application_shortlists cas
            JOIN
                {self.schema}.candidates c ON cas.contact_id = c.contact_id
            LEFT JOIN
                {self.schema}.candidate_application_shortlists_feedback casf ON cas.vacancy_id = casf.vacancy_id AND cas.contact_id = casf.contact_id
            LEFT JOIN
                {self.schema}.candidate_application_shortlists_detail casd ON cas.vacancy_id = casd.vacancy_id AND cas.contact_id = casd.contact_id
            LEFT JOIN
                {self.schema}.vacancy_shortlist_processed vsp ON cas.vacancy_id = vsp.vacancy_id
            WHERE
                cas.vacancy_id = %s::uuid AND cas.archived = FALSE
            ORDER BY
                cas.calculated_normalized_score DESC;
            """
            cur.execute(query, (vacancy_id,))
            rows = cur.fetchall()
            
            if not rows or cur.description is None:
                return {
                    "candidates": [],
                    "system_data_retrieval_time": datetime.now(timezone.utc).strftime('%Y-%m-%dT%H:%M:%S.%fZ')
                }

            col_names = [desc[0] for desc in cur.description]
            
            processed_candidates = []
            for idx, row_data in enumerate(rows):
                raw_candidate = dict(zip(col_names, row_data))
                
                candidate_contactid = str(raw_candidate['candidate_contactid'])
                resume_data_json = raw_candidate.get('resume_data') or {}
                
                city = resume_data_json.get('city', "")
                state = resume_data_json.get('state', "")
                availability = resume_data_json.get('availability', "")
                placement_status = resume_data_json.get('placement_status', "")
                
                category_scores = raw_candidate.get('category_intermediate_scores') or {}
                feedbacks_list = raw_candidate.get('feedbacks') or []
                
                first_feedback_data = None
                if feedbacks_list and len(feedbacks_list) > 0:
                    if reviewer_email:
                        for feedback_entry in feedbacks_list:
                            if feedback_entry.get("reviewer_email") == reviewer_email:
                                first_feedback_data = feedback_entry
                                break
                    else: 
                        first_feedback_data = feedbacks_list[0]

                recruiter_review_decision = {
                    "candidate_contact_id": candidate_contactid,
                    "vote": first_feedback_data.get("vote") if first_feedback_data else None,
                    "comment": first_feedback_data.get("comment", "") if first_feedback_data else "",
                    "reviewer_email": first_feedback_data.get("reviewer_email") if first_feedback_data else None,
                    "feedback_timestamp": first_feedback_data.get("feedback_date") if first_feedback_data else None
                }

                current_fitness_reason_obj = None 
                fitness_reason_json = raw_candidate.get('fitness_reason')
                if fitness_reason_json and isinstance(fitness_reason_json, dict):
                    current_fitness_info = fitness_reason_json.get('current')
                    if current_fitness_info and isinstance(current_fitness_info, dict):
                        current_fitness_reason_obj = {
                            "reason": current_fitness_info.get('reason'),
                            "author": current_fitness_info.get('author'),
                            "timestamp": current_fitness_info.get('timestamp')
                        }
                
                shortlisted_json = raw_candidate.get('shortlisted') or {
                    "status": "",
                    "shortlisted_at": "",
                    "shortlisted_by": "",
                    "crimson_vacancycandidateid": ""
                }
                
                classification_score = {
                    "overallscore": float(raw_candidate.get('calculated_normalized_score', 0.0) or 0.0),
                    "jobtitlescore": float(category_scores.get("job_titles", 0.0) or 0.0),
                    "softskillsscore": float(category_scores.get("soft_skills", 0.0) or 0.0),
                    "technical skills": float(category_scores.get("technical_skills", 0.0) or 0.0),
                    "toolsplatformsscore": float(category_scores.get("tools_platforms", 0.0) or 0.0),
                    "degrees and certifications": float(category_scores.get("degrees_certs", 0.0) or 0.0),
                    "relevantexperiencescore": 0.0,
                    "jobtitle_recency_score": float(category_scores.get("job_title_recency", 0.0) or 0.0),
                    "overallexperiencescore": 0.0,
                    "industryexperiencescore": float(category_scores.get("industry_relevance", 0.0) or 0.0)
                }
                
                # Explicitly default to None if the keys are missing or their values are NULL in the database.
                info_bot_response = raw_candidate.get('info_bot_response', None)
                info_bot_response_date = raw_candidate.get('info_bot_response_date', None)
                if isinstance(info_bot_response_date, datetime): # Ensure date is stringified
                    info_bot_response_date = info_bot_response_date.isoformat()

                availability = resume_data_json.get('availability')
                modified = resume_data_json.get('modified')
                
                freshness_index = compute_freshness_index(
                    availability_date=availability,
                    resume_received_date=modified
                )
                
                candidate_data_dict = {
                    "name": raw_candidate.get('candidate_name'),
                    "email": raw_candidate.get('email'),
                    "contactid": candidate_contactid,
                    "resume_url": None, 
                    "classification score": classification_score,
                    "recruiter_review_decision": recruiter_review_decision,
                    "current_fitness_reason": current_fitness_reason_obj,
                    "shortlisted_details": shortlisted_json,  # Include full shortlist details
                    "city": city,
                    "state": state,
                    "availability": availability,
                    "freshness_index": freshness_index,
                    "info_bot_response": info_bot_response,
                    "info_bot_response_date": info_bot_response_date,
                    "placement_status": placement_status
                }
                
                processed_candidates.append({
                    "id": idx + 1, # Sequential ID for the list
                    "vacancy_refno": raw_candidate.get('vacancy_refno'),
                    "candidate_contactid": candidate_contactid,
                    "candidate_data": candidate_data_dict
                })
            
            cur.close()
            return {"candidates": processed_candidates, "system_data_retrieval_time": datetime.now(timezone.utc).strftime('%Y-%m-%dT%H:%M:%S.%fZ')}

        except Exception as e:
            self.logger.error(f"Error fetching candidates: {e}", exc_info=True)
            if conn:
                conn.rollback()
            return {"error": "Error fetching candidates"}
        
    def update_candidate_decision(self, data: dict):
        self.logger.info(f"Update review data for candidate: {data}")
        self.file_path = "/mnt/incoming/recruiter-ui/vacancies/resumes/reviews"

        # Ensure the file exists
        directory = os.path.dirname(self.file_path)
        if not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)

        # Ensure the file exists
        if not os.path.exists(self.file_path):
            with open(self.file_path, "w") as file:
                json.dump({}, file)  # Initialize with an empty dictionary

        candidate_contactid = data.get("candidate_contact_id")
        hiring_decision = data.get("hiring_decision")
        review_message = data.get("review_message")
        recruiter_email = data.get("recruiter_email")
        vacancy_refno = data.get("vacancy_refno")  # Newly added field for vacancy reference number

        if not candidate_contactid or not vacancy_refno:
            self.logger.error("Error: candidate_contact_id and vacancy_refno are required")
            return {"error": "candidate_contact_id and vacancy_refno are required"}

        new_entry = {
            "candidate_contact_id": candidate_contactid,
            "hiring_decision": hiring_decision,
            "review_message": review_message,
            "recruiter_email": recruiter_email,
        }

        try:
            # Read the existing migrated data
            with open(self.file_path, "r") as file:
                existing_data = json.load(file)

            # Ensure existing_data is a dictionary
            if not isinstance(existing_data, dict):
                existing_data = {}

            # Check if the vacancy refno exists in the nested data
            if vacancy_refno not in existing_data:
                self.logger.info(f"Vacancy with refno {vacancy_refno} not found, creating a new entry.")
                existing_data[vacancy_refno] = {}  # Create a new object for this vacancy_refno

            # Update the candidate review for the specified vacancy refno
            existing_data[vacancy_refno][candidate_contactid] = new_entry

            # Write back the updated data
            with open(self.file_path, "w") as file:
                json.dump(existing_data, file, indent=4)

            self.logger.info(f"Candidate {candidate_contactid} updated successfully in vacancy {vacancy_refno}.")

            return {"message": "Candidate updated successfully"}

        except Exception as e:
            self.logger.error(f"Error updating candidate: {e}")
            return {"error": "Error updating candidate"}

    def update_candidate_decision_in_db(self, data: dict):
        """Update candidate decision with review lock validation."""
        self.logger.info(f"Updating candidate decision in DB: {data}")
        
        candidate_contact_id_str = data.get("candidate_contact_id")
        vote = data.get("vote")  
        comment = data.get("comment")   
        reviewer_email = data.get("reviewer_email") 
        vacancy_refno = data.get("vacancy_refno")

        if not candidate_contact_id_str or not vacancy_refno:
            self.logger.error("Error: candidate_contact_id and vacancy_refno are required")
            return {"error": "candidate_contact_id and vacancy_refno are required", "status_code": 400}

        if not reviewer_email:
            self.logger.error("Error: reviewer_email is required for feedback")
            return {"error": "reviewer_email is required", "status_code": 400}

        try:
            candidate_contact_id = uuid.UUID(candidate_contact_id_str)
        except ValueError:
            self.logger.error(f"Error: Invalid candidate_contact_id format: {candidate_contact_id_str}")
            return {"error": f"Invalid candidate_contact_id format: {candidate_contact_id_str}", "status_code": 400}

        self.db.connect()
        conn = self._get_active_db_connection()
        cur = None # Ensure cur is defined for the finally block
        try:
            cur = conn.cursor()

            # Step 1: Get vacancy_id and check review status from vacancy_shortlist_processed
            cur.execute(
                f"""
                SELECT vacancy_id, is_locked, locked_by, reviewer_config 
                FROM {self.schema}.vacancy_shortlist_processed 
                WHERE refno = %s
                FOR UPDATE;
                """,
                (vacancy_refno,)
            )
            vacancy_row = cur.fetchone()
            if not vacancy_row:
                self.logger.error(f"Error: Vacancy with refno {vacancy_refno} not found.")
                conn.rollback() # Rollback as we started a transaction with FOR UPDATE
                return {"error": f"Vacancy with refno {vacancy_refno} not found.", "status_code": 404}
                
            vacancy_id, is_locked, locked_by, reviewer_config = vacancy_row

            # Step 2: Create the new feedback object
            new_feedback = {
                "reviewer_email": reviewer_email,
                "vote": vote,
                "comment": comment,
                "feedback_date": datetime.now(timezone.utc).isoformat()
            }

            # Step 3: Fetch existing feedbacks from the new feedback table and append/update
            query_fetch_feedbacks_new_table = f"""
                SELECT feedbacks
                FROM {self.schema}.candidate_application_shortlists_feedback
                WHERE vacancy_id = %s::uuid AND contact_id = %s::uuid
                FOR UPDATE;
            """
            cur.execute(query_fetch_feedbacks_new_table, (str(vacancy_id), str(candidate_contact_id)))
            feedback_row = cur.fetchone()

            existing_feedbacks_list = []
            if feedback_row and feedback_row[0] is not None:
                existing_feedbacks_list = feedback_row[0]
                if not isinstance(existing_feedbacks_list, list):
                    self.logger.warning(f"Feedbacks from new table for {vacancy_id}, {candidate_contact_id} was not a list: {existing_feedbacks_list}. Resetting to list.")
                    existing_feedbacks_list = []
            
            feedback_updated_in_list = False
            for i, feedback_entry in enumerate(existing_feedbacks_list):
                if feedback_entry.get("reviewer_email") == reviewer_email:
                    existing_feedbacks_list[i] = new_feedback
                    feedback_updated_in_list = True
                    self.logger.info(f"Updating existing feedback for reviewer {reviewer_email} in new feedback table.")
                    break
            
            if not feedback_updated_in_list:
                # If no existing feedback for this reviewer, insert the new feedback (Bug 4006)
                existing_feedbacks_list.insert(0, new_feedback)
                self.logger.info(f"Inserting new feedback for reviewer {reviewer_email} to new feedback table at the beginning.")

            # Step 4: Update/Insert the feedbacks in the new candidate_application_shortlists_feedback table
            query_upsert_feedbacks_new_table = f"""
                INSERT INTO {self.schema}.candidate_application_shortlists_feedback
                    (vacancy_id, contact_id, feedbacks, feedbacks_json_version, updated_at)
                VALUES
                    (%s::uuid, %s::uuid, %s::jsonb, 0.1, CURRENT_TIMESTAMP)
                ON CONFLICT (vacancy_id, contact_id) DO UPDATE SET
                    feedbacks = EXCLUDED.feedbacks,
                    feedbacks_json_version = EXCLUDED.feedbacks_json_version,
                    updated_at = CURRENT_TIMESTAMP;
            """
            cur.execute(query_upsert_feedbacks_new_table, (
                str(vacancy_id),
                str(candidate_contact_id),
                json.dumps(existing_feedbacks_list)
            ))
            self.logger.info(f"Feedback successfully upserted into {self.schema}.candidate_application_shortlists_feedback.")

            # Step 5: Update the 'updated_at' timestamp in the original candidate_application_shortlists table
            # and get application_id. We no longer update 'feedbacks' column here.
            query_update_original_table_timestamp = f"""
                UPDATE {self.schema}.candidate_application_shortlists
                SET updated_at = CURRENT_TIMESTAMP
                WHERE vacancy_id = %s::uuid AND contact_id = %s::uuid
                RETURNING application_id;
            """
            cur.execute(query_update_original_table_timestamp, (str(vacancy_id), str(candidate_contact_id)))
            updated_row_original_table = cur.fetchone()

            if updated_row_original_table:
                conn.commit()
                self.logger.info(f"Candidate decision meta-data (timestamp) updated successfully in DB for contact_id {candidate_contact_id} in vacancy_id {vacancy_id}.")
                return {"message": "Candidate decision updated successfully in DB", "application_id": str(updated_row_original_table[0])}
            else:
                # This case implies the (vacancy_id, contact_id) pair didn't exist in candidate_application_shortlists.
                # This is an issue, as feedback is tied to an application.
                conn.rollback()
                self.logger.error(f"Error: No record found in candidate_application_shortlists to update for vacancy_id {vacancy_id} and contact_id {candidate_contact_id}.")
                return {"error": f"No base application record found for vacancy_id {vacancy_id} and contact_id {candidate_contact_id}.", "status_code": 404}

        except Exception as e:
            if conn:
                conn.rollback()
            self.logger.error(f"Error updating candidate decision in DB: {e}")
            # Consider if exposing raw str(e) is safe or if a generic message is better for production.
            return {"error": f"Error updating candidate decision in DB: {str(e)}", "status_code": 500}
        finally:
            if cur:
                cur.close()

    def update_candidate_fitness_reason(self, data: dict):
        """Update, set, or clear the fitness reason for a candidate in a vacancy."""
        self.logger.info(f"Updating fitness reason: {data}")

        vacancy_refno = data.get("vacancy_refno")
        candidate_contact_id_str = data.get("candidate_contact_id")
        fitness_reason_text = data.get("fitness_reason_text") # New reason, or None/empty to clear
        author_email = data.get("author_email")

        if not vacancy_refno or not candidate_contact_id_str or not author_email:
            self.logger.error("Missing required fields: vacancy_refno, candidate_contact_id, author_email")
            return {
                "error": "vacancy_refno, candidate_contact_id, and author_email are required",
                "status_code": 400
            }

        try:
            candidate_contact_id = uuid.UUID(candidate_contact_id_str)
        except ValueError:
            self.logger.error(f"Invalid candidate_contact_id format: {candidate_contact_id_str}")
            return {
                "error": f"Invalid candidate_contact_id format: {candidate_contact_id_str}",
                "status_code": 400
            }

        self.db.connect()
        conn = self._get_active_db_connection()
        cur = None
        try:
            cur = conn.cursor()

            cur.execute(
                f"""
                SELECT vacancy_id
                FROM {self.schema}.vacancy_shortlist_processed
                WHERE refno = %s;
                """,
                (vacancy_refno,)
            )
            vacancy_row = cur.fetchone()
            if not vacancy_row:
                self.logger.error(f"Vacancy with refno {vacancy_refno} not found.")
                return {"error": f"Vacancy with refno {vacancy_refno} not found.", "status_code": 404}
            vacancy_id = vacancy_row[0]

            cur.execute(
                f"""
                SELECT fitness_reason FROM {self.schema}.candidate_application_shortlists_detail
                WHERE vacancy_id = %s::uuid AND contact_id = %s::uuid
                FOR UPDATE;
                """,
                (str(vacancy_id), str(candidate_contact_id))
            )
            detail_row = cur.fetchone()

            existing_fitness_json = None
            if detail_row and detail_row[0]:
                existing_fitness_json = detail_row[0]

            current_time = datetime.now(timezone.utc).isoformat()
            history_list = []

            if existing_fitness_json:
                current_entry_in_db = existing_fitness_json.get("current")
                history_from_db = existing_fitness_json.get("history", [])
                if current_entry_in_db and isinstance(current_entry_in_db, dict) and current_entry_in_db.get("reason") is not None:
                    # Check if current_entry_in_db is not an empty object before adding to history
                    if current_entry_in_db: # Ensures it's not {}
                        history_list.append(current_entry_in_db)
                history_list.extend(history_from_db)

            new_current_reason_obj = {} # Default to empty object for clearing
            is_clearing_action = not (fitness_reason_text and fitness_reason_text.strip())

            if is_clearing_action:
                # If clearing, current becomes empty. Add a "deleted" event to history.
                # This "deleted" event is added *after* the actual (now previous) current reason was moved.
                deleted_event_history_entry = {
                    "author": author_email,
                    "timestamp": current_time,
                    "reason": f"{author_email} deleted the why fit reason."
                }
                history_list.insert(0, deleted_event_history_entry) # Add to the beginning of history
            else:
                # If adding or updating, set the new current object
                new_current_reason_obj = {
                    "author": author_email,
                    "timestamp": current_time,
                    "reason": fitness_reason_text
                }
            
            final_fitness_reason_to_store = {
                "current": new_current_reason_obj,
                "history": history_list
            }
            
            fitness_reason_json_version = 0.1

            upsert_query = f"""
                INSERT INTO {self.schema}.candidate_application_shortlists_detail
                    (vacancy_id, contact_id, fitness_reason, fitness_reason_json_version, created_at, updated_at)
                VALUES
                    (%s::uuid, %s::uuid, %s::jsonb, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                ON CONFLICT (vacancy_id, contact_id) DO UPDATE SET
                    fitness_reason = EXCLUDED.fitness_reason,
                    fitness_reason_json_version = EXCLUDED.fitness_reason_json_version,
                    updated_at = CURRENT_TIMESTAMP
                RETURNING vacancy_id, contact_id;
            """
            cur.execute(upsert_query, (
                str(vacancy_id),
                str(candidate_contact_id),
                json.dumps(final_fitness_reason_to_store),
                fitness_reason_json_version
            ))
            
            upserted_row = cur.fetchone()

            if upserted_row:
                conn.commit()
                action_message = "cleared" if is_clearing_action else "updated"
                self.logger.info(f"Fitness reason {action_message} successfully for vacancy_id {vacancy_id}, contact_id {candidate_contact_id}.")
                return {
                    "message": f"Fitness reason {action_message} successfully",
                    "vacancy_id": str(upserted_row[0]),
                    "contact_id": str(upserted_row[1]),
                    "status_code": 200
                }
            else:
                conn.rollback()
                self.logger.error("Failed to upsert fitness reason, no row returned.")
                return {"error": "Failed to update fitness reason", "status_code": 500}

        except Exception as e:
            if conn:
                conn.rollback()
            self.logger.error(f"Error updating fitness reason: {e}", exc_info=True)
            return {"error": f"An unexpected error occurred: {str(e)}", "status_code": 500}
        finally:
            if cur:
                cur.close()

    def start_vacancy_review(self, vacancy_id: str, reviewer_email: str):
        """Start a review process for a vacancy by locking it for the specified reviewer."""
        self.logger.info(f"Starting review process for vacancy {vacancy_id} by reviewer {reviewer_email}")
        self.db.connect()
        conn = self._get_active_db_connection()
        try:
            cur = conn.cursor()
            
            # Validate email domain
            if not reviewer_email.endswith("@tandymgroup.com"):
                return {
                    "error": "Invalid reviewer email domain",
                    "status_code": 400,
                    "details": "Reviewer email must be from tandymgroup.com domain"
                }
            
            # First check if vacancy exists and get current state
            # Using FOR UPDATE SKIP LOCKED to avoid waiting for other transactions
            cur.execute(
                f"""
                SELECT reviewer_config, is_locked, locked_by 
                FROM {self.schema}.vacancy_shortlist_processed 
                WHERE vacancy_id = %s::uuid
                FOR UPDATE SKIP LOCKED;
                """,
                (vacancy_id,)
            )
            row = cur.fetchone()
            
            if not row:
                return {"error": f"Vacancy {vacancy_id} not found", "status_code": 404}
                
            current_config, is_locked, locked_by = row
            
            # Check if vacancy is already locked (read-only)
            if is_locked:
                return {
                    "error": "Vacancy is locked and cannot be reviewed",
                    "status_code": 409,
                    "details": "This vacancy has been completed and locked. Please use the claim endpoint to start a new review process."
                }
                
            # Check if someone else is reviewing
            if current_config and current_config.get("current", {}).get("reviewer"):
                current_reviewer = current_config["current"]["reviewer"]
                if current_reviewer != reviewer_email:
                    return {
                        "error": f"Vacancy is currently being reviewed by {current_reviewer}",
                        "status_code": 409,
                        "details": f"Another reviewer {current_reviewer} has an active review process"
                    }
                
            # Create new review process
            current_time = datetime.now(timezone.utc).isoformat()
            new_config = {
                "current": {
                    "reviewer": reviewer_email,
                    "review_start_timestamp": current_time,
                    "review_end_timestamp": None
                },
                "history": current_config.get("history", []) if current_config else [],
                "review_complete": False
            }
            
            # Update vacancy with new review process
            cur.execute(
                f"""
                UPDATE {self.schema}.vacancy_shortlist_processed 
                SET reviewer_config = %s::jsonb,
                    locked_by = %s,
                    updated_at = CURRENT_TIMESTAMP
                WHERE vacancy_id = %s::uuid
                RETURNING vacancy_id, reviewer_config, locked_by;
                """,
                (json.dumps(new_config), reviewer_email, vacancy_id)
            )
            
            updated_row = cur.fetchone()
            conn.commit()
            
            if updated_row:
                return {
                    "message": "Review process started successfully",
                    "vacancy_id": str(updated_row[0]),
                    "reviewer_config": updated_row[1],
                    "locked_by": updated_row[2],
                    "status_code": 200
                }
            else:
                return {"error": "Failed to start review process", "status_code": 500}
                
        except Exception as e:
            self.logger.error(f"Error starting review process: {e}")
            conn.rollback()
            return {"error": f"Error starting review process: {str(e)}", "status_code": 500}
        finally:
            if cur:
                cur.close()

    def complete_vacancy_review(self, vacancy_id: str, reviewer_email: str):
        """Complete a review process and lock the vacancy."""
        self.logger.info(f"Completing review process for vacancy {vacancy_id} by reviewer {reviewer_email}")
        self.db.connect()
        conn = self._get_active_db_connection()
        try:
            cur = conn.cursor()
            
            # First check if vacancy exists and get current state
            # Using FOR UPDATE SKIP LOCKED since we're completing a review we started
            cur.execute(
                f"""
                SELECT reviewer_config, is_locked, locked_by 
                FROM {self.schema}.vacancy_shortlist_processed 
                WHERE vacancy_id = %s::uuid
                FOR UPDATE SKIP LOCKED;
                """,
                (vacancy_id,)
            )
            row = cur.fetchone()
            
            if not row:
                return {"error": f"Vacancy {vacancy_id} not found", "status_code": 404}
                
            current_config, is_locked, locked_by = row
            
            # Check if vacancy is already locked
            if is_locked:
                return {
                    "error": "Vacancy is already locked",
                    "status_code": 409,
                    "details": "Vacancy is already in read-only mode"
                }
            
            # Validate reviewer
            if not current_config or current_config.get("current", {}).get("reviewer") != reviewer_email:
                return {
                    "error": "Only the assigned reviewer can complete the review",
                    "status_code": 403,
                    "details": f"Current reviewer is {current_config.get('current', {}).get('reviewer') if current_config else 'none'}"
                }
                
            # Move current process to history and lock vacancy
            current_process = current_config.get("current", {})
            current_process["review_end_timestamp"] = datetime.now(timezone.utc).isoformat()
            
            history = current_config.get("history", [])
            history.append(current_process)
            
            new_config = {
                "current": {},
                "history": history,
                "review_complete": True
            }
            
            # Update vacancy with completed review
            cur.execute(
                f"""
                UPDATE {self.schema}.vacancy_shortlist_processed 
                SET reviewer_config = %s::jsonb,
                    is_locked = TRUE,
                    locked_by = %s,
                    updated_at = CURRENT_TIMESTAMP
                WHERE vacancy_id = %s::uuid
                RETURNING vacancy_id, reviewer_config, is_locked, locked_by;
                """,
                (json.dumps(new_config), reviewer_email, vacancy_id)
            )
            
            updated_row = cur.fetchone()
            conn.commit()
            
            if updated_row:
                return {
                    "message": "Review completed and vacancy locked successfully",
                    "vacancy_id": str(updated_row[0]),
                    "reviewer_config": updated_row[1],
                    "is_locked": updated_row[2],
                    "locked_by": updated_row[3],
                    "status_code": 200
                }
            else:
                return {"error": "Failed to complete review", "status_code": 500}
                
        except Exception as e:
            self.logger.error(f"Error completing review process: {e}")
            conn.rollback()
            return {"error": f"Error completing review process: {str(e)}", "status_code": 500}
        finally:
            if cur:
                cur.close()

    def get_vacancy_review_status(self, vacancy_id: str):
        """Get the current review status of a vacancy."""
        self.logger.info(f"Getting review status for vacancy {vacancy_id}")
        self.db.connect()
        conn = self._get_active_db_connection()
        cur = None # Define cur here to ensure it's available in finally
        try:
            cur = conn.cursor()
            
            cur.execute(
                f"""
                SELECT reviewer_config, is_locked, locked_by 
                FROM {self.schema}.vacancy_shortlist_processed 
                WHERE vacancy_id = %s::uuid;
                """,
                (vacancy_id,)
            )
            row = cur.fetchone()
            
            if not row:
                return {"error": f"Vacancy {vacancy_id} not found", "status_code": 404} # Added status_code
                
            return {
                "vacancy_id": vacancy_id,
                "reviewer_config": row[0],
                "is_locked": row[1],
                "locked_by": row[2],
                "status_code": 200 # Added status_code
            }
                
        except Exception as e:
            self.logger.error(f"Error getting review status: {e}", exc_info=True) # Added exc_info
            # Consider if conn.rollback() is needed if any write operation was planned
            return {"error": f"Error getting review status: {str(e)}", "status_code": 500} # Added status_code
        finally:
            if cur:
                cur.close()

    def get_vacancy_update_timestamps(self, vacancy_id: str):
        """Fetch current_search_result_generated_at, search_result_data_last_updated_at, and archived status for a vacancy."""
        self.logger.info(f"Fetching update timestamps for vacancy_id: {vacancy_id}")
        self.db.connect()
        conn = self._get_active_db_connection()
        cur = None
        try:
            # Validate vacancy_id is a proper UUID
            try:
                vacancy_uuid = uuid.UUID(vacancy_id)
            except (ValueError, AttributeError, TypeError):
                self.logger.error(f"Invalid vacancy_id format: {vacancy_id}")
                return {
                    "error": f"Invalid vacancy_id format: '{vacancy_id}'. Ensure that it is a valid UUID.",
                    "status_code": 400
                }

            cur = conn.cursor()
            query = f"""
            SELECT 
                current_search_result_generated_at,
                search_result_data_last_updated_at,
                archived
            FROM {self.schema}.vacancy_shortlist_processed
            WHERE vacancy_id = %s::uuid;
            """
            cur.execute(query, (str(vacancy_uuid),))
            row = cur.fetchone()

            if not row:
                self.logger.warning(f"No record found for vacancy_id: {vacancy_id} in vacancy_shortlist_processed.")
                return {
                    "error": f"Vacancy with ID {vacancy_id} not found.",
                    "status_code": 404
                }

            current_search_result_generated_at, search_result_data_last_updated_at, archived = row
            
            # Ensure timestamps are in ISO format if not None, else pass None
            return {
                "match_results_generated_at": current_search_result_generated_at.isoformat() if current_search_result_generated_at else None,
                "data_last_updated_at": search_result_data_last_updated_at.isoformat() if search_result_data_last_updated_at else None,
                "archived": archived,
                "status_code": 200
            }

        except Exception as e:
            self.logger.error(f"Error fetching vacancy update timestamps for vacancy_id {vacancy_id}: {e}", exc_info=True)
            # No rollback needed for SELECT typically, unless part of a larger transaction not shown.
            return {
                "error": f"Error fetching vacancy update timestamps: {str(e)}",
                "status_code": 500
                }
        finally:
            if cur:
                cur.close()

    def claim_vacancy_review(self, vacancy_id: str, reviewer_email: str):
        """Allow a new reviewer to claim a previously locked vacancy."""
        self.logger.info(f"Attempting to claim vacancy {vacancy_id} by reviewer {reviewer_email}")
        self.db.connect()
        conn = self._get_active_db_connection()
        try:
            cur = conn.cursor()
            
            # First check if vacancy exists and get current state
            # Using FOR UPDATE since we need to ensure no one else claims it
            cur.execute(
                f"""
                SELECT reviewer_config, is_locked, locked_by 
                FROM {self.schema}.vacancy_shortlist_processed 
                WHERE vacancy_id = %s::uuid
                FOR UPDATE;
                """,
                (vacancy_id,)
            )
            row = cur.fetchone()
            
            if not row:
                return {
                    "error": f"Vacancy {vacancy_id} not found",
                    "status_code": 404,
                    "details": "The specified vacancy does not exist"
                }
                
            current_config, is_locked, locked_by = row
            
            # Validate email domain (optional - uncomment if needed)
            if not reviewer_email.endswith("@tandymgroup.com"):
                return {
                    "error": "Invalid reviewer email domain",
                    "status_code": 400,
                    "details": "Reviewer email must be from tandymgroup.com domain"
                }
            
            # Check if vacancy is currently being reviewed (not locked and has an active reviewer)
            if not is_locked and current_config and current_config.get("current", {}).get("reviewer"):
                if current_config["current"]["reviewer"] == reviewer_email:
                    return {
                        "error": "You already have an active review process for this vacancy",
                        "status_code": 400,
                        "details": "Cannot claim a vacancy you are currently reviewing"
                    }
                return {
                    "error": f"Vacancy is currently being reviewed by {current_config['current']['reviewer']}",
                    "status_code": 409,
                    "details": f"Current reviewer: {current_config['current']['reviewer']}"
                }
            
            # Check if vacancy is open for rating (not locked and not in review)
            if not is_locked and (not current_config or not current_config.get("current", {}).get("reviewer")):
                return {
                    "error": "Vacancy is currently open for rating",
                    "status_code": 409,
                    "details": "This vacancy is available for review. Please use the start endpoint to begin the review process."
                }
            
            # Archive current process if it exists
            history = current_config.get("history", []) if current_config else []
            if current_config and current_config.get("current"):
                current_process = current_config["current"]
                if current_process.get("review_end_timestamp") is None:
                    current_process["review_end_timestamp"] = datetime.now(timezone.utc).isoformat()
                history.append(current_process)
            
            # Create new review process
            current_time = datetime.now(timezone.utc).isoformat()
            new_config = {
                "current": {
                    "reviewer": reviewer_email,
                    "review_start_timestamp": current_time,
                    "review_end_timestamp": None
                },
                "history": history,
                "review_complete": False
            }
            
            # Update vacancy with new review process
            cur.execute(
                f"""
                UPDATE {self.schema}.vacancy_shortlist_processed 
                SET reviewer_config = %s::jsonb,
                    is_locked = FALSE,
                    locked_by = %s,
                    updated_at = CURRENT_TIMESTAMP
                WHERE vacancy_id = %s::uuid
                RETURNING vacancy_id, reviewer_config, is_locked, locked_by;
                """,
                (json.dumps(new_config), reviewer_email, vacancy_id)
            )
            
            updated_row = cur.fetchone()
            conn.commit()
            
            if updated_row:
                return {
                    "message": "Vacancy claimed successfully",
                    "vacancy_id": str(updated_row[0]),
                    "reviewer_config": updated_row[1],
                    "is_locked": updated_row[2],
                    "locked_by": updated_row[3],
                    "status_code": 200
                }
            else:
                return {
                    "error": "Failed to claim vacancy",
                    "status_code": 500,
                    "details": "Database update failed"
                }
                
        except Exception as e:
            self.logger.error(f"Error claiming vacancy: {e}", exc_info=True)
            if conn:
                conn.rollback()
            return {
                "error": f"Error claiming vacancy: {str(e)}",
                "status_code": 500,
                "details": "Internal server error"
            }
        finally:
            if cur:
                cur.close()

    def get_catalyst_match_status_for_vacancy(self, vacancy_id: str):
        """Fetch Catalyst match generation status and update timestamps for a given vacancy."""
        self.logger.info(f"Fetching Catalyst match status and timestamps for vacancy_id: {vacancy_id}.")
        self.db.connect()
        conn = self._get_active_db_connection()
        cur = None
        try:
            # Validate vacancy_id is a proper UUID
            try:
                vacancy_uuid = uuid.UUID(vacancy_id)
            except (ValueError, AttributeError, TypeError):
                self.logger.error(f"Invalid vacancy_id format: {vacancy_id}")
                return {
                    "error": f"Invalid vacancy_id format: '{vacancy_id}'. Ensure that it is a valid UUID.",
                    "status_code": 400
                }

            cur = conn.cursor()

            # Additional check: Ensure vacancy_id exists in the vacancies table
            vacancy_check_query = f"""
            SELECT 1 FROM {self.schema}.vacancies WHERE vacancy_id = %s::uuid;
            """
            cur.execute(vacancy_check_query, (str(vacancy_uuid),))
            if cur.fetchone() is None:
                self.logger.warning(f"vacancy_id {vacancy_id} not found in vacancies table.")
                return {
                    "error": f"Vacancy with ID {vacancy_id} not found. Ensure Catalyst Match been enabled. If done recently, give it few minutes to process.",
                    "status_code": 404
                }

            # Query 1: Get vacancy update timestamps
            timestamps_query = f"""
            SELECT 
                current_search_result_generated_at,
                search_result_data_last_updated_at,
                archived
            FROM {self.schema}.vacancy_shortlist_processed
            WHERE vacancy_id = %s::uuid;
            """
            cur.execute(timestamps_query, (str(vacancy_uuid),))
            timestamps_row = cur.fetchone()

            if not timestamps_row:
                self.logger.warning(f"No record found for vacancy_id: {vacancy_id} in vacancy_shortlist_processed.")
                return {
                    "error": f"Catalyst Match has not yet been enabled and/or the job to generate the results for the vacancy {vacancy_id} is yet to be scheduled.",
                    "status_code": 404
                }
            
            current_search_result_generated_at, search_result_data_last_updated_at, archived = timestamps_row
            
            update_timestamps = {
                "match_results_generated_at": current_search_result_generated_at.isoformat() if current_search_result_generated_at else None,
                "data_last_updated_at": search_result_data_last_updated_at.isoformat() if search_result_data_last_updated_at else None,
                "archived": archived
            }

            # Query 2: Get Catalyst match status
            catalyst_query = f"""
            SELECT 
                search_match_status,
                created_at,
                search_match_completed_at,
                search_match_metadata->>'initiated_by' AS initiated_by
            FROM {self.schema}.catalyst_match_job_processing_queue
            WHERE vacancy_id = %s::uuid AND type = 'catalyst_match'
            ORDER BY created_at DESC
            LIMIT 1;
            """
            cur.execute(catalyst_query, (str(vacancy_uuid),))
            catalyst_row = cur.fetchone()

            catalyst_status_data = {
                "status": None,
                "initiated_at": None,
                "completed_at": None,
                "initiated_by": None
            }

            if catalyst_row:
                search_match_status, created_at, search_match_completed_at, initiated_by = catalyst_row
                catalyst_status_data = {
                    "status": search_match_status,
                    "initiated_at": created_at.isoformat() if created_at else None,
                    "completed_at": search_match_completed_at.isoformat() if search_match_completed_at else None,
                    "initiated_by": initiated_by
                }
            else:
                self.logger.warning(f"No Catalyst match generation record found for vacancy_id: {vacancy_id} with type 'catalyst_match'.")

            self.logger.info(f"Successfully fetched Catalyst catalyst_status_data: {catalyst_status_data}.")
            # Combine results
            return {
                "catalyst_match_status_data": catalyst_status_data,
                "update_timestamps": update_timestamps,
                "status_code": 200
            }

        except (psycopg2.Error, Exception) as e:
            self.logger.error(f"Error fetching Catalyst match status for vacancy_id {vacancy_id}: {e}", exc_info=True)
            return {
                "error": "Server error while retrieving match generation status.",
                "details": str(e),
                "status_code": 500
                }
        finally:
            if cur:
                cur.close()

    def regenerate_catalyst_match(self, vacancy_id: str, reviewer_email: str):
        """
        Enqueues a job to regenerate Catalyst match results for a vacancy.
        """
        self.logger.info(f"Enqueuing Catalyst match regeneration for vacancy_id: {vacancy_id}, initiated by: {reviewer_email}")
        self.db.connect()
        conn = self._get_active_db_connection()
        cur = None
        try:
            # Validate vacancy_id is a proper UUID
            try:
                vacancy_uuid = uuid.UUID(vacancy_id)
            except (ValueError, AttributeError, TypeError):
                self.logger.error(f"Invalid vacancy_id format: {vacancy_id}")
                return {
                    "error": f"Invalid vacancy_id format: '{vacancy_id}'. Ensure that it is a valid UUID.",
                    "status_code": 400
                }

            cur = conn.cursor()
            
            # Check if the vacancy exists in vacancy_shortlist_processed
            cur.execute(
                f"SELECT 1 FROM {self.schema}.vacancy_shortlist_processed WHERE vacancy_id = %s::uuid",
                (str(vacancy_uuid),)
            )
            if cur.fetchone() is None:
                self.logger.warning(f"Attempted to regenerate match for non-existent vacancy_id: {vacancy_id}")
                return {
                    "error": f"Vacancy with ID {vacancy_id} not found.",
                    "status_code": 404
                }

            search_match_metadata = json.dumps({"initiated_by": reviewer_email})
            
            insert_query = f"""
                INSERT INTO {self.schema}.catalyst_match_job_processing_queue
                    (vacancy_id, search_match_status, search_match_metadata, type)
                VALUES
                    (%s::uuid, 'queued', %s::jsonb, 'catalyst_match')
                RETURNING search_match_status, created_at, search_match_metadata->>'initiated_by' as initiated_by;
            """
            
            cur.execute(insert_query, (str(vacancy_uuid), search_match_metadata))
            
            new_job = cur.fetchone()
            conn.commit()
            
            if new_job is None:
                self.logger.error(f"Failed to enqueue regeneration job for vacancy_id: {vacancy_id} - no row returned.")
                return {
                    "error": "Failed to enqueue regeneration job.",
                    "status_code": 500
                }
            
            status, initiated_at, initiated_by = new_job
            
            self.logger.info(f"Successfully enqueued regeneration job for vacancy_id: {vacancy_id}")
            
            return {
                "status": status,
                "initiated_at": initiated_at,
                "completed_at": None,
                "initiated_by": initiated_by
            }

        except psycopg2.errors.UniqueViolation as e:
            if conn:
                conn.rollback()
            self.logger.warning(f"Conflict: Active Catalyst match job already exists for vacancy_id {vacancy_id}. Details: {e}")
            return {
                "error": "An active match generation job already exists for this vacancy.",
                "details": "A job is already 'queued' or 'inprocess'. Please wait for it to complete.",
                "status_code": 409
            }
        except Exception as e:
            if conn:
                conn.rollback()
            self.logger.error(f"Error enqueuing Catalyst match regeneration for vacancy_id {vacancy_id}: {e}", exc_info=True)
            return {
                "error": "Server error while enqueuing match generation job.",
                "details": str(e),
                "status_code": 500
            }
        finally:
            if cur:
                cur.close()

    def shortlist_candidate_for_vacancy(self, reviewer_email: str, vacancy_id: str, candidate_id: str):
        """Shortlist a candidate for a vacancy."""
        self.logger.info(f"Attempting to shortlist candidate {candidate_id} for vacancy {vacancy_id} by reviewer {reviewer_email}")
        self.db.connect()
        conn = self._get_active_db_connection()
        cur = None
        
        try:
            cur = conn.cursor()
            current_time = datetime.now(timezone.utc).isoformat()
            
            # Pre-check: See if the candidate is already shortlisted for this vacancy
            check_query = f"""
                SELECT shortlisted FROM {self.schema}.candidate_application_shortlists_detail
                WHERE vacancy_id = %s::uuid AND contact_id = %s::uuid
                FOR UPDATE;
            """
            
            cur.execute(check_query, (str(vacancy_id), str(candidate_id)))
            existing_row = cur.fetchone()
            
            if existing_row and existing_row[0]:
                existing_data = existing_row[0]
                existing_status = existing_data.get("status")
                
                # Check if already successfully shortlisted
                if existing_status == "success":
                    # Validate that we have meaningful data
                    shortlisted_at = existing_data.get("shortlisted_at")
                    shortlisted_by = existing_data.get("shortlisted_by")
                    
                    # If we have incomplete data, log it but still return the existing data
                    if not shortlisted_at or not shortlisted_by or shortlisted_at == "" or shortlisted_by == "":
                        self.logger.warning(f"Incomplete shortlist data found for candidate {candidate_id} in vacancy {vacancy_id}: {existing_data}")
                        # Update the incomplete data with current information
                        existing_data.update({
                            "shortlisted_at": shortlisted_at or current_time,
                            "shortlisted_by": shortlisted_by or reviewer_email,
                            "note": "Data was incomplete, updated with current information"
                        })
                    
                    return {
                        "success": False,
                        "status_code": 409,  # Conflict - resource already exists
                        "message": "Candidate is already shortlisted for this vacancy",
                        "details": "The candidate has already been successfully shortlisted for this vacancy",
                        "vacancy_id": str(vacancy_id),
                        "candidate_id": str(candidate_id),
                        "shortlisted_data": existing_data,
                        "error_code": "already_shortlisted",
                        "error_message": "Candidate already shortlisted"
                    }
                elif existing_status == "failed":
                    # If previous attempt failed, we can retry
                    self.logger.info(f"Previous shortlist attempt failed for candidate {candidate_id} in vacancy {vacancy_id}, retrying...")
                elif existing_status == "removed":
                    # If previously removed, we can shortlist again
                    self.logger.info(f"Candidate {candidate_id} was previously removed from vacancy {vacancy_id}, shortlisting again...")
            
            # Attempt to shortlist the candidate for the vacancy in Mercury Dataverse
            try:
                dataverse_result = self.vacancy_dataverse_helper.shortlist_candidate_for_vacancy(vacancy_id, candidate_id, reviewer_email)
                self.logger.info(f"Successfully shortlisted candidate {candidate_id} for vacancy {vacancy_id} in Dataverse")
                
                # Extract the crimson_vacancycandidateid from the result
                # This could be useful for the client to track the shortlist status and delete the shortlist if needed.
                crimson_vacancycandidateid = dataverse_result.get("crimson_vacancycandidateid")
                if crimson_vacancycandidateid:
                    self.logger.info(f"Dataverse shortlist ID: {crimson_vacancycandidateid}")
                
                # Record success in database
                shortlisted_data = {
                    "status": "success",
                    "shortlisted_at": current_time,
                    "shortlisted_by": reviewer_email,
                    "crimson_vacancycandidateid": crimson_vacancycandidateid
                }
                
            except DataverseError as e:
                self.logger.error(f"Dataverse error while shortlisting candidate {candidate_id} for vacancy {vacancy_id}: {str(e)}")
                
                # Check if this is the specific "already exists" error from Dataverse
                error_message = str(e)
                if "0x80040265" in error_message or "already exists" in error_message.lower():
                    # Get the existing shortlist data from database
                    existing_data = None
                    if existing_row and existing_row[0]:
                        existing_data = existing_row[0]
                        self.logger.info(f"Found existing shortlist data in database: {existing_data}")
                    else:
                        # If we don't have the data from the pre-check, query it again
                        check_query = f"""
                            SELECT shortlisted FROM {self.schema}.candidate_application_shortlists_detail
                            WHERE vacancy_id = %s::uuid AND contact_id = %s::uuid;
                        """
                        cur.execute(check_query, (str(vacancy_id), str(candidate_id)))
                        existing_row = cur.fetchone()
                        if existing_row and existing_row[0]:
                            existing_data = existing_row[0]
                            self.logger.info(f"Found existing shortlist data on second query: {existing_data}")
                        else:
                            self.logger.warning(f"No shortlist data found in database for candidate {candidate_id} in vacancy {vacancy_id}, but Dataverse says it exists")
                            # Create a basic shortlist data structure since we know it exists in Dataverse
                            existing_data = {
                                "status": "success",
                                "shortlisted_at": current_time,
                                "shortlisted_by": reviewer_email,
                                "crimson_vacancycandidateid": None,  # We don't have this info
                                "note": "Shortlist exists in Dataverse but not recorded in database"
                            }
                    
                    return {
                        "success": False,
                        "status_code": 409,  # Conflict - resource already exists
                        "message": "Candidate is already shortlisted for this vacancy",
                        "details": "The candidate has already been shortlisted for this vacancy in Dataverse",
                        "vacancy_id": str(vacancy_id),
                        "candidate_id": str(candidate_id),
                        "shortlisted_data": existing_data,
                        "error_code": "already_shortlisted",
                        "error_message": "Candidate already shortlisted in Dataverse"
                    }
                
                # Record failure in database
                shortlisted_data = {
                    "status": "failed",
                    "shortlisted_at": current_time,
                    "shortlisted_by": reviewer_email,
                    "error_code": "dataverse_error",
                    "error_message": str(e)
                }
                
                # Upsert the shortlisted status in candidate_application_shortlists_detail table
                upsert_query = f"""
                    INSERT INTO {self.schema}.candidate_application_shortlists_detail
                        (vacancy_id, contact_id, shortlisted, shortlisted_json_version, created_at, updated_at)
                    VALUES
                        (%s::uuid, %s::uuid, %s::jsonb, 0.1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                    ON CONFLICT (vacancy_id, contact_id) DO UPDATE SET
                        shortlisted = EXCLUDED.shortlisted,
                        shortlisted_json_version = EXCLUDED.shortlisted_json_version,
                        updated_at = CURRENT_TIMESTAMP
                    RETURNING vacancy_id, contact_id, shortlisted;
                """
                
                cur.execute(upsert_query, (
                    str(vacancy_id),
                    str(candidate_id),
                    json.dumps(shortlisted_data)
                ))
                
                updated_row = cur.fetchone()
                conn.commit()
                
                return {
                    "success": False,
                    "status_code": 400,
                    "message": f"Failed to shortlist candidate in Dataverse: {str(e)}",
                    "details": "Dataverse operation failed",
                    "vacancy_id": str(vacancy_id),
                    "candidate_id": str(candidate_id),
                    "shortlisted_data": updated_row[2] if updated_row else None,
                    "error_code": "dataverse_error",
                    "error_message": str(e)
                }
                
            except Exception as e:
                self.logger.error(f"Unexpected error while shortlisting candidate {candidate_id} for vacancy {vacancy_id}: {str(e)}")
                
                # Record failure in database
                shortlisted_data = {
                    "status": "failed",
                    "shortlisted_at": current_time,
                    "shortlisted_by": reviewer_email,
                    "error_code": "internal_error",
                    "error_message": str(e)
                }
                
                # Upsert the shortlisted status in candidate_application_shortlists_detail table
                upsert_query = f"""
                    INSERT INTO {self.schema}.candidate_application_shortlists_detail
                        (vacancy_id, contact_id, shortlisted, shortlisted_json_version, created_at, updated_at)
                    VALUES
                        (%s::uuid, %s::uuid, %s::jsonb, 0.1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                    ON CONFLICT (vacancy_id, contact_id) DO UPDATE SET
                        shortlisted = EXCLUDED.shortlisted,
                        shortlisted_json_version = EXCLUDED.shortlisted_json_version,
                        updated_at = CURRENT_TIMESTAMP
                    RETURNING vacancy_id, contact_id, shortlisted;
                """
                
                cur.execute(upsert_query, (
                    str(vacancy_id),
                    str(candidate_id),
                    json.dumps(shortlisted_data)
                ))
                
                updated_row = cur.fetchone()
                conn.commit()
                
                return {
                    "success": False,
                    "status_code": 500,
                    "message": f"Internal server error while shortlisting candidate: {str(e)}",
                    "details": "Unexpected error occurred",
                    "vacancy_id": str(vacancy_id),
                    "candidate_id": str(candidate_id),
                    "shortlisted_data": updated_row[2] if updated_row else None,
                    "error_code": "internal_error",
                    "error_message": str(e)
                }

            # Record success in database (only reached if Dataverse operation succeeded)
            upsert_query = f"""
                INSERT INTO {self.schema}.candidate_application_shortlists_detail
                    (vacancy_id, contact_id, shortlisted, shortlisted_json_version, created_at, updated_at)
                VALUES
                    (%s::uuid, %s::uuid, %s::jsonb, 0.1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                ON CONFLICT (vacancy_id, contact_id) DO UPDATE SET
                    shortlisted = EXCLUDED.shortlisted,
                    shortlisted_json_version = EXCLUDED.shortlisted_json_version,
                    updated_at = CURRENT_TIMESTAMP
                RETURNING vacancy_id, contact_id, shortlisted;
            """
            
            cur.execute(upsert_query, (
                str(vacancy_id),
                str(candidate_id),
                json.dumps(shortlisted_data)
            ))
            
            updated_row = cur.fetchone()
            conn.commit()
            
            if updated_row:
                self.logger.info(f"Successfully updated shortlisted status for candidate {candidate_id} in vacancy {vacancy_id}")
                return {
                    "success": True,
                    "status_code": 200,
                    "message": "Candidate shortlisted successfully in Dataverse and database",
                    "vacancy_id": str(updated_row[0]),
                    "candidate_id": str(updated_row[1]),
                    "shortlisted_data": updated_row[2]
                }
            else:
                return {
                    "success": False,
                    "status_code": 500,
                    "message": "Failed to update shortlisted status",
                    "details": "Database update failed",
                    "vacancy_id": str(vacancy_id),
                    "candidate_id": str(candidate_id)
                }
                
        except Exception as e:
            self.logger.error(f"Error updating shortlisted status: {e}")
            if conn:
                conn.rollback()
            return {
                "success": False,
                "status_code": 500,
                "message": f"Error updating shortlisted status: {str(e)}",
                "details": "Internal server error",
                "vacancy_id": str(vacancy_id),
                "candidate_id": str(candidate_id)
            }
        finally:
            if cur:
                cur.close()

    def get_vacancy_review_stats(self, vacancy_id: str):
        """Get review statistics for a vacancy."""
        self.logger.info(f"Fetching review statistics for vacancy_id: {vacancy_id}")
        
        # Validate vacancy_id is a proper UUID
        try:
            vacancy_uuid = uuid.UUID(vacancy_id)
        except (ValueError, AttributeError, TypeError):
            self.logger.error(f"Invalid vacancy_id format: {vacancy_id}")
            return {
                "error": f"Invalid vacancy_id format: '{vacancy_id}'. Ensure that it is a valid UUID.",
                "status_code": 400
            }

        self.db.connect()
        conn = self._get_active_db_connection()
        cur = None
        
        try:
            cur = conn.cursor()
            
            query = f"""
            WITH feedback_exploded AS (
              SELECT
                f.vacancy_id,
                f.contact_id,
                fb.value ->> 'vote' AS vote
              FROM
                {self.schema}.candidate_application_shortlists_feedback f
              LEFT JOIN LATERAL
                jsonb_array_elements(f.feedbacks) AS fb ON TRUE
            ),
            joined_feedbacks AS (
              -- Join feedbacks only with archived shortlist entries
              SELECT
                s.vacancy_id,
                s.contact_id,
                fe.vote
              FROM
                {self.schema}.candidate_application_shortlists s
              INNER JOIN
                feedback_exploded fe
                ON s.vacancy_id = fe.vacancy_id AND s.contact_id = fe.contact_id
              WHERE
                s.archived = false
            ),
            feedback_summary AS (
              SELECT
                vacancy_id,
                COUNT(DISTINCT contact_id) AS total_rated,
                COUNT(DISTINCT contact_id) FILTER (
                  WHERE LOWER(vote) IN ('like', 'maybe')
                ) AS liked,
                COUNT(DISTINCT contact_id) FILTER (
                  WHERE LOWER(vote) = 'dislike'
                ) AS disliked
              FROM
                joined_feedbacks
              GROUP BY
                vacancy_id
            )

            SELECT
              s.vacancy_id,
              v.refno,
              COUNT(*) AS total_found,
              COALESCE(f.total_rated, 0) AS total_rated,
              COALESCE(f.liked, 0) AS liked,
              COALESCE(f.disliked, 0) AS disliked
            FROM
              {self.schema}.candidate_application_shortlists s
            LEFT JOIN
              feedback_summary f ON s.vacancy_id = f.vacancy_id
            LEFT JOIN
              {self.schema}.vacancies v ON s.vacancy_id = v.vacancy_id
            WHERE
              s.archived = false
              AND s.vacancy_id = %s::uuid
            GROUP BY
              s.vacancy_id, v.refno, f.total_rated, f.liked, f.disliked
            ORDER BY
              s.vacancy_id;
            """
            
            cur.execute(query, (str(vacancy_uuid),))
            row = cur.fetchone()
            
            if not row:
                self.logger.warning(f"No review statistics found for vacancy_id: {vacancy_id}")
                return {
                    "error": "No review statistics found for the given vacancy.",
                    "status_code": 404
                }
            
            # Extract the results
            vacancy_id_result, refno, total_found, total_rated, liked, disliked = row
            
            return {
                "vacancy_id": str(vacancy_id_result),
                "refno": refno,
                "total_found": total_found,
                "total_rated": total_rated,
                "liked": liked,
                "disliked": disliked,
                "status_code": 200
            }
            
        except (psycopg2.Error, Exception) as e:
            self.logger.error(f"Error fetching review statistics for vacancy_id {vacancy_id}: {e}", exc_info=True)
            return {
                "error": "Server error while retrieving review statistics.",
                "details": str(e),
                "status_code": 500
            }
        finally:
            if cur:
                cur.close()

    def unshortlist_candidate_for_vacancy(self, reviewer_email: str, vacancy_id: str, candidate_id: str):
        """Unshortlist a candidate for a vacancy."""
        self.logger.info(f"Attempting to unshortlist candidate {candidate_id} for vacancy {vacancy_id} by reviewer {reviewer_email}")

        # Initialize database connection early to record all attempts
        self.db.connect()
        conn = self._get_active_db_connection()
        cur = None
        
        try:
            cur = conn.cursor()
            current_time = datetime.now(timezone.utc).isoformat()
            
            # First, check if the shortlist exists and get the crimson_vacancycandidateid
            check_query = f"""
                SELECT shortlisted FROM {self.schema}.candidate_application_shortlists_detail
                WHERE vacancy_id = %s::uuid AND contact_id = %s::uuid
                FOR UPDATE;
            """
            
            cur.execute(check_query, (str(vacancy_id), str(candidate_id)))
            existing_row = cur.fetchone()
            
            if not existing_row or not existing_row[0]:
                return {
                    "success": False,
                    "status_code": 404,
                    "message": "Shortlist not found for the given candidate and vacancy",
                    "details": "No shortlist record exists to remove",
                    "vacancy_id": str(vacancy_id),
                    "candidate_id": str(candidate_id)
                }
            
            # Extract the crimson_vacancycandidateid from existing data
            existing_data = existing_row[0]
            crimson_vacancycandidateid = existing_data.get("crimson_vacancycandidateid")
            
            # Attempt to remove the candidate from Dataverse shortlist
            try:
                if crimson_vacancycandidateid:
                    # Call Dataverse to remove the shortlist
                    dataverse_result = self.vacancy_dataverse_helper.unshortlist_candidate_for_vacancy(
                        vacancy_id, candidate_id, reviewer_email, crimson_vacancycandidateid
                    )
                    self.logger.info(f"Successfully unshortlisted candidate {candidate_id} for vacancy {vacancy_id} in Dataverse")
                else:
                    self.logger.warning(f"No crimson_vacancycandidateid found for candidate {candidate_id} in vacancy {vacancy_id}")
                
                # Record removal in database
                shortlisted_data = {
                    "status": "removed",
                    "shortlisted_at": current_time,
                    "shortlisted_by": reviewer_email,
                    "crimson_vacancycandidateid": crimson_vacancycandidateid
                }
                
            except DataverseError as e:
                self.logger.error(f"Dataverse error while unshortlisting candidate {candidate_id} for vacancy {vacancy_id}: {str(e)}")
                
                # Record failure in database
                shortlisted_data = {
                    "status": "failed",
                    "shortlisted_at": current_time,
                    "shortlisted_by": reviewer_email,
                    "error_code": "dataverse_error",
                    "error_message": str(e),
                    "crimson_vacancycandidateid": crimson_vacancycandidateid
                }
                
                # Update the shortlisted status in database
                update_query = f"""
                    UPDATE {self.schema}.candidate_application_shortlists_detail
                    SET shortlisted = %s::jsonb,
                        shortlisted_json_version = 0.1,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE vacancy_id = %s::uuid AND contact_id = %s::uuid
                    RETURNING vacancy_id, contact_id, shortlisted;
                """
                
                cur.execute(update_query, (
                    json.dumps(shortlisted_data),
                    str(vacancy_id),
                    str(candidate_id)
                ))
                
                updated_row = cur.fetchone()
                conn.commit()
                
                return {
                    "success": False,
                    "status_code": 400,
                    "message": f"Failed to unshortlist candidate in Dataverse: {str(e)}",
                    "details": "Dataverse operation failed",
                    "vacancy_id": str(vacancy_id),
                    "candidate_id": str(candidate_id),
                    "shortlisted_data": updated_row[2] if updated_row else None,
                    "error_code": "dataverse_error",
                    "error_message": str(e)
                }
                
            except Exception as e:
                self.logger.error(f"Unexpected error while unshortlisting candidate {candidate_id} for vacancy {vacancy_id}: {str(e)}")
                
                # Record failure in database
                shortlisted_data = {
                    "status": "failed",
                    "shortlisted_at": current_time,
                    "shortlisted_by": reviewer_email,
                    "error_code": "internal_error",
                    "error_message": str(e)
                }
                
                # Update the shortlisted status in database
                update_query = f"""
                    UPDATE {self.schema}.candidate_application_shortlists_detail
                    SET shortlisted = %s::jsonb,
                        shortlisted_json_version = 0.1,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE vacancy_id = %s::uuid AND contact_id = %s::uuid
                    RETURNING vacancy_id, contact_id, shortlisted;
                """
                
                cur.execute(update_query, (
                    json.dumps(shortlisted_data),
                    str(vacancy_id),
                    str(candidate_id)
                ))
                
                updated_row = cur.fetchone()
                conn.commit()
                
                return {
                    "success": False,
                    "status_code": 500,
                    "message": f"Internal server error while unshortlisting candidate: {str(e)}",
                    "details": "Unexpected error occurred",
                    "vacancy_id": str(vacancy_id),
                    "candidate_id": str(candidate_id),
                    "shortlisted_data": updated_row[2] if updated_row else None,
                    "error_code": "internal_error",
                    "error_message": str(e)
                }

            # Update the shortlisted status in database (only reached if Dataverse operation succeeded)
            update_query = f"""
                UPDATE {self.schema}.candidate_application_shortlists_detail
                SET shortlisted = %s::jsonb,
                    shortlisted_json_version = 0.1,
                    updated_at = CURRENT_TIMESTAMP
                WHERE vacancy_id = %s::uuid AND contact_id = %s::uuid
                RETURNING vacancy_id, contact_id, shortlisted;
            """
            
            cur.execute(update_query, (
                json.dumps(shortlisted_data),
                str(vacancy_id),
                str(candidate_id)
            ))
            
            updated_row = cur.fetchone()
            conn.commit()
            
            if updated_row:
                self.logger.info(f"Successfully updated unshortlisted status for candidate {candidate_id} in vacancy {vacancy_id}")
                return {
                    "success": True,
                    "status_code": 200,
                    "message": "Candidate unshortlisted successfully in Dataverse and database",
                    "vacancy_id": str(updated_row[0]),
                    "candidate_id": str(updated_row[1]),
                    "shortlisted_data": updated_row[2]
                }
            else:
                return {
                    "success": False,
                    "status_code": 500,
                    "message": "Failed to update unshortlisted status",
                    "details": "Database update failed",
                    "vacancy_id": str(vacancy_id),
                    "candidate_id": str(candidate_id)
                }
                
        except Exception as e:
            self.logger.error(f"Error updating unshortlisted status: {e}")
            if conn:
                conn.rollback()
            return {
                "success": False,
                "status_code": 500,
                "message": f"Error updating unshortlisted status: {str(e)}",
                "details": "Internal server error",
                "vacancy_id": str(vacancy_id),
                "candidate_id": str(candidate_id)
            }
        finally:
            if cur:
                cur.close()
