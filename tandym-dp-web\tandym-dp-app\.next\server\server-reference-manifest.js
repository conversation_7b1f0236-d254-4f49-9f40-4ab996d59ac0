self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"0019e5eeea75f1e298425b8560f1b6c359a66921b9\": {\n      \"workers\": {\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/getAppInsightsConnectionString.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (ecmascript)\",\n          \"async\": false\n        },\n        \"app/candidates/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/candidates/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/getAppInsightsConnectionString.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (ecmascript)\",\n          \"async\": false\n        },\n        \"app/_not-found/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/_not-found/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/getAppInsightsConnectionString.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (ecmascript)\",\n          \"async\": false\n        },\n        \"app/CandidateTuning/For_Mercury_Portal/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/CandidateTuning/For_Mercury_Portal/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/getAppInsightsConnectionString.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/api/serverActions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\",\n        \"app/candidates/page\": \"action-browser\",\n        \"app/_not-found/page\": \"action-browser\",\n        \"app/CandidateTuning/For_Mercury_Portal/page\": \"action-browser\"\n      }\n    },\n    \"0081f97a45fcc051f67f3f8979d95c4659f987c3e9\": {\n      \"workers\": {\n        \"app/CandidateTuning/For_Mercury_Portal/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/CandidateTuning/For_Mercury_Portal/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/getAppInsightsConnectionString.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/api/serverActions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/CandidateTuning/For_Mercury_Portal/page\": \"rsc\"\n      }\n    },\n    \"00dfd6ac6138fd6fdb4bb66cfe2e001b5b633b6248\": {\n      \"workers\": {\n        \"app/CandidateTuning/For_Mercury_Portal/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/CandidateTuning/For_Mercury_Portal/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/getAppInsightsConnectionString.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/api/serverActions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/CandidateTuning/For_Mercury_Portal/page\": \"rsc\"\n      }\n    },\n    \"00eed182c174bc0ad16b9dd64ee0f54ce7fdab93b8\": {\n      \"workers\": {\n        \"app/CandidateTuning/For_Mercury_Portal/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/CandidateTuning/For_Mercury_Portal/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/getAppInsightsConnectionString.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/api/serverActions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/CandidateTuning/For_Mercury_Portal/page\": \"rsc\"\n      }\n    },\n    \"408d6350adccefe97f8e569bca319fd5565d4ba3ab\": {\n      \"workers\": {\n        \"app/CandidateTuning/For_Mercury_Portal/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/CandidateTuning/For_Mercury_Portal/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/getAppInsightsConnectionString.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/api/serverActions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/CandidateTuning/For_Mercury_Portal/page\": \"rsc\"\n      }\n    },\n    \"40fd2068e785dd85c91ddd23fd7f0468fa988fe738\": {\n      \"workers\": {\n        \"app/CandidateTuning/For_Mercury_Portal/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/CandidateTuning/For_Mercury_Portal/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/getAppInsightsConnectionString.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/api/serverActions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/CandidateTuning/For_Mercury_Portal/page\": \"rsc\"\n      }\n    },\n    \"60177423cff0c80303d8847e8112cfa5122712a0f4\": {\n      \"workers\": {\n        \"app/CandidateTuning/For_Mercury_Portal/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/CandidateTuning/For_Mercury_Portal/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/getAppInsightsConnectionString.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/api/serverActions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/CandidateTuning/For_Mercury_Portal/page\": \"rsc\"\n      }\n    },\n    \"40b49559358c35ff4b692eb8eb12ea9614a4e1f71f\": {\n      \"workers\": {\n        \"app/CandidateTuning/For_Mercury_Portal/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/CandidateTuning/For_Mercury_Portal/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/getAppInsightsConnectionString.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/api/serverActions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/CandidateTuning/For_Mercury_Portal/page\": \"rsc\"\n      }\n    },\n    \"402798ac62c44aa80cef8655242e6d58d9162b01ec\": {\n      \"workers\": {\n        \"app/CandidateTuning/For_Mercury_Portal/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/CandidateTuning/For_Mercury_Portal/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/getAppInsightsConnectionString.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/api/serverActions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/CandidateTuning/For_Mercury_Portal/page\": \"rsc\"\n      }\n    },\n    \"00430c3fe55aae2c16d8c5809c583e907f184f0dc7\": {\n      \"workers\": {\n        \"app/CandidateTuning/For_Mercury_Portal/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/CandidateTuning/For_Mercury_Portal/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/getAppInsightsConnectionString.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/api/serverActions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/CandidateTuning/For_Mercury_Portal/page\": \"rsc\"\n      }\n    },\n    \"4053d6f48110c08a061373b14ac7e907994228ab59\": {\n      \"workers\": {\n        \"app/CandidateTuning/For_Mercury_Portal/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/CandidateTuning/For_Mercury_Portal/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/getAppInsightsConnectionString.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/api/serverActions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/CandidateTuning/For_Mercury_Portal/page\": \"rsc\"\n      }\n    },\n    \"6027034db188ca657eb89968e241062f20d7184639\": {\n      \"workers\": {\n        \"app/CandidateTuning/For_Mercury_Portal/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/CandidateTuning/For_Mercury_Portal/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/getAppInsightsConnectionString.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/api/serverActions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/CandidateTuning/For_Mercury_Portal/page\": \"rsc\"\n      }\n    },\n    \"4063428e91071df462c0b9076728a7d32615a943e7\": {\n      \"workers\": {\n        \"app/CandidateTuning/For_Mercury_Portal/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/CandidateTuning/For_Mercury_Portal/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/getAppInsightsConnectionString.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/api/serverActions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/CandidateTuning/For_Mercury_Portal/page\": \"rsc\"\n      }\n    },\n    \"0070c516a719bc4d5da2991e171dfc1731b3bfcd01\": {\n      \"workers\": {\n        \"app/CandidateTuning/For_Mercury_Portal/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/CandidateTuning/For_Mercury_Portal/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/getAppInsightsConnectionString.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/api/serverActions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/CandidateTuning/For_Mercury_Portal/page\": \"rsc\"\n      }\n    },\n    \"406345071a46f57d39bc1d0cb464a8359afc40dca2\": {\n      \"workers\": {\n        \"app/CandidateTuning/For_Mercury_Portal/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/CandidateTuning/For_Mercury_Portal/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/getAppInsightsConnectionString.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/api/serverActions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/CandidateTuning/For_Mercury_Portal/page\": \"rsc\"\n      }\n    },\n    \"40d478d951619bce460cfbc7d49a3cc493ba4b47ea\": {\n      \"workers\": {\n        \"app/CandidateTuning/For_Mercury_Portal/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/CandidateTuning/For_Mercury_Portal/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/getAppInsightsConnectionString.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/api/serverActions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/CandidateTuning/For_Mercury_Portal/page\": \"rsc\"\n      }\n    },\n    \"60494406d8979ee9809192155ba859d5ec85a89895\": {\n      \"workers\": {\n        \"app/CandidateTuning/For_Mercury_Portal/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/CandidateTuning/For_Mercury_Portal/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/getAppInsightsConnectionString.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/api/serverActions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/CandidateTuning/For_Mercury_Portal/page\": \"rsc\"\n      }\n    },\n    \"40001ab8f91b17607478f15f3e331f94fa8f492027\": {\n      \"workers\": {\n        \"app/CandidateTuning/For_Mercury_Portal/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/CandidateTuning/For_Mercury_Portal/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/getAppInsightsConnectionString.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/api/serverActions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/CandidateTuning/For_Mercury_Portal/page\": \"rsc\"\n      }\n    },\n    \"6081ed1fe9575b2c0def33594794818f09fc3b9690\": {\n      \"workers\": {\n        \"app/CandidateTuning/For_Mercury_Portal/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/CandidateTuning/For_Mercury_Portal/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/getAppInsightsConnectionString.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/api/serverActions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/CandidateTuning/For_Mercury_Portal/page\": \"rsc\"\n      }\n    },\n    \"6094ec7632250ae73ee9964addbc280e43b1d90039\": {\n      \"workers\": {\n        \"app/CandidateTuning/For_Mercury_Portal/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/CandidateTuning/For_Mercury_Portal/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/getAppInsightsConnectionString.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/api/serverActions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/CandidateTuning/For_Mercury_Portal/page\": \"rsc\"\n      }\n    },\n    \"60de454cb16c2ea8f0b7e2e25af8d0bfaa4de6e5bd\": {\n      \"workers\": {\n        \"app/CandidateTuning/For_Mercury_Portal/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/CandidateTuning/For_Mercury_Portal/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/getAppInsightsConnectionString.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/api/serverActions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/CandidateTuning/For_Mercury_Portal/page\": \"rsc\"\n      }\n    },\n    \"60ff2f47ca078fbac86668827367cfc9554ca27bc5\": {\n      \"workers\": {\n        \"app/CandidateTuning/For_Mercury_Portal/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/CandidateTuning/For_Mercury_Portal/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/getAppInsightsConnectionString.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/api/serverActions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/CandidateTuning/For_Mercury_Portal/page\": \"rsc\"\n      }\n    },\n    \"4058069912dbff67450c9f924a6c0aa1061b4c63d0\": {\n      \"workers\": {\n        \"app/CandidateTuning/For_Mercury_Portal/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/CandidateTuning/For_Mercury_Portal/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/getAppInsightsConnectionString.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/api/serverActions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/CandidateTuning/For_Mercury_Portal/page\": \"rsc\"\n      }\n    },\n    \"406ac29c78d366f5e75ca4601810ebec39513dc73f\": {\n      \"workers\": {\n        \"app/CandidateTuning/For_Mercury_Portal/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/CandidateTuning/For_Mercury_Portal/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/getAppInsightsConnectionString.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/api/serverActions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/CandidateTuning/For_Mercury_Portal/page\": \"rsc\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"WHLvqhg+5uAd2CK00BvwlNoi+/rxpBSUqGf1awguCRA=\"\n}"