variables:
  WebLBIP: ************
  ServerLBIP: ************
  PortalLBIP: ************
  ReactAppEnv: qa
  WebMinReplicas: 1
  WebMaxReplicas: 1
  WebMemThreshold: 80
  WebCpuThreshold: 80
  ServerMinReplicas: 1
  ServerMaxReplicas: 1
  ServerMemThreshold: 80
  ServerCpuThreshold: 80
  PortalMinReplicas: 1
  PortalMaxReplicas: 1
  PortalMemThreshold: 80
  PortalCpuThreshold: 80
  NEXTAUTH_URL: https://recruiter.qa.tandymgroup.com
  NEXT_PUBLIC_AD_LOGIN: true
  NEXT_PUBLIC_PARSE_RESUME_FROM_MERCURY: true
  NEXT_PUBLIC_APPINSIGHTS_CONNECTION_STRING: f3dd9927-e8d5-439b-972a-da458156c3ac;IngestionEndpoint=https://eastus-3.in.applicationinsights.azure.com/;LiveEndpoint=https://eastus.livediagnostics.monitor.azure.com/;ApplicationId=d6e306bb-1468-40e7-99d1-12f2024ed410
  NEXT_PUBLIC_AUTH_URL: recruiter.qa.tandymgroup.com
  CRM_URL: https://tandymgroup-sandbox.crm.dynamics.com
