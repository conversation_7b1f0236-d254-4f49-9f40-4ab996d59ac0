"use client";

import React, { useState, useEffect, useMemo, useCallback } from "react";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table";
import Loading from "@/components/Loading";
import {
  fetchExperimentVacancies,
  fetchExperimentVacancyRunDetails,
  fetchExperimentCandidatesForVacancyRun,
  fetchExperimentRunConfig,
  fetchExperimentCandidateResume,
  archiveExperimentVacancy,
  promoteExperimentResults, // + Import the new promote function
} from "@/api/experimentActions";
import {
  ExperimentVacancy,
  ExperimentRun,
  VacancyRunDetails,
  ExperimentCandidate,
  RunConfig,
  ExperimentVacancyResponse,
  ExperimentResumeResponse,
  TransformedResumeData,
  Feedback,
  FitnessReason,
} from "./helper";
import ExperimentCandidateResume from "@/components/experiments/ExperimentCandidateResume";
import { Input } from "@/components/ui/input"; // + Import Input for the dialog
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Tabs, // + Import Tabs components
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs"; // + Import Tabs components
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"; // Added Tooltip imports
import { unFormattedDateWithBrowserTimezone } from "@/utils/utils";
import {
  ThumbsUp,
  ThumbsDown,
  HelpCircle,
  Copy,
  Link, // Added Link icon
  CheckCircle2, // Import for Promoted icon
  ArchiveIcon, // Import for Archived icon
  Settings, // + Import Settings icon (gear)
  AlertCircle, // For error notifications
  Info, // For general info/success notifications
} from "lucide-react";
import {
  Drawer,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
  DrawerDescription,
  DrawerFooter,
  DrawerClose,
} from "@/components/ui/drawer"; // Assuming vaul is aliased to ui/drawer

// Helper function to format score category keys into readable titles
const formatCategoryKey = (key: string): string => {
  return key
    .split("_")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
};

const FeedbackVoteIcon = ({ vote }: { vote: Feedback["vote"] }) => {
  switch (vote?.toLowerCase()) {
    case "like":
      return <ThumbsUp className="h-5 w-5 text-green-500" />;
    case "dislike":
      return <ThumbsDown className="h-5 w-5 text-red-500" />;
    case "maybe":
      return <HelpCircle className="h-5 w-5 text-yellow-500" />;
    default:
      return <span className="text-gray-400">-</span>;
  }
};

const ExperimentsPage: React.FC = () => {
  const [vacancies, setVacancies] = useState<ExperimentVacancy[]>([]);
  const [selectedVacancy, setSelectedVacancy] =
    useState<ExperimentVacancy | null>(null);
  const [selectedRunId, setSelectedRunId] = useState<number | null>(null);
  const [candidates, setCandidates] = useState<ExperimentCandidate[]>([]);
  const [vacancyDetails, setVacancyDetails] =
    useState<VacancyRunDetails | null>(null);
  const [runConfig, setRunConfig] = useState<RunConfig | null>(null);
  const [isRunPromoted, setIsRunPromoted] = useState<boolean>(false); // New state for promoted status
  const [isVacancyArchived, setIsVacancyArchived] = useState<boolean>(false); // New state for archived status

  const [loadingVacancies, setLoadingVacancies] = useState<boolean>(false);
  const [loadingRunData, setLoadingRunData] = useState<boolean>(false);
  const [loadingResume, setLoadingResume] = useState<boolean>(false);

  const [vacanciesPage, setVacanciesPage] = useState(1);
  const [totalVacanciesPages, setTotalVacanciesPages] = useState(1);
  const [candidatesPage, setCandidatesPage] = useState(1);
  const [totalCandidatesPages, setTotalCandidatesPages] = useState(1);

  const [categoryScoreHeaders, setCategoryScoreHeaders] = useState<string[]>(
    []
  );

  const [selectedExperimentResume, setSelectedExperimentResume] =
    useState<TransformedResumeData | null>(null);
  const [isExperimentResumeModalOpen, setIsExperimentResumeModalOpen] =
    useState(false);

  const [selectedFeedbacks, setSelectedFeedbacks] = useState<Feedback[] | null>(
    null
  );
  const [currentCandidateForFeedback, setCurrentCandidateForFeedback] =
    useState<string | null>(null);
  const [isFeedbacksModalOpen, setIsFeedbacksModalOpen] = useState(false);

  const [selectedFitnessReason, setSelectedFitnessReason] =
    useState<FitnessReason | null>(null);
  const [
    currentCandidateForFitnessReason,
    setCurrentCandidateForFitnessReason,
  ] = useState<string | null>(null);
  const [isFitnessReasonModalOpen, setIsFitnessReasonModalOpen] =
    useState(false);

  const [isVacancyDataModalOpen, setIsVacancyDataModalOpen] = useState(false);
  const [isRunConfigModalOpen, setIsRunConfigModalOpen] = useState(false);

  // + State for Archive Vacancy Dialog
  const [isArchiveModalOpen, setIsArchiveModalOpen] = useState(false);
  const [archiveRefNo, setArchiveRefNo] = useState("");
  const [isArchiving, setIsArchiving] = useState(false);

  // + State for Promote Results Dialog
  const [promoteRunId, setPromoteRunId] = useState("");
  const [promoteVacancyRefNo, setPromoteVacancyRefNo] = useState("");
  const [isPromoting, setIsPromoting] = useState(false);
  const [activeDialogTab, setActiveDialogTab] = useState("archive");

  // State for a simple notification using Vaul
  const [notificationOpen, setNotificationOpen] = useState(false);
  const [notificationTitle, setNotificationTitle] = useState("");
  const [notificationDescription, setNotificationDescription] = useState("");
  const [notificationType, setNotificationType] = useState<
    "success" | "error" | "info"
  >("info");

  // Helper to show notification
  const showNotification = (
    title: string,
    description: string,
    type: "success" | "error" | "info"
  ) => {
    setNotificationTitle(title);
    setNotificationDescription(description);
    setNotificationType(type);
    setNotificationOpen(true);
  };

  // State for URL parameters
  const [initialUrlParams, setInitialUrlParams] = useState<{
    vacancyId: string;
    vacancySubcategory: string;
    vacancyJobTitle: string;
    runId: string;
  } | null>(null);
  const [initialParamsApplied, setInitialParamsApplied] = useState(false);

  const copyToClipboard = useCallback(
    async (text: string | undefined, type: string) => {
      if (!text) {
        console.warn(`${type} is not available to copy.`); // Keep console log for debugging
        return;
      }
      try {
        await navigator.clipboard.writeText(text);
        console.log(
          `${type} copied to clipboard: ${text.substring(0, 70)}${
            text.length > 70 ? "..." : ""
          }`
        );
        // alert(`${type} copied to clipboard!\n${text.substring(0,70)}${text.length > 70 ? '...' : ''}`); // Removed alert
      } catch (err) {
        console.error(`Failed to copy ${type}:`, err);
        // alert(`Failed to copy ${type}. See console for details.`); // Removed alert
      }
    },
    []
  );

  useEffect(() => {
    if (candidates.length > 0) {
      const allKeys = new Set<string>();
      candidates.forEach((candidate) => {
        if (candidate.category_intermediate_scores) {
          Object.keys(candidate.category_intermediate_scores).forEach((key) =>
            allKeys.add(key)
          );
        }
      });
      setCategoryScoreHeaders(Array.from(allKeys).sort());
    } else {
      setCategoryScoreHeaders([]);
    }
  }, [candidates]);

  // Parse URL parameters on initial mount
  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const vacancyId = params.get("vacancyId");
    const vacancySubcategory = params.get("vacancySubcategory");
    const vacancyJobTitle = params.get("vacancyJobTitle"); // This is the raw job_title string
    const runId = params.get("runId");

    if (vacancyId && vacancySubcategory && vacancyJobTitle && runId) {
      setInitialUrlParams({
        vacancyId,
        vacancySubcategory,
        vacancyJobTitle,
        runId,
      });
    }
  }, []);

  const loadRunData = useCallback(
    async (runId: number, page: number) => {
      if (selectedVacancy && runId) {
        console.log(
          `loadRunData: Starting. runId=${runId}, page=${page}, vacancyId=${selectedVacancy.vacancy_id}`
        );
        setLoadingRunData(true);
        if (page === 1) {
          setIsRunPromoted(false);
          setIsVacancyArchived(false); // Reset archived status
        }
        try {
          //console.log("loadRunData: Fetching vacancy run details...");
          const detailsData = await fetchExperimentVacancyRunDetails(
            selectedVacancy.vacancy_id,
            runId
          );
          console.log("loadRunData: Fetched vacancy run details.", detailsData);
          if (typeof detailsData.promoted === "boolean") {
            setIsRunPromoted(detailsData.promoted);
          }
          if (typeof detailsData.vacancy_archived_status === "boolean") {
            setIsVacancyArchived(detailsData.vacancy_archived_status);
          }

          //console.log("loadRunData: Fetching experiment candidates...");
          type CandidatesData = Awaited<
            ReturnType<typeof fetchExperimentCandidatesForVacancyRun>
          >;
          const candidatesData: CandidatesData =
            await fetchExperimentCandidatesForVacancyRun(
              selectedVacancy.vacancy_id,
              runId,
              page
            );
          console.log(
            "loadRunData: Fetched experiment candidates.",
            candidatesData
          );

          //console.log("loadRunData: Fetching experiment run config...");
          const configData = await fetchExperimentRunConfig(runId);
          console.log(
            "loadRunData: Fetched experiment run config.",
            configData
          );

          setVacancyDetails(detailsData);
          setCandidates((prevCandidates) =>
            page === 1
              ? candidatesData.candidates
              : [...prevCandidates, ...candidatesData.candidates]
          );
          setTotalCandidatesPages(candidatesData.total_pages);
          // setIsRunPromoted is now set from detailsData
          // setIsVacancyArchived is now set from detailsData
          setRunConfig(configData);
          //console.log("loadRunData: State updated successfully.");
        } catch (error) {
          console.error("Error loading run data:", error);
          setIsRunPromoted(false); // Reset on error
          setIsVacancyArchived(false); // Reset on error
        } finally {
          //console.log("loadRunData: Finally block. Setting loadingRunData to false.");
          setLoadingRunData(false);
        }
      } else {
        console.warn(
          "loadRunData: Skipped due to missing selectedVacancy or runId.",
          { selectedVacancyPresent: !!selectedVacancy, runIdPresent: !!runId }
        );
        if (loadingRunData) {
          // Add this check to ensure spinner is turned off if this path is unexpectedly hit
          setLoadingRunData(false);
        }
      }
    },
    [selectedVacancy, loadingRunData] // Added loadingRunData to dependency array because it's read in the else block
  );

  const handleVacancyChange = useCallback(
    (compositeKeyValue: string) => {
      if (!compositeKeyValue) {
        setSelectedVacancy(null);
        setSelectedRunId(null);
        setIsRunPromoted(false); // Reset promoted status
        setIsVacancyArchived(false); // Reset archived status
        setCandidates([]);
        setVacancyDetails(null);
        setRunConfig(null);
        setCandidatesPage(1);
        setTotalCandidatesPages(1);
        return;
      }
      const firstSeparatorIndex = compositeKeyValue.indexOf("::");
      const secondSeparatorIndex = compositeKeyValue.indexOf(
        "::",
        firstSeparatorIndex + 2
      );

      if (firstSeparatorIndex === -1 || secondSeparatorIndex === -1) {
        console.error("Invalid compositeKeyValue format:", compositeKeyValue);
        setSelectedVacancy(null);
        setSelectedRunId(null);
        setCandidates([]);
        setVacancyDetails(null);
        setRunConfig(null);
        setCandidatesPage(1);
        setTotalCandidatesPages(1);
        return;
      }

      const id = compositeKeyValue.substring(0, firstSeparatorIndex);
      const subcat = compositeKeyValue.substring(
        firstSeparatorIndex + 2,
        secondSeparatorIndex
      );
      const jobTitle = compositeKeyValue.substring(secondSeparatorIndex + 2);

      const vacancy = vacancies.find(
        (v) =>
          v.vacancy_id === id &&
          v.subcategory === subcat &&
          v.job_title === jobTitle
      );
      setSelectedVacancy(vacancy || null);
      setSelectedRunId(null);
      setIsRunPromoted(false); // Reset promoted status
      setIsVacancyArchived(false); // Reset archived status
      setCandidates([]);
      setVacancyDetails(null);
      setRunConfig(null);
      setCandidatesPage(1);
      setTotalCandidatesPages(1);
    },
    [vacancies]
  );

  const handleRunChange = useCallback(
    async (runIdStr: string) => {
      const runId = parseInt(runIdStr, 10);
      if (isNaN(runId)) {
        console.error("Invalid runId passed to handleRunChange:", runIdStr);
        return;
      }
      setSelectedRunId(runId);
      setIsRunPromoted(false); // Reset promoted status before loading new run data
      setIsVacancyArchived(false); // Reset archived status before loading new run data
      setCandidatesPage(1);
      await loadRunData(runId, 1);
    },
    [loadRunData]
  );

  // Effect to load vacancies (initial and "load more")
  useEffect(() => {
    const loadVacancies = async () => {
      setLoadingVacancies(true);
      try {
        const data: ExperimentVacancyResponse = await fetchExperimentVacancies(
          vacanciesPage
        );
        setVacancies((prev) =>
          vacanciesPage === 1 ? data.vacancies : [...prev, ...data.vacancies]
        );
        setTotalVacanciesPages(data.total_pages);
      } catch (error) {
        console.error("Error fetching vacancies:", error);
      } finally {
        setLoadingVacancies(false);
      }
    };
    loadVacancies();
  }, [vacanciesPage]);

  // Effect to apply initialVacancyParams after vacancies are loaded
  useEffect(() => {
    if (
      initialUrlParams &&
      !initialParamsApplied &&
      vacancies.length > 0 &&
      !selectedVacancy
    ) {
      const { vacancyId, vacancySubcategory, vacancyJobTitle } =
        initialUrlParams;
      const compositeKey = `${vacancyId}::${vacancySubcategory}::${vacancyJobTitle}`;
      const vacancyExists = vacancies.some(
        (v) =>
          `${v.vacancy_id}::${v.subcategory}::${v.job_title}` === compositeKey
      );

      if (vacancyExists) {
        handleVacancyChange(compositeKey);
        // Don't set initialParamsApplied yet, wait for run to be potentially set
      } else if (vacanciesPage >= totalVacanciesPages && !loadingVacancies) {
        // All vacancies loaded (or no more pages), and the target one wasn't found.
        console.warn(
          "Initial vacancy from URL not found after loading all available pages."
        );
        setInitialParamsApplied(true); // Mark as processed to stop trying for this vacancy
      }
    }
  }, [
    vacancies,
    initialUrlParams,
    selectedVacancy,
    vacanciesPage,
    totalVacanciesPages,
    loadingVacancies,
    handleVacancyChange,
    initialParamsApplied,
  ]);

  const availableRuns = useMemo(() => {
    if (!selectedVacancy) return [];
    const uniqueRuns = new Map<number, ExperimentRun>();
    if (Array.isArray(selectedVacancy.runs)) {
      selectedVacancy.runs.forEach((run) => {
        if (!uniqueRuns.has(run.run_id)) {
          uniqueRuns.set(run.run_id, run);
        }
      });
    }
    return Array.from(uniqueRuns.values()).sort(
      (a, b) =>
        new Date(b.run_timestamp).getTime() -
        new Date(a.run_timestamp).getTime()
    );
  }, [selectedVacancy]);

  // Effect to apply initialRunIdParam after vacancy is selected (from URL) and availableRuns are populated
  useEffect(() => {
    if (
      initialUrlParams &&
      !initialParamsApplied &&
      selectedVacancy &&
      selectedVacancy.vacancy_id === initialUrlParams.vacancyId &&
      selectedVacancy.subcategory === initialUrlParams.vacancySubcategory &&
      selectedVacancy.job_title === initialUrlParams.vacancyJobTitle && // Verify it's the correct vacancy from URL
      initialUrlParams.runId &&
      availableRuns.length > 0
    ) {
      // Ensure availableRuns is populated

      const runIdToSelect = initialUrlParams.runId;
      // Check if this run ID is different from current or if no run is selected
      if (!selectedRunId || selectedRunId.toString() !== runIdToSelect) {
        const runIsValid = availableRuns.some(
          (run) => run.run_id.toString() === runIdToSelect
        );

        if (runIsValid) {
          handleRunChange(runIdToSelect);
          setInitialParamsApplied(true); // All params successfully applied
          // Clean the URL
          window.history.replaceState(
            {},
            document.title,
            window.location.pathname
          );
        } else {
          console.warn(
            `Run ID ${runIdToSelect} from URL is not available for this vacancy.`
          );
          setInitialParamsApplied(true); // Mark as processed (failed run part)
          window.history.replaceState(
            {},
            document.title,
            window.location.pathname
          );
        }
      } else if (selectedRunId && selectedRunId.toString() === runIdToSelect) {
        // Correct run is already selected, implies params were applied.
        setInitialParamsApplied(true);
        window.history.replaceState(
          {},
          document.title,
          window.location.pathname
        );
      }
    } else if (
      initialUrlParams &&
      !initialParamsApplied &&
      selectedVacancy &&
      selectedVacancy.vacancy_id === initialUrlParams.vacancyId && // if vacancy matches URL
      selectedVacancy.runs &&
      selectedVacancy.runs.length === 0 && // and explicitly has no runs
      availableRuns.length === 0
    ) {
      // and availableRuns confirms this
      console.warn(
        "Vacancy from URL has no runs. Cannot apply run_id from URL."
      );
      setInitialParamsApplied(true); // Mark as processed
      window.history.replaceState({}, document.title, window.location.pathname);
    }
  }, [
    selectedVacancy,
    initialUrlParams,
    selectedRunId,
    availableRuns,
    handleRunChange,
    initialParamsApplied,
  ]);

  const handleLoadMoreCandidates = () => {
    if (
      selectedRunId &&
      candidatesPage < totalCandidatesPages &&
      !loadingRunData
    ) {
      const nextPage = candidatesPage + 1;
      setCandidatesPage(nextPage);
      loadRunData(selectedRunId, nextPage);
    }
  };

  const handleLoadMoreVacancies = () => {
    if (vacanciesPage < totalVacanciesPages && !loadingVacancies) {
      setVacanciesPage((prev) => prev + 1);
    }
  };

  const handleViewExperimentResume = async (contactId: string) => {
    setLoadingResume(true);
    setIsExperimentResumeModalOpen(true);
    const resumeResponse: ExperimentResumeResponse | null =
      await fetchExperimentCandidateResume(contactId);
    if (resumeResponse && resumeResponse.resume_data) {
      const transformedData: TransformedResumeData = {
        candidate: resumeResponse.resume_data,
      };
      setSelectedExperimentResume(transformedData);
    } else {
      setSelectedExperimentResume(null);
      console.error(
        "Failed to fetch or parse resume data for contact ID:",
        contactId
      );
    }
    setLoadingResume(false);
  };

  const handleViewFeedbacks = (
    feedbacks: Feedback[] | null,
    candidateName: string
  ) => {
    if (feedbacks && feedbacks.length > 0) {
      setSelectedFeedbacks(feedbacks);
      setCurrentCandidateForFeedback(candidateName);
      setIsFeedbacksModalOpen(true);
    } else {
      setSelectedFeedbacks(null);
      setCurrentCandidateForFeedback(null);
    }
  };

  const handleViewFitnessReason = (
    fitnessReason: FitnessReason | null,
    candidateName: string
  ) => {
    if (fitnessReason) {
      setSelectedFitnessReason(fitnessReason);
      setCurrentCandidateForFitnessReason(candidateName);
      setIsFitnessReasonModalOpen(true);
    } else {
      setSelectedFitnessReason(null);
      setCurrentCandidateForFitnessReason(null);
    }
  };

  const getDisplayJobTitle = (jobTitleStr: string): string => {
    if (typeof jobTitleStr !== "string") return String(jobTitleStr);
    try {
      const parsed = JSON.parse(jobTitleStr);
      if (Array.isArray(parsed)) {
        return parsed.join(", ");
      }
      return jobTitleStr;
    } catch (error) {
      console.error("Error getting the job titles:", error);
      return jobTitleStr;
    }
  };

  const getQuickVoteIcon = (feedbacks: Feedback[] | null) => {
    if (!feedbacks || feedbacks.length === 0) {
      return <span className="text-gray-400">-</span>;
    }
    const firstFeedbackVote = feedbacks[0].vote;
    return <FeedbackVoteIcon vote={firstFeedbackVote} />;
  };

  // + Handler for submitting the archive vacancy form
  const handleArchiveVacancySubmit = async () => {
    if (!archiveRefNo.trim()) {
      console.warn("Vacancy Reference Number is required for archiving.");
      showNotification(
        "Input Required",
        "Vacancy Reference Number is required.",
        "error"
      );
      return;
    }
    setIsArchiving(true);
    const result = await archiveExperimentVacancy(archiveRefNo);
    setIsArchiving(false);

    if (result.success) {
      console.log("Vacancy archived successfully:", result.data);
      showNotification("Success", "Vacancy archived successfully!", "success");
      setIsArchiveModalOpen(false);
      setArchiveRefNo("");
      // Optionally, refresh data or update UI state
      // For example, if the current vacancy was archived, you might want to clear selections
      if (selectedVacancy && selectedVacancy.refno === archiveRefNo) {
        handleVacancyChange(""); // Reset vacancy selection
      }
    } else {
      console.error("Failed to archive vacancy:", result.error);
      showNotification(
        "Archive Failed",
        result.error || "An unknown error occurred.",
        "error"
      );
    }
  };

  // + Handler for submitting the promote results form
  const handlePromoteResultsSubmit = async () => {
    if (!promoteRunId.trim() || !promoteVacancyRefNo.trim()) {
      showNotification(
        "Input Required",
        "Run ID and Vacancy RefNo are required.",
        "error"
      );
      return;
    }
    const runIdNum = parseInt(promoteRunId, 10);
    if (isNaN(runIdNum)) {
      showNotification("Invalid Input", "Run ID must be a number.", "error");
      return;
    }

    setIsPromoting(true);
    const result = await promoteExperimentResults(
      runIdNum,
      promoteVacancyRefNo
    );
    setIsPromoting(false);

    if (result.success) {
      console.log("Results promoted successfully:", result.data);
      showNotification("Success", "Results promoted successfully!", "success");
      setIsArchiveModalOpen(false); // Close the main dialog
      setPromoteRunId("");
      setPromoteVacancyRefNo("");
      // Optionally, refresh data if the promotion affects the current view
      if (
        selectedRunId === runIdNum &&
        selectedVacancy &&
        selectedVacancy.refno === promoteVacancyRefNo
      ) {
        // Reload current run data to reflect promoted status
        loadRunData(selectedRunId, 1);
      }
    } else {
      console.error("Failed to promote results:", result.error);
      showNotification(
        "Promotion Failed",
        result.error || "An unknown error occurred.",
        "error"
      );
    }
  };

  return (
    <div className="w-full p-4 md:p-6 lg:p-8 space-y-6 bg-gray-50 min-h-screen">
      <header className="relative py-6 bg-white shadow-md rounded-lg">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-800">
            Experiment Results Viewer
          </h1>
          <p className="text-md text-gray-600 mt-1">
            Analyze candidate ranking based on different experimental
            configurations.
          </p>
        </div>
        <div className="absolute top-4 right-4 md:top-6 md:right-6">
          <Dialog
            open={isArchiveModalOpen}
            onOpenChange={(isOpen) => {
              setIsArchiveModalOpen(isOpen);
              if (!isOpen) {
                // Reset states when dialog closes
                setArchiveRefNo("");
                setPromoteRunId("");
                setPromoteVacancyRefNo("");
                setActiveDialogTab("archive"); // Reset to default tab
              }
            }}
          >
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <DialogTrigger asChild>
                    <Button variant="outline" size="icon" className="p-2">
                      <Settings className="h-5 w-5" />
                    </Button>
                  </DialogTrigger>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Actions</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            <DialogContent className="sm:max-w-[480px]">
              <DialogHeader>
                <DialogTitle>Actions</DialogTitle>
                <DialogDescription>
                  Perform administrative actions like archiving vacancies or
                  promoting results.
                </DialogDescription>
              </DialogHeader>
              <Tabs
                value={activeDialogTab}
                onValueChange={setActiveDialogTab}
                className="w-full mt-4"
              >
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="archive">Archive Vacancy</TabsTrigger>
                  <TabsTrigger value="promote">Promote Results</TabsTrigger>
                </TabsList>
                <TabsContent value="archive">
                  <div className="grid gap-4 py-4">
                    <p className="text-sm text-muted-foreground">
                      Enter the Vacancy Reference Number (RefNo) to archive.
                      This action will archive the vacancy and its associated
                      candidates.
                    </p>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <label
                        htmlFor="archive-refno"
                        className="text-right col-span-1"
                      >
                        RefNo
                      </label>
                      <Input
                        id="archive-refno"
                        value={archiveRefNo}
                        onChange={(e) => setArchiveRefNo(e.target.value)}
                        className="col-span-3"
                        placeholder="Enter Vacancy RefNo"
                      />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => {
                        setIsArchiveModalOpen(false);
                      }}
                      disabled={isArchiving}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      onClick={handleArchiveVacancySubmit}
                      disabled={isArchiving || !archiveRefNo.trim()}
                    >
                      {isArchiving ? "Archiving..." : "Archive"}
                    </Button>
                  </DialogFooter>
                </TabsContent>
                <TabsContent value="promote">
                  <div className="grid gap-4 py-4">
                    <p className="text-sm text-muted-foreground">
                      Enter the Run ID and Vacancy RefNo to promote the
                      experiment results.
                    </p>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <label
                        htmlFor="promote-runid"
                        className="text-right col-span-1"
                      >
                        Run ID
                      </label>
                      <Input
                        id="promote-runid"
                        value={promoteRunId}
                        onChange={(e) => setPromoteRunId(e.target.value)}
                        className="col-span-3"
                        placeholder="Enter Run ID"
                        type="number"
                      />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <label
                        htmlFor="promote-refno"
                        className="text-right col-span-1"
                      >
                        RefNo
                      </label>
                      <Input
                        id="promote-refno"
                        value={promoteVacancyRefNo}
                        onChange={(e) => setPromoteVacancyRefNo(e.target.value)}
                        className="col-span-3"
                        placeholder="Enter Vacancy RefNo"
                      />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => {
                        setIsArchiveModalOpen(false);
                      }}
                      disabled={isPromoting}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      onClick={handlePromoteResultsSubmit}
                      disabled={
                        isPromoting ||
                        !promoteRunId.trim() ||
                        !promoteVacancyRefNo.trim()
                      }
                    >
                      {isPromoting ? "Promoting..." : "Promote"}
                    </Button>
                  </DialogFooter>
                </TabsContent>
              </Tabs>
            </DialogContent>
          </Dialog>
        </div>
      </header>

      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="text-xl">Selection Panel</CardTitle>
          <CardDescription>
            Select a vacancy and a specific run to view its details.
          </CardDescription>
        </CardHeader>
        <TooltipProvider delayDuration={0}>
          {" "}
          {/* Added TooltipProvider with immediate delay */}
          <CardContent className="grid md:grid-cols-2 gap-6 p-6">
            <div>
              <label
                htmlFor="vacancy-select"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Select Vacancy ({vacancies.length} loaded /{" "}
                {totalVacanciesPages > 1
                  ? `${totalVacanciesPages} pages`
                  : "1 page"}
                )
              </label>
              <div className="flex items-center space-x-2">
                <div className="flex-grow">
                  <Select
                    onValueChange={handleVacancyChange}
                    value={
                      selectedVacancy
                        ? `${selectedVacancy.vacancy_id}::${selectedVacancy.subcategory}::${selectedVacancy.job_title}`
                        : ""
                    }
                  >
                    <SelectTrigger id="vacancy-select" className="w-full">
                      <SelectValue placeholder="Choose a vacancy..." />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectGroup>
                        <SelectLabel>Vacancies</SelectLabel>
                        {vacancies.map((vacancy) => {
                          const compositeKey = `${vacancy.vacancy_id}::${vacancy.subcategory}::${vacancy.job_title}`;
                          const displayJobTitle = getDisplayJobTitle(
                            vacancy.job_title
                          );
                          return (
                            <SelectItem key={compositeKey} value={compositeKey}>
                              {vacancy.refno} - {vacancy.subcategory} -{" "}
                              {displayJobTitle}
                            </SelectItem>
                          );
                        })}
                        {loadingVacancies && (
                          <SelectItem value="loading_vac" disabled>
                            Loading more vacancies...
                          </SelectItem>
                        )}
                        {!loadingVacancies &&
                          vacanciesPage < totalVacanciesPages && (
                            <Button
                              onClick={handleLoadMoreVacancies}
                              variant="link"
                              className="w-full justify-start p-2 text-sm text-blue-600 hover:text-blue-800"
                            >
                              Load More Vacancies (
                              {totalVacanciesPages - vacanciesPage} more
                              page(s))
                            </Button>
                          )}
                      </SelectGroup>
                    </SelectContent>
                  </Select>
                </div>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      // title="Copy Vacancy RefNo" // Removed title attribute
                      onClick={() =>
                        copyToClipboard(selectedVacancy?.refno, "Vacancy RefNo")
                      }
                      disabled={!selectedVacancy}
                      variant="outline"
                      size="icon"
                      className="p-2 h-9 w-9" // Adjusted size for consistency
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Copy Vacancy RefNo</p>
                  </TooltipContent>
                </Tooltip>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      // title="Copy Vacancy ID" // Removed title attribute
                      onClick={() =>
                        copyToClipboard(
                          selectedVacancy?.vacancy_id,
                          "Vacancy ID"
                        )
                      }
                      disabled={!selectedVacancy}
                      variant="outline"
                      size="icon"
                      className="p-2 h-9 w-9" // Adjusted size
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Copy Vacancy ID</p>
                  </TooltipContent>
                </Tooltip>
              </div>
            </div>
            <div>
              <label
                htmlFor="run-select"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Select Run
              </label>
              <div className="flex items-center space-x-2">
                <div className="flex-grow">
                  <Select
                    onValueChange={handleRunChange}
                    value={selectedRunId?.toString() || ""}
                    disabled={!selectedVacancy || availableRuns.length === 0}
                  >
                    <SelectTrigger id="run-select" className="w-full">
                      <SelectValue
                        placeholder={
                          selectedVacancy
                            ? availableRuns.length > 0
                              ? "Choose a run..."
                              : "No runs available"
                            : "Select a vacancy first"
                        }
                      />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectGroup>
                        <SelectLabel>Available Runs</SelectLabel>
                        {availableRuns.map((run) => (
                          <SelectItem
                            key={run.run_id}
                            value={run.run_id.toString()}
                          >
                            {run.run_tag} (
                            {unFormattedDateWithBrowserTimezone(
                              run.run_timestamp
                            )}
                            )
                          </SelectItem>
                        ))}
                        {selectedVacancy &&
                          availableRuns.length === 0 &&
                          !loadingRunData && ( // Show only if not loading
                            <SelectItem value="no_runs" disabled>
                              No runs available for this vacancy.
                            </SelectItem>
                          )}
                      </SelectGroup>
                    </SelectContent>
                  </Select>
                </div>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      // title="Copy Run ID" // Removed title attribute
                      onClick={() =>
                        copyToClipboard(selectedRunId?.toString(), "Run ID")
                      }
                      disabled={!selectedRunId}
                      variant="outline"
                      size="icon"
                      className="p-2 h-9 w-9" // Adjusted size
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Copy Run ID</p>
                  </TooltipContent>
                </Tooltip>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      // title="Copy Direct Link to Experiment" // Removed title attribute
                      onClick={() => {
                        if (selectedVacancy && selectedRunId) {
                          const params = new URLSearchParams();
                          params.append(
                            "vacancyId",
                            selectedVacancy.vacancy_id
                          );
                          params.append(
                            "vacancySubcategory",
                            selectedVacancy.subcategory
                          );
                          params.append(
                            "vacancyJobTitle",
                            selectedVacancy.job_title
                          ); // Use raw job_title
                          params.append("runId", selectedRunId.toString());
                          const url = `${window.location.origin}${
                            window.location.pathname
                          }?${params.toString()}`;
                          copyToClipboard(url, "Experiment URL");
                        }
                      }}
                      disabled={!selectedVacancy || !selectedRunId}
                      variant="outline"
                      size="icon"
                      className="p-2 h-9 w-9" // Adjusted size
                    >
                      <Link className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Copy Direct Link to Experiment</p>
                  </TooltipContent>
                </Tooltip>
              </div>
            </div>
          </CardContent>
        </TooltipProvider>{" "}
        {/* Closed TooltipProvider */}
      </Card>

      {/* ... (rest of your component, including modals and table, remains unchanged) ... */}
      {loadingRunData && candidates.length === 0 && (
        <div className="flex justify-center py-10">
          <Loading height="h-[200px]" />
        </div>
      )}

      {!loadingRunData && selectedVacancy && selectedRunId && (
        <div className="grid md:grid-cols-2 gap-6">
          {vacancyDetails && (
            <Dialog
              open={isVacancyDataModalOpen}
              onOpenChange={setIsVacancyDataModalOpen}
            >
              <DialogTrigger asChild>
                <Button variant="outline" className="w-full">
                  View Vacancy Data for Run
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[600px] md:max-w-[800px] lg:max-w-[1000px] max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle className="text-lg">
                    Vacancy Data Used for Run: {vacancyDetails.run_tag}
                  </DialogTitle>
                  <DialogDescription>
                    Ref No: {vacancyDetails.refno} | Subcategory:{" "}
                    {vacancyDetails.subcategory}
                  </DialogDescription>
                </DialogHeader>
                <pre className="bg-gray-100 p-4 rounded-md text-xs overflow-x-auto mt-4">
                  {JSON.stringify(
                    vacancyDetails.vacancy_data_used_for_run,
                    null,
                    2
                  )}
                </pre>
                <DialogFooter className="mt-4">
                  <Button
                    type="button"
                    variant="secondary"
                    onClick={() => setIsVacancyDataModalOpen(false)}
                  >
                    Close
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          )}

          {runConfig && (
            <Dialog
              open={isRunConfigModalOpen}
              onOpenChange={setIsRunConfigModalOpen}
            >
              <DialogTrigger asChild>
                <Button variant="outline" className="w-full">
                  View Run Configuration
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[600px] md:max-w-[800px] lg:max-w-[1000px] max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle className="text-lg">
                    Run Configuration: {runConfig.run_tag}
                  </DialogTitle>
                  <DialogDescription>
                    Timestamp:{" "}
                    {unFormattedDateWithBrowserTimezone(
                      runConfig.run_timestamp
                    )}
                  </DialogDescription>
                </DialogHeader>
                <pre className="bg-gray-100 p-4 rounded-md text-xs overflow-x-auto mt-4">
                  {JSON.stringify(
                    runConfig.cleaned_effective_config_json,
                    null,
                    2
                  )}
                </pre>
                <DialogFooter className="mt-4">
                  <Button
                    type="button"
                    variant="secondary"
                    onClick={() => setIsRunConfigModalOpen(false)}
                  >
                    Close
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          )}
        </div>
      )}

      {!loadingRunData && candidates.length > 0 && (
        <Card className="shadow-lg mt-6">
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="text-xl">Ranked Candidates</CardTitle>
              <div className="flex items-center space-x-2">
                {isRunPromoted && (
                  <span className="text-sm font-semibold text-green-700 bg-green-100 px-3 py-1 rounded-full flex items-center">
                    <CheckCircle2 className="h-4 w-4 mr-1.5" />
                    Promoted
                  </span>
                )}
                {isVacancyArchived && (
                  <span className="text-sm font-semibold text-orange-700 bg-orange-100 px-3 py-1 rounded-full flex items-center">
                    <ArchiveIcon className="h-4 w-4 mr-1.5" />
                    Archived
                  </span>
                )}
              </div>
            </div>
            <CardDescription>
              Showing {candidates.length} candidates for the selected run. Page{" "}
              {candidatesPage} of {totalCandidatesPages}.
            </CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow className="bg-gray-100">
                    <TableHead className="px-4 py-3 sticky left-0 bg-gray-100 z-10">
                      Rank
                    </TableHead>
                    <TableHead className="px-4 py-3">Name</TableHead>
                    <TableHead className="px-4 py-3">Email</TableHead>
                    <TableHead className="px-4 py-3">
                      Normalized Score
                    </TableHead>
                    {categoryScoreHeaders.map((headerKey) => (
                      <TableHead
                        key={headerKey}
                        className="px-4 py-3 whitespace-nowrap"
                      >
                        {formatCategoryKey(headerKey)}
                      </TableHead>
                    ))}
                    <TableHead className="px-4 py-3">Candidate State</TableHead>
                    <TableHead className="px-4 py-3 text-center">
                      Feedback
                    </TableHead>
                    <TableHead className="px-4 py-3 text-center sticky right-0 bg-gray-100 z-10">
                      Actions
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {candidates.map((candidate) => (
                    <TableRow
                      key={candidate.application_id}
                      className="hover:bg-gray-50 group"
                    >
                      <TableCell className="px-4 py-3 sticky left-0 bg-white group-hover:bg-gray-50 z-10">
                        {candidate.rank}
                      </TableCell>
                      <TableCell className="px-4 py-3">
                        {candidate.candidate_name}
                      </TableCell>
                      <TableCell className="px-4 py-3">
                        {candidate.candidate_email}
                      </TableCell>
                      <TableCell className="px-4 py-3">
                        {parseFloat(
                          candidate.calculated_normalized_score
                        ).toFixed(4)}
                      </TableCell>
                      {categoryScoreHeaders.map((headerKey) => {
                        const score =
                          candidate.category_intermediate_scores?.[headerKey];
                        const displayValue =
                          typeof score === "number" && !isNaN(score)
                            ? score.toFixed(4)
                            : "N/A";
                        return (
                          <TableCell key={headerKey} className="px-4 py-3">
                            {displayValue}
                          </TableCell>
                        );
                      })}
                      <TableCell className="px-4 py-3">
                        {candidate.candidate_state || "N/A"}
                      </TableCell>
                      <TableCell className="px-4 py-3 text-center">
                        {getQuickVoteIcon(candidate.feedbacks)}
                      </TableCell>
                      <TableCell className="px-4 py-3 text-center sticky right-0 bg-white group-hover:bg-gray-50 z-10 space-x-1 md:space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            handleViewExperimentResume(candidate.contact_id)
                          }
                          aria-label={`View resume for ${candidate.candidate_name}`}
                          className="hover:bg-gray-200 p-1"
                          title="View Candidate Resume"
                        >
                          Resume
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            handleViewFeedbacks(
                              candidate.feedbacks,
                              candidate.candidate_name
                            )
                          }
                          disabled={
                            !candidate.feedbacks ||
                            candidate.feedbacks.length === 0
                          }
                        >
                          Feedback ({candidate.feedbacks?.length || 0})
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            handleViewFitnessReason(
                              candidate.fitness_reason,
                              candidate.candidate_name
                            )
                          }
                          disabled={!candidate.fitness_reason}
                        >
                          Why Fit
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
            {loadingRunData &&
              candidatesPage > 1 && ( // Show loading only when loading more, not initial load
                <div className="text-center py-4">
                  <Loading height="h-10" />
                </div>
              )}
            {!loadingRunData && candidatesPage < totalCandidatesPages && (
              <div className="mt-4 text-center p-4 border-t">
                <Button onClick={handleLoadMoreCandidates} variant="outline">
                  Load More Candidates ({totalCandidatesPages - candidatesPage}{" "}
                  more page(s))
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      )}
      {!loadingRunData && selectedRunId && candidates.length === 0 && (
        // Condition to show "No candidates" only if not loading and a run is selected
        <p className="text-center text-gray-600 mt-6 py-4 bg-white rounded-lg shadow">
          No candidates found for this run.
        </p>
      )}

      <ExperimentCandidateResume
        selectedResume={selectedExperimentResume}
        setSelectedResume={setSelectedExperimentResume}
        isResumeModalOpen={isExperimentResumeModalOpen}
        setIsResumeModalOpen={setIsExperimentResumeModalOpen}
        isLoading={loadingResume}
      />

      {/* Feedbacks Modal */}
      <Dialog
        open={isFeedbacksModalOpen}
        onOpenChange={setIsFeedbacksModalOpen}
      >
        <DialogContent className="sm:max-w-[500px] md:max-w-[700px] max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              Feedbacks for {currentCandidateForFeedback || "Candidate"}
            </DialogTitle>
            <DialogDescription>Showing all feedback entries.</DialogDescription>
          </DialogHeader>
          <div className="mt-4 space-y-4">
            {selectedFeedbacks && selectedFeedbacks.length > 0 ? (
              selectedFeedbacks.map((fb, index) => (
                <div
                  key={index}
                  className="p-3 border rounded-md bg-gray-50 shadow-sm"
                >
                  <p className="text-sm text-gray-800">
                    <span className="font-semibold">Vote:</span> {fb.vote}
                  </p>
                  {fb.comment && (
                    <p className="text-sm text-gray-800 mt-1">
                      <span className="font-semibold">Comment:</span>{" "}
                      {fb.comment}
                    </p>
                  )}
                  <p className="text-xs text-gray-500 mt-1">
                    <span className="font-semibold">Reviewer:</span>{" "}
                    {fb.reviewer_email}
                  </p>
                  <p className="text-xs text-gray-500">
                    <span className="font-semibold">Date:</span>{" "}
                    {unFormattedDateWithBrowserTimezone(fb.feedback_date)}
                  </p>
                </div>
              ))
            ) : (
              <p className="text-gray-500">No feedback entries available.</p>
            )}
          </div>
          <DialogFooter className="mt-6">
            <Button
              variant="outline"
              onClick={() => setIsFeedbacksModalOpen(false)}
            >
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Fitness Reason Modal */}
      <Dialog
        open={isFitnessReasonModalOpen}
        onOpenChange={setIsFitnessReasonModalOpen}
      >
        <DialogContent className="sm:max-w-[500px] md:max-w-[700px] max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              Fitness Reason for{" "}
              {currentCandidateForFitnessReason || "Candidate"}
            </DialogTitle>
            <DialogDescription>
              Showing current and historical fitness reasons.
            </DialogDescription>
          </DialogHeader>
          <div className="mt-4 space-y-4">
            {selectedFitnessReason?.current && (
              <div>
                <h3 className="text-md font-semibold text-gray-700 mb-2">
                  Current Reason:
                </h3>
                <div className="p-3 border rounded-md bg-blue-50 shadow-sm">
                  <p className="text-sm text-gray-800">
                    {selectedFitnessReason.current.reason}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    <span className="font-semibold">Author:</span>{" "}
                    {selectedFitnessReason.current.author}
                  </p>
                  <p className="text-xs text-gray-500">
                    <span className="font-semibold">Date:</span>{" "}
                    {unFormattedDateWithBrowserTimezone(
                      selectedFitnessReason.current.timestamp
                    )}
                  </p>
                </div>
              </div>
            )}
            {selectedFitnessReason &&
              selectedFitnessReason.history &&
              selectedFitnessReason.history.length > 0 && (
                <div>
                  <h3 className="text-md font-semibold text-gray-700 mt-4 mb-2">
                    History:
                  </h3>
                  <div className="space-y-3 max-h-[40vh] overflow-y-auto pr-2">
                    {selectedFitnessReason.history.map((entry, index) => (
                      <div
                        key={index}
                        className="p-3 border rounded-md bg-gray-50 shadow-sm"
                      >
                        <p className="text-sm text-gray-800">{entry.reason}</p>
                        <p className="text-xs text-gray-500 mt-1">
                          <span className="font-semibold">Author:</span>{" "}
                          {entry.author}
                        </p>
                        <p className="text-xs text-gray-500">
                          <span className="font-semibold">Date:</span>{" "}
                          {unFormattedDateWithBrowserTimezone(entry.timestamp)}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            {!selectedFitnessReason?.current &&
              (!selectedFitnessReason?.history ||
                selectedFitnessReason.history.length === 0) && (
                <p className="text-gray-500">
                  No fitness reason entries available.
                </p>
              )}
          </div>
          <DialogFooter className="mt-6">
            <Button
              variant="outline"
              onClick={() => setIsFitnessReasonModalOpen(false)}
            >
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Notification Drawer using Vaul */}
      <Drawer open={notificationOpen} onOpenChange={setNotificationOpen}>
        <DrawerContent className="sm:max-w-md mx-auto">
          <DrawerHeader className="text-left">
            <DrawerTitle className="flex items-center">
              {notificationType === "success" && (
                <CheckCircle2 className="h-5 w-5 text-green-500 mr-2" />
              )}
              {notificationType === "error" && (
                <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
              )}
              {notificationType === "info" && (
                <Info className="h-5 w-5 text-blue-500 mr-2" />
              )}
              {notificationTitle}
            </DrawerTitle>
            <DrawerDescription>{notificationDescription}</DrawerDescription>
          </DrawerHeader>
          <DrawerFooter className="pt-2">
            <DrawerClose asChild>
              <Button variant="outline">Close</Button>
            </DrawerClose>
          </DrawerFooter>
        </DrawerContent>
      </Drawer>
    </div>
  );
};

export default ExperimentsPage;
