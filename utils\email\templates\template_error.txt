Hello {{ recruiter_name }},

We encountered an issue with the job template  in Advert Text 1 for vacancy {{ vacancy_ref }} that prevents us from enabling Catalyst Match:

TEMPLATE ERROR: {{ error_description }}

To resolve this issue and enable Catalyst Match for this vacancy:

1. Click the link below to access the vacancy in Mercury
2. Review and update the job template with the missing information
3. Save the changes
4. Re-enable Catalyst Match for this vacancy

Open Vacancy "{{ vacancy_ref }}: {{ vacancy_title }}" in Mercury:
{{ vacancy_url }}

Note: Catalyst Match has been temporarily disabled for this vacancy until the template is corrected.

If you need assistance with updating the job template or have any questions, please submit a ticket to RevOps-Catalyst Match Support.

Thank you,
Tandym Catalyst Automation Team