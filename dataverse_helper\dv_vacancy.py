import logging
from dataverse_helper.dv_common import read_fields_from_dataverse, add_row_to_dataverse, update_row_in_dataverse, search_dataverse_table, delete_row_from_dataverse
from common.CustomExceptions import DataverseError

class VacancyDataverseHelper:
    """
    Helper class for vacancy-related Dataverse operations.
    """
    def __init__(self, token, dataverse_url, logger=None):
        self.token = token
        self.dataverse_url = dataverse_url
        self.logger = logger or logging.getLogger(__name__)

    def fetch_vacancies(self, fields, where_clause=None, expand=None, additional_headers=None):
        """
        Fetch vacancies from the Dataverse 'crimson_vacancy' table.
        Args:
            fields (list): List of fields to fetch.
            where_clause (str, optional): OData filter condition.
            expand (str, optional): OData $expand query string.
        Returns:
            list: List of vacancy records (dicts)
        """
        response = read_fields_from_dataverse(
            self.token,
            self.dataverse_url,
            'crimson_vacancy',
            fields,
            where_clause,
            logger=self.logger,
            expand=expand,
            additional_headers=additional_headers
        )
        
        if response and 'value' in response:
            records = response['value']
            # Flatten expanded columns and remove the original dict
            flattened_records = []
            for record in records:
                flat_record = record.copy()
                keys_to_remove = []
                for key, value in record.items():
                    if isinstance(value, dict):
                        for subkey, subval in value.items():
                            flat_record[f"{key}.{subkey}"] = subval
                        keys_to_remove.append(key)
                for key in keys_to_remove:
                    flat_record.pop(key, None)
                flattened_records.append(flat_record)
            return flattened_records
        return []

    def update_vacancy(self, vacancy_id, updated_data, dryRun=False):
        """
        Update a vacancy record in Dataverse.
        
        Args:
            vacancy_id (str): The ID of the vacancy to update
            updated_data (dict): Dictionary containing the fields to update
            dryRun (bool): If True, simulates the update without making actual changes
            
        Returns:
            Response object from the update request or a simulated response if dryRun is True
        """
        if dryRun:
            self.logger.info(f"[DRY RUN] Would update vacancy {vacancy_id} with data: {updated_data}")
            # Create a simulated successful response
            class SimulatedResponse:
                def __init__(self):
                    self.status_code = 204
                    self.text = "Dry run - no actual update performed"
            return SimulatedResponse()
            
        response = update_row_in_dataverse(
            self.token,
            self.dataverse_url,
            'crimson_vacancy',
            vacancy_id,
            updated_data
        )
        return response

    def shortlist_candidate_for_vacancy(self, vacancy_id, candidate_id, reviewer_email):
        """
        Shortlist a candidate for a vacancy.
        Args:
            vacancy_id (str): The ID of the vacancy to update
            candidate_id (str): The ID of the candidate to shortlist
            reviewer_email (str): The email of the reviewer
            
        Returns:
            Response object from the update request or a simulated response if dryRun is True
        """
            
        # Step 1:
        # Fetch the User's Azure Object ID (azureactivedirectoryobjectid) from SystemUsers based on the reviewer_email
        # https://tandymgroup-sandbox.api.crm.dynamics.com/api/data/v9.1/systemusers?$filter=domainname eq '<EMAIL>'

        search_conditions = [('domainname', reviewer_email)]
        response = search_dataverse_table(self.dataverse_url, 'systemuser', search_conditions, self.token, count=True)
        if response and response.status_code == 200 and 'value' in response.json() and response.json()['@odata.count'] > 0:
            reviewer_object_id = response.json()['value'][0]['azureactivedirectoryobjectid']
        else:
            raise DataverseError(f"Invalid reviewer email: '{reviewer_email}' not found in system users. Please verify the email address is correct and the user exists in the system.")

        # Step 2:
        # Prepare the payload to shortlist a candidate for a vacancy
        # Reference: Feature 4181 - https://dev.azure.com/TandymGroup/Mercury%20Portal/_workitems/edit/4181
        # Use the Azure Object ID to set the CallerObjectId header
        additional_headers = {
            "If-None-Match": "null",
            "CallerObjectId": reviewer_object_id
        }

        payload = {
            "<EMAIL>": self.dataverse_url + "/api/data/v9.1/crimson_vacancies(" + vacancy_id + ")",
            "<EMAIL>": self.dataverse_url + "/api/data/v9.1/contacts(" + candidate_id + ")"
        }

        response = add_row_to_dataverse(
            self.token,
            self.dataverse_url,
            'crimson_vacancycandidate',
            payload,
            additional_headers
        )
        
        # Check for successful response (204 No Content or 201 Created on success)
        if response.status_code in [204, 201]:
            self.logger.info(f"Successfully shortlisted candidate {candidate_id} for vacancy {vacancy_id} by reviewer {reviewer_email}")
            
            crimson_vacancycandidateid = None
            
            # Step 3: Note that we only receive 201 or 204 for success.
            # Validate if the candidate was really shortlisted for the vacancy.
            # If the candidate was shortlisted, we will get the crimson_vacancycandidateid 
            # but there could be a delay in the dataverse table update.
            search_conditions = [('_crimson_vacancyid_value', vacancy_id), ('_recruit_candidatecontact_value', candidate_id)]
            validateresponse = search_dataverse_table(self.dataverse_url, 'crimson_vacancycandidate', search_conditions, self.token, additional_headers=additional_headers, count=True)
            if validateresponse and validateresponse.status_code == 200 and 'value' in validateresponse.json() and validateresponse.json()['@odata.count'] > 0:
                self.logger.info(f"Validation response: {validateresponse.json()}")
                crimson_vacancycandidateid = validateresponse.json()['value'][0]['crimson_vacancycandidateid']
                self.logger.info(f"Crimson Vacancy Candidate ID: {crimson_vacancycandidateid}")
            else:
                self.logger.warning(f"Could not validate shortlist creation for candidate {candidate_id} and vacancy {vacancy_id}")

            # Return both the response and the ID
            return {
                "response": response,
                "crimson_vacancycandidateid": crimson_vacancycandidateid,
                "success": True
            }
        else:
            # Handle error response
            error_message = f"Failed to shortlist candidate {candidate_id} for vacancy {vacancy_id}. Status code: {response.status_code}"
            try:
                error_details = response.json() if response.text else "No error details available"
                error_message += f", Response: {error_details}"
            except:
                error_message += f", Response: {response.text}"
            
            self.logger.error(error_message)
            raise DataverseError(error_message)

    def unshortlist_candidate_for_vacancy(self, vacancy_id, candidate_id, reviewer_email, crimson_vacancycandidateid):
        """
        Remove a candidate from the shortlist for a vacancy.
        Args:
            vacancy_id (str): The ID of the vacancy
            candidate_id (str): The ID of the candidate to unshortlist
            reviewer_email (str): The email of the reviewer
            crimson_vacancycandidateid (str): The Dataverse shortlist record ID
            
        Returns:
            Response object from the delete request
        """
            
        # Step 1:
        # Fetch the User's Azure Object ID (azureactivedirectoryobjectid) from SystemUsers based on the reviewer_email
        search_conditions = [('domainname', reviewer_email)]
        response = search_dataverse_table(self.dataverse_url, 'systemuser', search_conditions, self.token, count=True)
        if response and response.status_code == 200 and 'value' in response.json() and response.json()['@odata.count'] > 0:
            reviewer_object_id = response.json()['value'][0]['azureactivedirectoryobjectid']
        else:
            raise DataverseError(f"Invalid reviewer email: '{reviewer_email}' not found in system users. Please verify the email address is correct and the user exists in the system.")

        # Step 2:
        # Use the Azure Object ID to set the CallerObjectId header
        additional_headers = {
            "If-None-Match": "null"
            # "CallerObjectId": reviewer_object_id
        }

        # Step 3:
        # Delete the shortlist record from Dataverse
        response = delete_row_from_dataverse(
            self.token,
            self.dataverse_url,
            'crimson_vacancycandidate',
            crimson_vacancycandidateid,
            additional_headers
        )
        
        # Check for successful response (204 No Content on success)
        if response.status_code == 204:
            self.logger.info(f"Successfully unshortlisted candidate {candidate_id} for vacancy {vacancy_id} by reviewer {reviewer_email}")
            return {
                "response": response,
                "success": True
            }
        else:
            # Handle error response
            error_message = f"Failed to unshortlist candidate {candidate_id} for vacancy {vacancy_id}. Status code: {response.status_code}"
            try:
                error_details = response.json() if response.text else "No error details available"
                error_message += f", Response: {error_details}"
            except:
                error_message += f", Response: {response.text}"
            
            self.logger.error(error_message)
            raise DataverseError(error_message)

    # Future methods for vacancy operations can be added here. 
    