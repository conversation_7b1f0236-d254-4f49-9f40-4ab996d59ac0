from openai import OpenAI
import json
import os
import dotenv
import sys
from generator.vacancy_prompt import job_template_prompt, job_posting_prompt, vacancy_validation_prompt
import re

def extract_and_parse_json(text: str) -> dict:
    """
    Extracts a JSON block from a string containing triple-backtick fences (```json ... ```)
    and parses it into a Python dict.
    """
    # 1. Extract JSON between ```json and ```
    match = re.search(r'```json\s*(\{.*?\})\s*```', text, re.DOTALL)
    if not match:
        raise ValueError("No JSON block found in text.")
    
    json_block = match.group(1)
    
    # 2. Parse JSON
    return json.loads(json_block)

def extract_job_template_json(job_temp_text, logger):
    logger.info("sending to chatgpt")
    
    prompt = job_template_prompt['instructions'] + job_temp_text

    client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))

    response = client.responses.create(
    model="gpt-4.1",
    input=[
        {
            "role": "user",
            "content": prompt
        }
    ]
    )
    
    return extract_and_parse_json(response.output_text)

def create_adverttext3(recruiter_notes, logger):
    logger.info("sending to chatgpt")
    
    prompt = job_posting_prompt['instructions'] + recruiter_notes

    client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))

    response = client.responses.create(
    model="gpt-4o",

    input=[
        {
            "role": "user",
            "content": prompt
        }
    ]
    )
    
    return response.output_text

def validate_vacancy_json(job_matching_json, recruiter_notes, logger):
    logger.info("sending to chatgpt")
    
    prompt = vacancy_validation_prompt['instructions'] + "\nhere is the job_matching_json: " + json.dumps(job_matching_json) + "\nhere is the recruiter_notes: " + recruiter_notes
    
    client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
    
    response = client.responses.create(
    model="o3",
    input=[
        {
            "role": "user",
            "content": prompt
        }
    ]
    )
    return response.output_text