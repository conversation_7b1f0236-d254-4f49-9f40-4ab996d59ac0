import requests
import urllib.parse
import json
from common.appLogger import getGlobalAppLogger
from common.CustomExceptions import Dataverse<PERSON>rror
from dataverse_helper.request_respone import make_http_request

def read_fields_from_dataverse(token, dataverse_url, table_name, fields, whereClause=None, logger=None, expand=None, additional_headers=None):
    """
    Read fields from Dataverse table.
    
    Args:
        token: Authentication token
        dataverse_url: Base URL of the Dataverse environment
        table_name: Name of the table to read from
        fields: List of fields to retrieve
        whereClause: Optional OData filter condition
        logger: Optional logger instance
        expand: Optional expand parameter for related entities
        additional_headers: Optional dictionary of additional headers to include
        
    Returns:
        dict: JSON response with the data
    """
    # Base headers
    headers = {
        'Authorization': f'Bearer {token.get_token()}',
        'Accept': 'application/json',
        'OData-MaxVersion': '4.0',
        'OData-Version': '4.0'
    }
    
    # Merge additional headers if provided
    if additional_headers:
        headers.update(additional_headers)

    if logger == None:
        logger = getGlobalAppLogger()

    if table_name == "crimson_vacancy":
        tname = "crimson_vacancies"
    else:
        tname = f"{table_name}s"

    # Construct the API URL, specify the fields you want to retrieve
    fields_query = ",".join(fields)  # Join the field names with commas
    query_params = [f"$select={fields_query}"]
    if whereClause is not None:
        query_params.append(f"$filter={whereClause}")
    if expand is not None:
        query_params.append(f"$expand={expand}")
    query_string = "&".join(query_params)
    url = f"{dataverse_url}/api/data/v9.0/{tname}?{query_string}"

    #logger.info(url)
    response = requests.get(url, headers=headers)

    if response.status_code == 200:
        #logger.info("Rows successfully retrieved:")
        return response.json()  # Return the JSON response with the data
    else:
        logger.info(f"Failed to retrieve rows: {response.status_code}, {response.text}")
        return None
    
def find_row_in_dataverse(token, dataverse_url, table_name, search_field, search_value):
    headers = {
        'Authorization': f'Bearer {token.get_token()}',
        'Accept': 'application/json',
        'OData-MaxVersion': '4.0',
        'OData-Version': '4.0'
    }
    if table_name == "crimson_vacancy":
        tname = "crimson_vacancies"
    else:
        tname = f"{table_name}s"


    # Escape apostrophes by replacing them with two single quotes (SQL escaping)
    escaped_search_value = search_value.replace("'", "''")
    
    # URL encode the search value to handle special characters (like spaces and &)
    encoded_search_value = urllib.parse.quote(escaped_search_value)
    
    # Construct the URL for the Dataverse API query
    url = f"{dataverse_url}/api/data/v9.1/{tname}?$filter={search_field} eq '{encoded_search_value}' and statecode eq 0"

    
    response = requests.get(url, headers=headers)


    if response.status_code == 200:
        data = response.json()
        if len(data['value']) > 0:
            return data['value'][0]  # Return the first matching record
        else:
            return None  # No matching record found
    else:
        raise DataverseError(f"function - find_row_in_dataverse -  \n {search_field}, {search_value} not found. Status code: {response.status_code} - \nResponse:, {response.text}")

# Step 3: Update an existing row
def update_row_in_dataverse(token, dataverse_url, table_name, row_id, updated_data):
    """
    Update a row in any Dataverse table.
    
    Args:
        token: Authentication token
        dataverse_url: Base URL of the Dataverse environment
        table_name: Name of the table to update
        row_id: ID of the row to update
        updated_data: Dictionary containing the fields to update
        
    Returns:
        Response object from the update request
    """
    headers = {
        'Authorization': f'Bearer {token.get_token()}',
        'Content-Type': 'application/json',
        'OData-MaxVersion': '4.0',
        'OData-Version': '4.0'
    }

    # Handle special case for crimson_vacancy table
    if table_name == "crimson_vacancy":
        tname = "crimson_vacancies"
    else:
        tname = f"{table_name}s"

    # API endpoint for updating a row
    url = f"{dataverse_url}/api/data/v9.1/{tname}({row_id})"
    
    try:
        response = requests.patch(url, headers=headers, data=json.dumps(updated_data))
        if response.status_code not in [200, 204]:
            raise DataverseError(f"Failed to update row. Status code: {response.status_code}, Response: {response.text}")
        return response
    except Exception as e:
        raise DataverseError(f"Error updating row: {str(e)}")

# Step 4: Add a new row
def add_row_to_dataverse(token, dataverse_url, table_name, new_row_data, additional_headers=None):
    headers = {
        'Authorization': f'Bearer {token.get_token()}',
        'Content-Type': 'application/json',
        'OData-MaxVersion': '4.0',
        'OData-Version': '4.0',
        'Prefer': 'return=representation'
    }

    # Merge additional headers if provided
    if additional_headers:
        headers.update(additional_headers)

    url = f"{dataverse_url}/api/data/v9.1/{table_name}s"  # Plural name of the entity
    response = requests.post(url, headers=headers, data=json.dumps(new_row_data))
    return response

#Not used anywhere, could be a useful util function. was used in Kwal.
def search_dataverse_table(dataverse_url, table_name, search_conditions, token, additional_headers=None, count=False):

    headers = {
        'Authorization': f'Bearer {token.get_token()}',
        'Accept': 'application/json',
        'OData-MaxVersion': '4.0',
        'OData-Version': '4.0'
    }
    """
    Search a Dataverse table using multiple filter conditions.

    Parameters:
        dataverse_url (str): The base URL of the Dataverse environment.
        table_name (str): The name of the Dataverse table.
        search_conditions (list of tuple): A list of tuples where each tuple contains a field name and the search value.
        access_token (str): The OAuth2 token for authentication.
        additional_headers (dict): Additional headers to include in the request.
        count (bool): Whether to return the count of records as well.

    Returns:
        dict: The response containing the search results.
    """
    # Construct the $filter query
    #filter_query = " and ".join([f"{field} eq '{value}'" for field, value in search_conditions])

    # Construct the $filter query
    filter_query_parts = []
    for field, value in search_conditions:
        # Check if the value is a string and wrap it with quotes
        if isinstance(value, str):
            filter_query_parts.append(f"{field} eq '{value}'")
        else:
            # For non-string values, don't use quotes
            filter_query_parts.append(f"{field} eq {value}")
    
    filter_query = " and ".join(filter_query_parts)

    if count:
        filter_query += "&$count=true"

    # Construct the complete URL for the query
    url = f"{dataverse_url}/api/data/v9.1/{table_name}s?$filter={filter_query}"
    #getGlobalAppLogger().debug(url)

    # Make the GET request to the Dataverse Web API
    response = requests.get(url, headers=headers)
    return response

def update_contact_row_in_dataverse(token, dataverse_url, row_id, updated_data, logger=None):
    headers = {
        'Authorization': f'Bearer {token.get_token()}',
        'Content-Type': 'application/json',
        'OData-MaxVersion': '4.0',
        'OData-Version': '4.0'
    }

    # API endpoint for updating a row (singular name of the entity with the row's GUID)
    url = f"{dataverse_url}/api/data/v9.1/contacts({row_id})"
    if logger == None:
        logger = getGlobalAppLogger()
    logger.debug(url)
    
    result = make_http_request('PATCH', url, headers=headers, data=json.dumps(updated_data), logger=logger)

    if result["error"]:
        logger.error(f"Update Contact Row in Dataverse Error: {result['error']} - {row_id}")
        return None
    
    #print("PATCH request successful!")
    return result["response"]

def delete_row_from_dataverse(token, dataverse_url, table_name, row_id, additional_headers=None):
    """
    Delete a row from any Dataverse table.
    
    Args:
        token: Authentication token
        dataverse_url: Base URL of the Dataverse environment
        table_name: Name of the table to delete from
        row_id: ID of the row to delete
        additional_headers: Optional dictionary of additional headers to include
        
    Returns:
        Response object from the delete request
    """
    headers = {
        'Authorization': f'Bearer {token.get_token()}',
        'OData-MaxVersion': '4.0',
        'OData-Version': '4.0'
    }

    # Merge additional headers if provided
    if additional_headers:
        headers.update(additional_headers)

    # Handle special case for crimson_vacancy table
    if table_name == "crimson_vacancy":
        tname = "crimson_vacancies"
    else:
        tname = f"{table_name}s"

    # API endpoint for deleting a row
    url = f"{dataverse_url}/api/data/v9.1/{tname}({row_id})"
    
    try:
        response = requests.delete(url, headers=headers)
        if response.status_code not in [200, 204]:
            raise DataverseError(f"Failed to delete row. Status code: {response.status_code}, Response: {response.text}")
        return response
    except Exception as e:
        raise DataverseError(f"Error deleting row: {str(e)}")
