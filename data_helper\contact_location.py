from dataverse_helper.token_manager import get_token_for_env, get_dataverse_credentials_for_env, Environment
from AI_Agents.thank_avail_add_cand import get_candidate_info_sms
import json
from common.secrets_env import load_secrets_env_variables
from common.appLogger import <PERSON><PERSON><PERSON><PERSON><PERSON>, getGlobalAppLogger
from common.db.config_postgres import PostgresEnvironment
from common.db.global_dbconnector import GlobalDBConnector

def sync_candidate_location_with_dataverse(contactid, logger, connector):
    """
    Fetch candidate info from Dataverse,
    and update candidates.resume_data (state & city) in Postgres.
    Always returns a tuple (city, state) — or (None, None) if unavailable.
    """
    connector.connect()

    # Setup Dataverse credentials
    enum_env = Environment.PROD
    token = get_token_for_env(enum_env)
    credentials = get_dataverse_credentials_for_env(enum_env)
    dataverse_url = credentials["RESOURCE_URL"]

    # Fetch from Dataverse
    candi_list = get_candidate_info_sms(token, dataverse_url, contactid, logger)
    if not candi_list:
        logger.info(f"Could not get info for candidate {contactid}")
        return None, None

    #logger.debug(json.dumps(candi_list, indent=4))
    candi = candi_list[0]

    # Extract city and state safely
    city = candi.get("address2_city")

    recruit_address2_state = candi.get("recruit_address2_state")
    state = None
    if isinstance(recruit_address2_state, dict):
        state = recruit_address2_state.get("mercury_name")
    elif recruit_address2_state:
        logger.debug(f"recruit_address2_state is not a dict for {contactid}: {type(recruit_address2_state)} = {recruit_address2_state}")

    if not city and not state:
        logger.info(f"No city/state to update for {contactid}")
        return None, None

    if not city:
        logger.info(f"Updating the state but not the city for {contactid}")
    if not state:
        logger.info(f"Updating the city but not the state for {contactid}")

    # Update resume_data in DB
    with connector.connection.cursor() as cur:
        cur.execute(
            f"SELECT resume_data FROM {connector.schema}.candidates WHERE contact_id = %s",
            (contactid,)
        )
        row = cur.fetchone()
        if not row:
            logger.info(f"No candidate found in DB for {contactid}")
            return None, None

        resume_data = row[0] or {}
        if city:
            resume_data['city'] = city
        if state:
            resume_data['state'] = state

        cur.execute(
            f"""
            UPDATE {connector.schema}.candidates
            SET resume_data = %s, updated_at = CURRENT_TIMESTAMP
            WHERE contact_id = %s
            """,
            (json.dumps(resume_data), contactid)
        )
        connector.connection.commit()

    return city, state  # ✅ Consistent return

def sync_candidates_location_with_dataverse(candidate_ids, logger, connector):
    """
    Sync candidates location with Dataverse.
    """
    logger.info("Syncing candidates location with Dataverse")
    connector.connect()
    total_candidates = len(candidate_ids)

    for i, contact_id in enumerate(candidate_ids, 1):
        sync_candidate_location_with_dataverse(contact_id, logger, connector)
        
        # Log status every 1000 candidates
        if i % 1000 == 0:
            logger.info(f"Processed {i}/{total_candidates} candidates ({(i/total_candidates)*100:.1f}%)")

    logger.info("Finished syncing candidates missing city and state with Dataverse")

def sync_missing_city_state_with_dataverse(logger, connector):
    """
    Sync missing city and state with Dataverse.
    """
    logger.info("Syncing missing city and state with Dataverse")
    connector.connect()

    # Fetch all candidates with missing city/state
    with connector.connection.cursor() as cur:
        cur.execute(
            f"SELECT contact_id FROM {connector.schema}.candidates WHERE resume_data->>'city' IS NULL OR resume_data->>'state' IS NULL"
        )
        rows = cur.fetchall()
    
    total_candidates = len(rows)
    logger.info(f"Found {total_candidates} candidates with missing city/state")
    #import sys
    #sys.exit()
    for i, row in enumerate(rows, 1):
        contact_id = row[0]
        sync_candidate_location_with_dataverse(contact_id, logger, connector)
        
        # Log status every 1000 candidates
        if i % 1000 == 0:
            logger.info(f"Processed {i}/{total_candidates} candidates ({(i/total_candidates)*100:.1f}%)")

    logger.info("Finished syncing missing city and state with Dataverse")


def test1():
    db_connector = GlobalDBConnector.get_connector(PostgresEnvironment.PROD, getGlobalAppLogger())
    sync_candidate_location_with_dataverse("45cc37b0-90e7-ee11-85fb-6045bd7b41b0", logger=getGlobalAppLogger(), connector=db_connector)

def test2():
    db_connector = GlobalDBConnector.get_connector(PostgresEnvironment.PROD, getGlobalAppLogger())
    contact_ids = [
        'df716a47-2308-f011-bae3-7c1e52673a74',
        '55861fac-7c29-ef11-8601-6045bd7b41b0',
        'b4eef855-2805-f011-bae3-7c1e525ad549',
        '58891145-84d6-ef11-a730-000d3a1852ca',
        '1e9a7b2e-382c-f011-8c4d-0022481f5012',
        '76ef01c2-8e0a-f011-bae3-7c1e525ad549',
        'a4b9dcb2-da27-f011-8c4d-0022482db50b',
        'fd19dd93-9522-ef11-85ff-6045bd7b41b0',
        'fd2146ae-90e7-ee11-85fb-6045bd7b41b0',
        '9a04db77-a502-f011-bae3-7c1e52673a74',
        '58f225d9-b3ef-ef11-be20-7c1e525ad549',
        '6de1fec7-c809-f011-bae2-7c1e52671ca1',
        'fd2333ae-90e7-ee11-85fb-6045bd7b41b0',
        '8ca1050c-4e0b-f011-bae3-7c1e525ad549',
        '8ef11769-0388-ef11-ac21-6045bdd65e1f',
        '6844b6ce-fbe8-ef11-be1f-7c1e525ad549',
        '6fdff8fc-021c-f011-998a-7c1e52673a74',
        '52bfc5b0-90e7-ee11-85fb-6045bd7b41b0',
        'f7e471d7-e9f8-ef11-be20-6045bdd829f9',
        '4bc79fdb-201a-f011-998a-7c1e52673a74',
        '0f5bff06-f303-f011-bae3-7c1e525ad549',
        'fd2a33ae-90e7-ee11-85fb-6045bd7b41b0',
        '2b75baae-90e7-ee11-85fb-6045bd7b41b0'        
    ]
    sync_candidates_location_with_dataverse(contact_ids, logger=getGlobalAppLogger(), connector=db_connector)

def test_sync_missing_city_state_with_dataverse():
    db_connector = GlobalDBConnector.get_connector(PostgresEnvironment.PROD, getGlobalAppLogger())
    sync_missing_city_state_with_dataverse(logger=getGlobalAppLogger(), connector=db_connector)

if __name__ == "__main__":
    load_secrets_env_variables()
    test2()
    #test_sync_missing_city_state_with_dataverse()

