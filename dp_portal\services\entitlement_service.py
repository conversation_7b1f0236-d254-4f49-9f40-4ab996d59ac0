import json
import os
from dp_portal.db.postgres_connector import PostgresConnector
from dp_portal.appLogger import AppLogger
from dp_portal.services.historical_service import HistoricalService 

class EntitlementService:
    def __init__(self, logger: AppLogger, db_connector: PostgresConnector):
        self.logger = logger
        self.db = db_connector
        self.schema = self.db.schema

    def get_entitlement(self, email_id: str, portal_name: str):
        try:
            # Load entitlement config from environment
            app_config_raw = os.getenv("EntitlementsJson", "{}")
            app_config = json.loads(app_config_raw)

            # Map environment keys to entitlement features
            raw_entitlements = {
                "Work_force_Index": app_config.get("work_force_index"),
                "Sub_Catregory": app_config.get("sub_catregory"),
                "Vacancy": app_config.get("vacancy"),
                "Search_Match": app_config.get("search_match"),
                "Sc_Score_Config": app_config.get("sc_score_config"),
                "candidate_tunning_page": app_config.get("candidate_tunning_page"),
                "Shorting_Listing": app_config.get("shorting_listing")
            }

            self.logger.info(f"raw_entitlements: {raw_entitlements}")

            result_entitlements = {}

            # Get a live database connection
            conn = self.db.connection
            if conn is None or conn.closed != 0:
                raise Exception("Database connection is not available or already closed.")

            for key, value in raw_entitlements.items():
                key_lower = key.lower()
                value_lower = value.lower() if isinstance(value, str) else ""
                if value_lower == "entitled":
                    result_entitlements[key] = True

                elif value_lower == "list":
                    try:
                        with conn.cursor() as cur:
                            query = """
                                SELECT COUNT(*) 
                                FROM rp_feature_listmode_users rfl
                                JOIN rp_feature_entitlement rfe ON rfl.feature_id = rfe.feature_id
                                WHERE LOWER(rfl.email_id) = LOWER(%s)
                                AND LOWER(rfe.feature_name) = %s
                            """
                            cur.execute(query, (email_id, key_lower))
                            result = cur.fetchone()
                            result_entitlements[key] = result[0] > 0 if result else False
                    except Exception as query_err:
                        self.logger.error(f"DB Query Error for feature '{key}': {query_err}")
                        raise

                elif value_lower == "none":
                    result_entitlements[key] = False
                else:
                    result_entitlements[key] = False  # Unknown config value

            # Insert audit log (write operation, so rollback if error)
            try:
                with conn.cursor() as cur:
                    insert_query = """
                        INSERT INTO rp_historical_logs(emailid, portalname, eventtype)
                        VALUES (%s, %s, %s)
                    """
                    cur.execute(insert_query, (email_id, portal_name, "Entitlement"))
                    conn.commit()
            except Exception as insert_err:
                conn.rollback()
                self.logger.error(f"DB Insert Error: {insert_err}")
                raise

            return {
                "error": False,
                "code": "TR_01",
                "message": "Successful",
                "entitlement": result_entitlements
            }

        except Exception as e:
            try:
                # Only rollback if connection is active
                conn = self.db.connection
                if conn and conn.closed == 0:
                    conn.rollback()
            except Exception:
                pass  # Ignore rollback failure

            self.logger.error(f"Final Exception: {e}")
            return {
                "error": True,
                "code": "TR_ERR",
                "message": f"Error fetching Entitlement: {str(e)}",
                "entitlement": {}
            }
        
