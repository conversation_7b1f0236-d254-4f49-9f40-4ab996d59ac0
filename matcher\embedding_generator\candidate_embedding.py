import os
import json  # Ensure json is imported
import uuid  # Ensure uuid is imported
import argparse
import datetime  # Import datetime
from typing import List, Dict, Tuple, Set, Optional, Any, Literal
from openai import OpenAI
from common.appLogger import AppLogger
#from common.db.global_dbconnector import GlobalDBConnector, PostgresEnvironment
from common.db.postgres_connector import PostgresConnectorV2, PostgresEnvironment
from common.secrets_env import load_secrets_env_variables
import psycopg2
from psycopg2.extras import RealDictCursor, execute_batch
from collections import defaultdict  # Import defaultdict
import tiktoken # Added import

# --- Configuration ---
SOURCE_TABLE = "candidates"
TEMP_SOURCE_TABLE = "temp12"  # Added for temp mode
TARGET_TABLE_JOB_TITLE = "candidate_job_title_embedding"
TARGET_TABLE_SOFT_SKILL = "candidate_soft_skill_embedding"  # New target table
TARGET_TABLE_TECHNICAL_SKILL = "candidate_tech_skill_embedding"  # New target table for technical skills
TARGET_TABLE_TOOL_PLATFORM = "candidate_tool_and_platform_embedding"  # New target table for tools and platforms
TARGET_TABLE_DEGREE_CERTIFICATION = "candidate_degree_certification_embedding"  # New target table for degrees/certs
TARGET_TABLE_INDUSTRY = "candidate_industry_embedding"  # New target table for industry
TARGET_TABLE_LATEST_WORK_EXPERIENCE = "candidate_latest_work_experience_embedding"  # New target table for industry
TARGET_TABLE_DEGREE_CERTIFICATION_OBJECT = "candidate_degree_certification_complete_object_embedding" # New target table for degree/cert JSON object
EMBEDDING_MODEL = "text-embedding-3-large"  # Updated model
FETCH_BATCH_SIZE = 1000  # Fetch more candidates per DB query
EMBEDDING_API_BATCH_SIZE = 2048  # Max input items for text-embedding-3-large is 2048
INSERT_BATCH_SIZE = 1000  # How many records to insert in a single batch transaction
UNPARSABLE_DEGREES_CERTS_LOG = "unparsable_degrees_certs_ids.txt"  # File to log IDs with unparsable degrees/certs data

# Define a safe limit, slightly less than the OpenAI API's absolute max (e.g., 300,000 for text-embedding-3-large)
# Using 290,000 as a buffer.
SAFE_MAX_TOKENS_PER_API_CALL = 180000

ALL_PROCESSING_MODES: List[Literal['job_title', 'soft_skill', 'technical_skill', 'tool_platform', 'degree_certification', 'industry', 'degree_certification_object', 'latest_work_experience']] = [
    'job_title', 
    'soft_skill', 
    'technical_skill', 
    'tool_platform', 
    'degree_certification', 
    'industry', 
    'degree_certification_object',
    'latest_work_experience'
]

# --- OpenAI Client ---
client = OpenAI(api_key=os.environ.get('OPENAI_API_KEY')) # Ensure this is initialized

# --- Helper Function to Generate Embeddings ---
def get_embeddings(texts: List[str], logger: AppLogger) -> List[List[float]]:
    """
    Generate embeddings for a list of texts using OpenAI.
    Returns a list of embedding vectors (each a list of floats).
    Handles potential API errors.
    """
    if not texts:
        return []
    embeddings: List[List[float]] = []
    try:
        # Filter out None or empty strings *before* sending to API
        valid_texts = [text.replace("\n", " ").strip() for text in texts if text and text.strip()]
        if not valid_texts:
            logger.warning("All texts in the batch were empty or None after cleaning.")
            # Return empty lists matching the original input length
            return [[] for _ in texts]

        logger.info(f"Requesting embeddings for {len(valid_texts)} non-empty texts from OpenAI model {EMBEDDING_MODEL}...")
        response = client.embeddings.create(input=valid_texts, model=EMBEDDING_MODEL, dimensions=1536)
        # Create a map from the *cleaned* text to its embedding
        embeddings_map = {text: item.embedding for text, item in zip(valid_texts, response.data)}

        # Reconstruct the result list in the original order, handling skipped texts
        result_embeddings: List[List[float]] = []
        for original_text in texts:
            if original_text and original_text.strip():
                cleaned_text = original_text.replace("\n", " ").strip()
                # Use get() to handle cases where API might not return embedding for a valid text
                embedding = embeddings_map.get(cleaned_text)
                if embedding:
                    result_embeddings.append(embedding)
                else:
                    logger.warning(f"Could not find embedding for '{cleaned_text}' in API response, appending empty list.")
                    result_embeddings.append([])
            else:
                # Append empty list for original None or empty strings
                result_embeddings.append([])

        logger.info(f"Successfully received {len(embeddings_map)} embeddings from API.")
        return result_embeddings
    except Exception as e:
        logger.error(f"Error fetching embeddings: {e}", exc_info=True)
        # Return empty lists matching the original input length on error
        return [[] for _ in texts]

# --- Helper Function to Parse Work Experience ---
def extract_job_titles_from_work_experience(work_experience_json: Optional[Any], candidate_id: uuid.UUID, logger: AppLogger) -> List[Tuple[str, int]]:
    """
    Parses the work_experience JSON string or list and extracts job titles with their recency index.
    The index 0 represents the most recent job title.
    Returns a list of (job_title, index) tuples.
    """
    titles_with_indices: List[Tuple[str, int]] = []
    if not work_experience_json:
        return titles_with_indices

    try:
        work_experiences: List[Any]
        if isinstance(work_experience_json, list):
            work_experiences = work_experience_json
        elif isinstance(work_experience_json, str):
            try:
                work_experiences = json.loads(work_experience_json)
            except json.JSONDecodeError:
                logger.warning(f"Could not parse work_experience JSON string for candidate {candidate_id}: {work_experience_json[:100]}...")
                return titles_with_indices
        else:
            # Handle cases where it might already be a dict (from RealDictCursor)
            if isinstance(work_experience_json, dict):
                title = work_experience_json.get('job_title') or work_experience_json.get('Title') or work_experience_json.get('title')
                if isinstance(title, str) and title.strip():
                    titles_with_indices.append((title.strip(), 0)) # Assign index 0 for single dict
                    return titles_with_indices
                else:
                    logger.warning(f"work_experience for candidate {candidate_id} is a dict but has no title: {work_experience_json}")
                    return titles_with_indices
            else:
                logger.warning(f"work_experience for candidate {candidate_id} is unexpected type: {type(work_experience_json)}")
                return titles_with_indices

        if not isinstance(work_experiences, list):
            logger.warning(f"Parsed work_experience for candidate {candidate_id} is not a list: {work_experiences}")
            return titles_with_indices

        for index, entry in enumerate(work_experiences):
            if isinstance(entry, dict):
                title = entry.get('job_title') or entry.get('Title') or entry.get('title')
                if isinstance(title, str) and title.strip():
                    titles_with_indices.append((title.strip(), index))
                # else: # Reduce noise: Don't log every missing title
                #     logger.debug(f"No valid job title found in work experience entry for candidate {candidate_id}: {entry}")
            # else: # Reduce noise: Don't log every non-dict entry
            #     logger.debug(f"Work experience entry is not a dictionary for candidate {candidate_id}: {entry}")

    except Exception as e:
        logger.error(f"Unexpected error processing work experience for candidate {candidate_id}: {e}", exc_info=True)

    # Primary key (contact_id, job_title) will handle uniqueness for storage.
    # ON CONFLICT clause will use LEAST() for experience_index.
    return titles_with_indices

# --- Helper Function to Parse Industries from Work Experience ---
def extract_industries_from_work_experience(work_experience_json: Optional[Any], candidate_id: uuid.UUID, logger: AppLogger) -> List[Tuple[str, int]]:
    """
    Parses the work_experience JSON and extracts industry names with their recency index.
    The index 0 represents the most recent industry.
    Returns a list of (industry_name, index) tuples.
    """
    industries_with_indices: List[Tuple[str, int]] = []
    if not work_experience_json:
        return industries_with_indices

    try:
        work_experiences: List[Any]
        if isinstance(work_experience_json, list):
            work_experiences = work_experience_json
        elif isinstance(work_experience_json, str):
            try:
                work_experiences = json.loads(work_experience_json)
            except json.JSONDecodeError:
                logger.warning(f"Could not parse work_experience JSON string for industry for candidate {candidate_id}: {work_experience_json[:100]}...")
                return industries_with_indices
        else:
            logger.warning(f"work_experience for candidate {candidate_id} (industry) is unexpected type: {type(work_experience_json)}")
            return industries_with_indices

        if not isinstance(work_experiences, list):
            logger.warning(f"Parsed work_experience for candidate {candidate_id} (industry) is not a list: {work_experiences}")
            return industries_with_indices

        for index, entry in enumerate(work_experiences):
            if isinstance(entry, dict):
                industry_obj = entry.get('industry')
                if isinstance(industry_obj, dict):
                    industry_name = industry_obj.get('name')
                    if isinstance(industry_name, str) and industry_name.strip():
                        industries_with_indices.append((industry_name.strip(), index))
                    else: # Reduce noise
                        logger.debug(f"No valid industry name found in industry object for candidate {candidate_id}: {industry_obj}")
                else: # Reduce noise
                    logger.debug(f"Industry field is not a dictionary or missing in work experience entry for candidate {candidate_id}: {entry}")
            else: # Reduce noise
                logger.debug(f"Work experience entry is not a dictionary for candidate {candidate_id} (industry): {entry}")

    except Exception as e:
        logger.error(f"Unexpected error processing work experience for industry for candidate {candidate_id}: {e}", exc_info=True)

    # Unlike job titles, we don't do list(set()) here as we need to preserve all occurrences
    # with their original indices for the ON CONFLICT clause to work correctly with LEAST().
    # The primary key (contact_id, industry) will handle uniqueness for storage.
    return industries_with_indices

# --- Helper Function to Parse Soft Skills from resume_data ---
def extract_soft_skills_from_resume_data(resume_data_json: Optional[Any], candidate_id: uuid.UUID, logger: AppLogger) -> List[str]:
    """
    Parses the resume_data JSON (string or dict) and extracts soft skills.
    Handles JSON errors and missing data.
    """
    skills: List[str] = []
    if not resume_data_json:
        return skills

    try:
        resume_data: Dict[str, Any]
        if isinstance(resume_data_json, dict):
            resume_data = resume_data_json
        elif isinstance(resume_data_json, str):
            try:
                resume_data = json.loads(resume_data_json)
            except json.JSONDecodeError:
                logger.warning(f"Could not parse resume_data JSON string for candidate {candidate_id}: {resume_data_json[:100]}...")
                return skills
        else:
            logger.warning(f"resume_data for candidate {candidate_id} is unexpected type: {type(resume_data_json)}")
            return skills

        if not isinstance(resume_data, dict):
            logger.warning(f"Parsed resume_data for candidate {candidate_id} is not a dictionary: {resume_data}")
            return skills

        soft_skills_data = resume_data.get('soft skills') or resume_data.get('soft_skills')  # Check both keys

        if isinstance(soft_skills_data, list):
            for skill in soft_skills_data:
                if isinstance(skill, str) and skill.strip():
                    skills.append(skill.strip())
        # else: # Reduce noise
        # if soft_skills_data is not None:
        #     logger.debug(f"'soft skills' field is not a list for candidate {candidate_id}: {soft_skills_data}")

    except Exception as e:
        logger.error(f"Unexpected error processing resume_data for soft skills for candidate {candidate_id}: {e}", exc_info=True)

    return list(set(skills))  # Return unique skills

# --- Helper Function to Parse Technical Skills from resume_data ---
def extract_technical_skills_from_resume_data(resume_data_json: Optional[Any], candidate_id: uuid.UUID, logger: AppLogger) -> List[str]:
    """
    Parses the resume_data JSON (string or dict) and extracts technical skills.
    Handles JSON errors and missing data.
    """
    skills: List[str] = []
    if not resume_data_json:
        return skills

    try:
        resume_data: Dict[str, Any]
        if isinstance(resume_data_json, dict):
            resume_data = resume_data_json
        elif isinstance(resume_data_json, str):
            try:
                resume_data = json.loads(resume_data_json)
            except json.JSONDecodeError:
                logger.warning(f"Could not parse resume_data JSON string for candidate {candidate_id}: {resume_data_json[:100]}...")
                return skills
        else:
            logger.warning(f"resume_data for candidate {candidate_id} is unexpected type: {type(resume_data_json)}")
            return skills

        if not isinstance(resume_data, dict):
            logger.warning(f"Parsed resume_data for candidate {candidate_id} is not a dictionary: {resume_data}")
            return skills

        # Look for 'technical skills' or 'technical_skills' keys
        technical_skills_data = resume_data.get('technical skills') or resume_data.get('technical_skills')

        if isinstance(technical_skills_data, list):
            for skill in technical_skills_data:
                if isinstance(skill, str) and skill.strip():
                    skills.append(skill.strip())
        else: # Reduce noise
            if technical_skills_data is not None:
                logger.debug(f"'technical skills' field is not a list for candidate {candidate_id}: {technical_skills_data}")

    except Exception as e:
        logger.error(f"Unexpected error processing resume_data for technical skills for candidate {candidate_id}: {e}", exc_info=True)

    return list(set(skills))  # Return unique skills

# --- Helper Function to Parse Tools & Platforms from resume_data ---
def extract_tools_platforms_from_resume_data(resume_data_json: Optional[Any], candidate_id: uuid.UUID, logger: AppLogger) -> List[str]:
    """
    Parses the resume_data JSON (string or dict) and extracts tools and platforms.
    Handles JSON errors and missing data.
    """
    items: List[str] = []
    if not resume_data_json:
        return items

    try:
        resume_data: Dict[str, Any]
        if isinstance(resume_data_json, dict):
            resume_data = resume_data_json
        elif isinstance(resume_data_json, str):
            try:
                resume_data = json.loads(resume_data_json)
            except json.JSONDecodeError:
                logger.warning(f"Could not parse resume_data JSON string for candidate {candidate_id}: {resume_data_json[:100]}...")
                return items
        else:
            logger.warning(f"resume_data for candidate {candidate_id} is unexpected type: {type(resume_data_json)}")
            return items

        if not isinstance(resume_data, dict):
            logger.warning(f"Parsed resume_data for candidate {candidate_id} is not a dictionary: {resume_data}")
            return items

        # Look for 'tools and platforms' key
        tools_platforms_data = resume_data.get('tools and platforms')

        if isinstance(tools_platforms_data, list):
            for item in tools_platforms_data:
                if isinstance(item, str) and item.strip():
                    items.append(item.strip())
        else: # Reduce noise
            if tools_platforms_data is not None:
                logger.debug(f"'tools and platforms' field is not a list for candidate {candidate_id}: {tools_platforms_data}")

    except Exception as e:
        logger.error(f"Unexpected error processing resume_data for tools and platforms for candidate {candidate_id}: {e}", exc_info=True)

    return list(set(items))  # Return unique items

# --- Helper function to log unparsable IDs ---
def log_unparsable_id(candidate_id: uuid.UUID, filename: str, logger: AppLogger):
    """Appends a candidate ID to a specified log file."""
    try:
        with open(filename, 'a') as f:
            f.write(f"{candidate_id}\n")
    except IOError as e:
        logger.error(f"Failed to write unparsable ID {candidate_id} to log file '{filename}': {e}")

# --- Helper Function to Parse Degrees & Certifications from resume_data ---
def extract_degrees_certifications_from_resume_data(resume_data_json: Optional[Any], candidate_id: uuid.UUID, logger: AppLogger) -> List[str]:
    """
    Parses the resume_data JSON (string or dict) and extracts degrees and certifications.
    Handles JSON errors, missing data, and various formats. Logs IDs with unparsable structures.
    """
    items: List[str] = []
    if not resume_data_json:
        return items

    try:
        resume_data: Dict[str, Any]
        if isinstance(resume_data_json, dict):
            resume_data = resume_data_json
        elif isinstance(resume_data_json, str):
            try:
                resume_data = json.loads(resume_data_json)
            except json.JSONDecodeError:
                logger.warning(f"Could not parse resume_data JSON string for candidate {candidate_id} (degrees/certs): {resume_data_json[:100]}...")
                log_unparsable_id(candidate_id, UNPARSABLE_DEGREES_CERTS_LOG, logger)
                return items
        else:
            logger.warning(f"resume_data for candidate {candidate_id} (degrees/certs) is unexpected type: {type(resume_data_json)}")
            log_unparsable_id(candidate_id, UNPARSABLE_DEGREES_CERTS_LOG, logger)
            return items

        if not isinstance(resume_data, dict):
            logger.warning(f"Parsed resume_data for candidate {candidate_id} (degrees/certs) is not a dictionary: {resume_data}")
            log_unparsable_id(candidate_id, UNPARSABLE_DEGREES_CERTS_LOG, logger)
            return items

        degrees_certs_data = resume_data.get('degrees and certifications')

        if degrees_certs_data is None:
            # Check for top-level 'degrees' or 'certifications' if 'degrees and certifications' is missing
            # This handles cases where the entire resume_data might be one of the new structures.
            if isinstance(resume_data.get('degrees'), list) or isinstance(resume_data.get('certifications'), list):
                degrees_certs_data = resume_data # Process the whole resume_data dict
            elif isinstance(resume_data.get('credential'), str): # handles [{"credential": "RN License"}] at top level
                degrees_certs_data = [resume_data] # wrap it in a list to be processed by the loop
            else:
                # Check if resume_data itself is a list of degrees/certs (e.g. new formats at top level)
                is_list_of_potential_items = False
                if isinstance(resume_data_json, list): # Check original input if it was a list
                    is_list_of_potential_items = True
                    degrees_certs_data = resume_data_json
                elif isinstance(resume_data, list): # Check parsed resume_data if it became a list
                    is_list_of_potential_items = True
                    degrees_certs_data = resume_data

                if not is_list_of_potential_items:
                    return items


        _processed_texts_in_current_candidate: Set[str] = set()
        def _add_item(text_val: Optional[str]):
            if isinstance(text_val, str) and text_val.strip():
                cleaned_text = text_val.strip()
                if cleaned_text not in _processed_texts_in_current_candidate:
                    items.append(cleaned_text)
                    _processed_texts_in_current_candidate.add(cleaned_text)

        def _process_potential_item(item_data: Any):
            if isinstance(item_data, str):
                _add_item(item_data)
                return

            if not isinstance(item_data, dict):
                return

            # Combination 1: type + field (or field_of_study)
            item_type_val = item_data.get('type')
            item_field_val = item_data.get('field') or item_data.get('field_of_study')

            if isinstance(item_type_val, str) and item_type_val.strip() and \
               isinstance(item_field_val, str) and item_field_val.strip():
                _add_item(f"{item_type_val.strip()} {item_field_val.strip()}")
                # Continue to check other keys as 'type' or 'name' might be more specific

            # Combination 2: qualification + course_of_study
            item_qual_val = item_data.get('qualification')
            item_study_val = item_data.get('course_of_study')
            if isinstance(item_qual_val, str) and item_qual_val.strip() and \
               isinstance(item_study_val, str) and item_study_val.strip():
                _add_item(f"{item_qual_val.strip()} {item_study_val.strip()}")
                # Continue to check other keys

            # Check for single, direct keys.
            preferred_single_keys = [
                'name', 'degree', 'Degree', 'certification', 'Certification', # Original
                'degree_name',                                                # New
                'certificate',                                                # New
                'title',                                                      # New (also for certs)
                'credential',                                                 # New
                'type_of_training',                                           # New (certs)
                'degree_type',                                                # New (degrees)
                'field_of_study',                                             # Added: To pick up field of study directly
                'certification_name',                                         # Added: For keys like "certification_name"
                # 'type' and 'qualification' will be handled below to ensure they are added
                # even if not part of a combination, or if they are the primary identifier.
            ]
            for key in preferred_single_keys:
                _add_item(item_data.get(key))
            
            # Add 'type' if it exists (it might be the primary identifier or part of a combo)
            # and hasn't been captured by a combination or preferred_single_keys if 'type' itself is the value.
            if isinstance(item_type_val, str) and item_type_val.strip():
                _add_item(item_type_val.strip())

            # Add 'qualification' if it exists and hasn't been captured.
            if isinstance(item_qual_val, str) and item_qual_val.strip():
                _add_item(item_qual_val.strip())
            
            # Add 'field' or 'field_of_study' (item_field_val) if it exists and hasn't been captured,
            # especially if it wasn't part of a combination.
            if isinstance(item_field_val, str) and item_field_val.strip():
                _add_item(item_field_val.strip())


        if isinstance(degrees_certs_data, list):
            for entry in degrees_certs_data:
                if isinstance(entry, str):
                    _add_item(entry)
                elif isinstance(entry, dict):
                    has_nested_degrees = False
                    degrees_list_from_entry = entry.get('degrees')
                    if isinstance(degrees_list_from_entry, list):
                        has_nested_degrees = True
                        for deg_item in degrees_list_from_entry:
                            _process_potential_item(deg_item)

                    has_nested_certifications = False
                    certifications_list_from_entry = entry.get('certifications')
                    if isinstance(certifications_list_from_entry, list):
                        has_nested_certifications = True
                        for cert_item in certifications_list_from_entry:
                            _process_potential_item(cert_item)
                    
                    # If the entry itself is a degree/cert item and not just a container
                    if not has_nested_degrees and not has_nested_certifications:
                        _process_potential_item(entry)

        elif isinstance(degrees_certs_data, dict): # e.g. resume_data = {"degrees": [...], "certifications": [...]}
            degrees_list = degrees_certs_data.get('degrees') or degrees_certs_data.get('Degrees')
            if isinstance(degrees_list, list):
                for degree_item in degrees_list:
                    _process_potential_item(degree_item)

            certifications_list = degrees_certs_data.get('certifications') or degrees_certs_data.get('Certification')
            if isinstance(certifications_list, list):
                for cert_item in certifications_list:
                    _process_potential_item(cert_item)
            
            # If the dict itself might be a single degree/cert item (less common for top-level 'degrees and certifications')
            # This case is more for when _process_potential_item is called on a dict that is an item.
            # The initial check for degrees_certs_data being None and then potentially being resume_data handles some top-level dicts.
            # If degrees_certs_data was a dict but not with 'degrees'/'certifications' keys, process it directly.
            if not isinstance(degrees_list, list) and not isinstance(certifications_list, list):
                _process_potential_item(degrees_certs_data)


        else:
            logger.warning(f"'degrees and certifications' field (or fallback data) for candidate {candidate_id} is not a list or dict: {type(degrees_certs_data)}. Logging ID.")
            log_unparsable_id(candidate_id, UNPARSABLE_DEGREES_CERTS_LOG, logger)

    except Exception as e:
        logger.error(f"Unexpected error processing resume_data for degrees/certifications for candidate {candidate_id}: {e}", exc_info=True)
        log_unparsable_id(candidate_id, UNPARSABLE_DEGREES_CERTS_LOG, logger)

    return list(set(items))  # Return unique items

# --- Helper Function to Parse Degrees & Certifications JSON object as string from resume_data ---
def extract_degrees_certifications_object_as_string(resume_data_json: Optional[Any], candidate_id: uuid.UUID, logger: AppLogger) -> List[str]:
    """
    Parses the resume_data JSON and extracts the 'degrees and certifications' field as a JSON string
    if it's a non-empty JSON object, array, or a string representing valid non-empty JSON.
    """
    if not resume_data_json:
        return []

    try:
        resume_data: Dict[str, Any]
        if isinstance(resume_data_json, dict):
            resume_data = resume_data_json
        elif isinstance(resume_data_json, str):
            try:
                resume_data = json.loads(resume_data_json)
            except json.JSONDecodeError:
                logger.warning(f"Could not parse resume_data JSON string for candidate {candidate_id} (degrees/certs object): {resume_data_json[:100]}...")
                return []
        else:
            logger.warning(f"resume_data for candidate {candidate_id} (degrees/certs object) is unexpected type: {type(resume_data_json)}")
            return []

        if not isinstance(resume_data, dict):
            logger.warning(f"Parsed resume_data for candidate {candidate_id} (degrees/certs object) is not a dictionary: {resume_data}")
            return []

        degrees_certs_data = resume_data.get('degrees and certifications')

        if degrees_certs_data is None:
            return []

        if isinstance(degrees_certs_data, str):
            stripped_data = degrees_certs_data.strip()
            if not stripped_data: # Empty string
                return []
            try:
                parsed_json = json.loads(stripped_data)
                if isinstance(parsed_json, (list, dict)) and not parsed_json: # Represents empty JSON array/object
                    return []
                # If it's a valid non-empty JSON string, return it as is in a list
                return [stripped_data]
            except json.JSONDecodeError:
                logger.debug(f"'degrees and certifications' for candidate {candidate_id} is a string but not valid JSON: {stripped_data[:100]}...")
                return [] # Not a valid JSON object string
        elif isinstance(degrees_certs_data, (list, dict)):
            if not degrees_certs_data: # Empty list or dict
                return []
            try:
                # Convert the list/dict to its JSON string representation
                return [json.dumps(degrees_certs_data)]
            except TypeError as te:
                logger.error(f"Could not serialize 'degrees and certifications' data to JSON for candidate {candidate_id}: {te}", exc_info=True)
                return []
        else:
            logger.debug(f"'degrees and certifications' for candidate {candidate_id} is not a string, list, or dict: {type(degrees_certs_data)}")
            return []

    except Exception as e:
        logger.error(f"Unexpected error processing resume_data for degrees/certifications object for candidate {candidate_id}: {e}", exc_info=True)
        return []

def extract_latest_work_experience(work_experience_json: Optional[Any], candidate_id: uuid.UUID, logger: AppLogger) -> List[Tuple[Dict[str, Any], int]]:
    """
    Parses the work_experience JSON and extracts up to two most recent entries.
    Handles cases where 'description' may be a list. Logs candidate_id if so.
    Returns a list of (work_experience_dict, index) tuples, where index=0 is most recent.
    """
    filtered_experiences: List[Tuple[Dict[str, Any], int]] = []

    if not work_experience_json:
        logger.error(f"work_experience_json is None for candidate {candidate_id}")
        return filtered_experiences

    try:
        if isinstance(work_experience_json, str):
            try:
                work_experiences = json.loads(work_experience_json)
            except json.JSONDecodeError:
                logger.warning(f"Could not parse work_experience JSON string for candidate {candidate_id}: {work_experience_json[:100]}...")
                return []
        elif isinstance(work_experience_json, list):
            work_experiences = work_experience_json
        else:
            logger.warning(f"work_experience for candidate {candidate_id} is unexpected type: {type(work_experience_json)}")
            return []

        if not isinstance(work_experiences, list):
            logger.warning(f"Parsed work_experience for candidate {candidate_id} is not a list.")
            return []

        recency_index = 0
        for entry in work_experiences:
            if not isinstance(entry, dict):
                logger.debug(f"Invalid entry type in work_experience for candidate {candidate_id}: {entry}")
                continue

            if entry.get('description'):
                filtered_experiences.append((entry['description'], recency_index))
                recency_index += 1

            if recency_index == 2:
                break

    except Exception as e:
        logger.error(f"Unexpected error processing work experience for candidate {candidate_id}: {e}", exc_info=True)

    return filtered_experiences

# --- MODIFICATION: New Helper Function for Mode Configurations ---
def _get_mode_configurations(
    mode: Literal['job_title', 'soft_skill', 'technical_skill', 'tool_platform', 'degree_certification', 'industry', 'degree_certification_object', 'latest_work_experience']
) -> Dict[str, Any]:
    """Returns a dictionary of configurations based on the processing mode."""
    if mode == 'job_title':
        return {
            "data_type": "Job Title",
            "source_column_name": "work_experience",
            "extraction_func": extract_job_titles_from_work_experience,
            "target_table_name": TARGET_TABLE_JOB_TITLE,
            "text_col": "job_title",
            "embedding_col": "job_title_embedding",
            "exp_idx_col": "experience_index"
        }
    elif mode == 'soft_skill':
        return {
            "data_type": "Soft Skill",
            "source_column_name": "resume_data",
            "extraction_func": extract_soft_skills_from_resume_data,
            "target_table_name": TARGET_TABLE_SOFT_SKILL,
            "text_col": "soft_skill",
            "embedding_col": "soft_skill_embedding",
            "exp_idx_col": None
        }
    elif mode == 'technical_skill':
        return {
            "data_type": "Technical Skill",
            "source_column_name": "resume_data",
            "extraction_func": extract_technical_skills_from_resume_data,
            "target_table_name": TARGET_TABLE_TECHNICAL_SKILL,
            "text_col": "tech_skill",
            "embedding_col": "tech_skill_embedding",
            "exp_idx_col": None
        }
    elif mode == 'tool_platform':
        return {
            "data_type": "Tool/Platform",
            "source_column_name": "resume_data",
            "extraction_func": extract_tools_platforms_from_resume_data,
            "target_table_name": TARGET_TABLE_TOOL_PLATFORM,
            "text_col": "tool_and_platform",
            "embedding_col": "tool_and_platform_embedding",
            "exp_idx_col": None
        }
    elif mode == 'degree_certification':
        return {
            "data_type": "Degree/Certification",
            "source_column_name": "resume_data",
            "extraction_func": extract_degrees_certifications_from_resume_data,
            "target_table_name": TARGET_TABLE_DEGREE_CERTIFICATION,
            "text_col": "degree_certification",
            "embedding_col": "degree_certification_embedding",
            "exp_idx_col": None
        }
    elif mode == 'industry':
        return {
            "data_type": "Industry",
            "source_column_name": "work_experience",
            "extraction_func": extract_industries_from_work_experience,
            "target_table_name": TARGET_TABLE_INDUSTRY,
            "text_col": "industry",
            "embedding_col": "industry_embedding",
            "exp_idx_col": "experience_index"
        }
    elif mode == 'degree_certification_object':
        return {
            "data_type": "Degree/Certification Object",
            "source_column_name": "resume_data",
            "extraction_func": extract_degrees_certifications_object_as_string,
            "target_table_name": TARGET_TABLE_DEGREE_CERTIFICATION_OBJECT,
            "text_col": "degree_certification", # For the JSON string
            "embedding_col": "degree_certification_embedding",
            "exp_idx_col": None
        }
    elif mode == 'latest_work_experience':
        return {
            "data_type": "Work Experience",
            "source_column_name": "work_experience",
            "extraction_func": extract_latest_work_experience,
            "target_table_name": TARGET_TABLE_LATEST_WORK_EXPERIENCE,
            "text_col": "work_experience", # For the JSON string
            "embedding_col": "work_experience_embedding",
            "exp_idx_col": "experience_index"
        }
    else:
        # Should not happen if Literal type hint is respected, but good for safety
        return {}
# --- END MODIFICATION ---

# --- New Function for Single Candidate Embedding Generation ---
def generate_embeddings_for_single_candidate(
    logger: AppLogger,
    mode: Literal['job_title', 'soft_skill', 'technical_skill', 'tool_platform', 'degree_certification', 'industry', 'degree_certification_object', 'latest_work_experience'],
    contact_id: uuid.UUID,
    pg_env: PostgresEnvironment,
    dry_run: bool = False,
    skip_regenerate_embedding: bool = False  # New parameter
) -> Tuple[bool, Optional[str]]:
    """
    Generates embeddings for a single candidate and a specific mode.
    If skip_regenerate_embedding is True and embeddings already exist, skips regeneration.
    Returns a tuple: (success_status: bool, error_message: Optional[str]).
    Error_message is None if successful, or a string describing the error if failed.
    """
    logger.info(f"Generating {mode} embeddings for candidate {contact_id}. Dry run: {dry_run}, Skip existing: {skip_regenerate_embedding}")

    # --- MODIFICATION: Use helper function for mode configurations ---
    configs = _get_mode_configurations(mode)
    if not configs:
        err_msg = f"Invalid mode '{mode}' for single candidate embedding."
        logger.error(err_msg)
        return False, err_msg

    data_type = configs["data_type"]
    source_column_name = configs["source_column_name"]
    extraction_func = configs["extraction_func"]
    target_table_name = configs["target_table_name"]
    text_col = configs["text_col"]
    embedding_col = configs["embedding_col"]
    exp_idx_col = configs.get("exp_idx_col")
    # --- END MODIFICATION ---

    source_conn = None
    target_conn = None
    success = False
    error_reason = None

    try:
        # --- MODIFICATION: Add skip_regenerate_embedding logic ---
        if skip_regenerate_embedding:
            # Check if embeddings already exist for this candidate and mode
            checker_conn = None
            try:
                checker_connector = PostgresConnectorV2(env=pg_env, logger=logger)
                checker_conn = checker_connector.connect()
                checker_schema = checker_connector.schema
                with checker_conn.cursor() as cursor_check:
                    # This query checks if *any* record for the contact_id exists in the target table for this mode.
                    query_exists = f"SELECT EXISTS (SELECT 1 FROM {checker_schema}.{target_table_name} WHERE contact_id = %s);"
                    cursor_check.execute(query_exists, (contact_id,))
                    exists = cursor_check.fetchone()[0]
                    if exists:
                        logger.info(f"Embeddings for candidate {contact_id} in mode '{mode}' (table: {target_table_name}) already exist. Skipping regeneration as skip_regenerate_embedding is True.")
                        return True, None
            except psycopg2.Error as db_err_check:
                logger.warning(f"Database error during pre-check for existing embeddings for candidate {candidate_id}, mode {mode}: {db_err_check}. Proceeding with generation.")
            except Exception as e_check:
                logger.warning(f"Unexpected error during pre-check for existing embeddings for candidate {candidate_id}, mode {mode}: {e_check}. Proceeding with generation.")
            finally:
                if checker_conn:
                    checker_conn.close()
        # --- END MODIFICATION ---

        # Ensure OpenAI client is available
        if not client:
            error_reason = "OpenAI client is not initialized."
            logger.error(error_reason)
            return False, error_reason

        source_connector = PostgresConnectorV2(env=pg_env, logger=logger)
        source_conn = source_connector.connect()
        source_schema = source_connector.schema

        candidate_data_val = None
        with source_conn.cursor(cursor_factory=RealDictCursor) as cursor:
            # Ensure contact_id is cast to UUID in the query for type safety
            query = f"SELECT {source_column_name} FROM {source_schema}.{SOURCE_TABLE} WHERE contact_id = %s::uuid;"
            cursor.execute(query, (contact_id,))
            row = cursor.fetchone()
            if row:
                candidate_data_val = row[source_column_name]
            else:
                error_reason = f"Candidate {contact_id} not found in {SOURCE_TABLE}."
                logger.warning(error_reason)
                return False, error_reason # Candidate not found

        if candidate_data_val is None:
            logger.info(f"No source data ('{source_column_name}') found for candidate {contact_id} for mode {mode}.")
            return True, None # No data to process is not an error

        # Extract items
        extracted_items_data = extraction_func(candidate_data_val, contact_id, logger)
        
        items_to_embed_texts = []
        candidate_item_details_for_insert = [] # (text, exp_idx if applicable) or (text)

        if exp_idx_col: # For modes like job_title, industry
            for item_text, exp_idx_val in extracted_items_data: # Ensure exp_idx_val is used for clarity
                items_to_embed_texts.append(item_text)
                candidate_item_details_for_insert.append({'text': item_text, 'exp_idx': exp_idx_val})
        else: # For skill, tool, degree/cert strings
            for item_text in extracted_items_data:
                items_to_embed_texts.append(item_text)
                candidate_item_details_for_insert.append({'text': item_text})

        if not items_to_embed_texts:
            logger.info(f"No {data_type}s extracted for candidate {contact_id} for mode {mode}.")
            return True, None # Successfully processed, nothing to embed

        # Get embeddings
        # Use a temporary cache for this single candidate's items to avoid redundant API calls if an item appears multiple times
        temp_embedding_cache = {}
        unique_texts_for_api = list(set(items_to_embed_texts))
        
        if unique_texts_for_api:
            logger.info(f"Requesting embeddings for {len(unique_texts_for_api)} unique {data_type}s for candidate {contact_id}.")
            # get_embeddings expects a list and returns a list of embeddings in the same order
            embeddings_list = get_embeddings(unique_texts_for_api, logger)
            for text, embedding_vector in zip(unique_texts_for_api, embeddings_list):
                if embedding_vector: # Ensure embedding is not empty
                    temp_embedding_cache[text] = embedding_vector
                else:
                    logger.warning(f"Failed to get embedding for '{text}' for candidate {contact_id}.")
                    # Potentially mark this as a partial failure if some embeddings are missing
                    # For now, if any embedding fails, the whole operation for this mode might be considered failed
                    # or we can collect specific error messages.
                    # Let's assume if get_embeddings returns empty for a text, it's a failure for that text.
                    # If all texts fail to get embeddings, then it's a larger failure.
        
        # Prepare insert data
        insert_batch_data = []
        for item_detail in candidate_item_details_for_insert:
            text_val = item_detail['text'] # Use text_val for clarity
            embedding = temp_embedding_cache.get(text_val)
            if embedding:
                if exp_idx_col:
                    insert_batch_data.append((contact_id, text_val, embedding, item_detail['exp_idx']))
                elif mode == 'degree_certification_object': # text_val is a JSON string
                    insert_batch_data.append((contact_id, text_val, embedding)) # Will be cast to JSONB in query
                else:
                    insert_batch_data.append((contact_id, text_val, embedding))
            else:
                logger.warning(f"Skipping insert for '{text_val}' for candidate {contact_id} due to missing embedding.")

        if not insert_batch_data:
            logger.info(f"No valid embeddings to insert for candidate {contact_id}, mode {mode}.")
            # This could be because all API calls failed or no valid items were extracted that had embeddings
            # If items_to_embed_texts was non-empty but insert_batch_data is empty, it implies embedding failures.
            if items_to_embed_texts: # If there were items but no embeddings to insert
                error_reason = f"No valid embeddings were generated or retrieved for {len(items_to_embed_texts)} items for candidate {contact_id}, mode {mode}."
                logger.warning(error_reason)
                return False, error_reason 
            return True, None # Processed, but nothing to insert (e.g. no items extracted initially)

        if dry_run:
            logger.info(f"[DRY RUN] Would insert {len(insert_batch_data)} {data_type} embeddings for candidate {contact_id}.")
            for item_data_tuple in insert_batch_data[:3]:
                logger.info(f"  [DRY RUN] Data: {item_data_tuple[0]}, {item_data_tuple[1][:50]}..., {len(item_data_tuple[2])} dims")
            return True, None

        # Insert into target table
        target_connector = PostgresConnectorV2(env=pg_env, logger=logger)
        target_conn = target_connector.connect()
        target_schema = target_connector.schema
        
        with target_conn.cursor() as cursor:
            if exp_idx_col:
                # Special handling for candidate_latest_work_experience_embedding: PK is (contact_id, experience_index)
                if target_table_name == "candidate_latest_work_experience_embedding":
                    query = f"""
                        INSERT INTO {target_schema}.{target_table_name} (contact_id, {text_col}, {embedding_col}, {exp_idx_col})
                        VALUES (%s, %s, %s, %s)
                        ON CONFLICT (contact_id, {exp_idx_col}) DO UPDATE SET
                            {embedding_col} = EXCLUDED.{embedding_col},
                            {text_col} = EXCLUDED.{text_col};
                    """
                else:
                    query = f"""
                        INSERT INTO {target_schema}.{target_table_name} (contact_id, {text_col}, {embedding_col}, {exp_idx_col})
                        VALUES (%s, %s, %s, %s)
                        ON CONFLICT (contact_id, {text_col}) DO UPDATE SET
                            {exp_idx_col} = LEAST(EXCLUDED.{exp_idx_col}, {target_schema}.{target_table_name}.{exp_idx_col}),
                            {embedding_col} = EXCLUDED.{embedding_col};
                    """
            elif mode == 'degree_certification_object':
                query = f"""
                    INSERT INTO {target_schema}.{target_table_name} (contact_id, {text_col}, {embedding_col})
                    VALUES (%s, %s::jsonb, %s)
                    ON CONFLICT (contact_id) DO UPDATE SET
                        {text_col} = EXCLUDED.{text_col},
                        {embedding_col} = EXCLUDED.{embedding_col};
                """
            else: # skills, tools, simple degree/cert strings
                query = f"""
                    INSERT INTO {target_schema}.{target_table_name} (contact_id, {text_col}, {embedding_col})
                    VALUES (%s, %s, %s)
                    ON CONFLICT DO NOTHING;
                """
            
            execute_batch(cursor, query, insert_batch_data, page_size=len(insert_batch_data)) # Insert all for this one candidate
            target_conn.commit()
            logger.info(f"Successfully inserted/updated {len(insert_batch_data)} {data_type} embeddings for candidate {contact_id}.")
            success = True

    except psycopg2.Error as db_err:
        error_reason = f"Database error processing {mode} for candidate {contact_id}: {str(db_err)}"
        logger.error(error_reason, exc_info=True)
        if source_conn: source_conn.rollback()
        if target_conn: target_conn.rollback()
        success = False
    except Exception as e:
        error_reason = f"Unexpected error processing {mode} for candidate {contact_id}: {str(e)}"
        logger.error(error_reason, exc_info=True)
        if source_conn: source_conn.rollback()
        if target_conn: target_conn.rollback()
        success = False
    finally:
        if source_conn: source_conn.close()
        if target_conn: target_conn.close()
    
    return success, error_reason

# --- Main Processing Function ---
def process_candidate_data(
    logger: AppLogger,
    mode: Literal['job_title', 'soft_skill', 'technical_skill', 'tool_platform', 'degree_certification', 'industry', 'degree_certification_object', 'latest_work_experience'],  # Added 'degree_certification_object'
    limit: Optional[int] = None,
    dry_run: bool = True,
    resume_id: Optional[str] = None,
    temp_mode: bool = False,
    created_before_date: Optional[str] = None,  # New parameter
    created_after_date: Optional[str] = None  # New parameter for date range
):
    # Construct dynamic checkpoint file name
    checkpoint_file_name = f"candidate_{mode}_embedding_checkpoint.txt"
    logger.info(f"Using checkpoint file: {checkpoint_file_name}")

    # --- MODIFICATION: Use helper function for mode configurations ---
    mode_configs = _get_mode_configurations(mode)
    if not mode_configs:
        logger.error(f"Invalid mode specified: {mode}")
        return

    data_type = mode_configs["data_type"]
    source_column = mode_configs["source_column_name"]
    extraction_function = mode_configs["extraction_func"]
    target_table = mode_configs["target_table_name"]
    target_text_column = mode_configs["text_col"]
    target_embedding_column = mode_configs["embedding_col"]
    target_experience_index_column = mode_configs.get("exp_idx_col") # Handles modes with/without this
    # --- END MODIFICATION ---

    logger.info(f"--- Starting Candidate {data_type} Embedding Generation ---")
    logger.info(f"Mode: {mode}, Target Table: {target_table}")
    if temp_mode:
        logger.info(f"--- RUNNING IN TEMP MODE --- Reading contact_ids from temp_candidates.json.")
    if limit is not None and not temp_mode:
        logger.info(f"Processing limit set to {limit} candidates.")
    elif not temp_mode:
        # Keep default limit or adjust if needed
        default_limit = 162000
        limit = default_limit
        logger.info(f"No limit provided, using default limit of {limit} candidates.")
    else:
        limit = None  # Ignore limit in temp_mode
        logger.info("Limit and offset are ignored in temp mode.")
    if resume_id:
        logger.info(f"Resuming from candidate contact_id > {resume_id}")
    if created_before_date and not temp_mode:
        logger.info(f"Filtering candidates created before {created_before_date}")
    if created_after_date and not temp_mode: # New log
        logger.info(f"Filtering candidates created on or after {created_after_date}")

    # In-memory cache for embeddings {text: embedding_vector}
    embedding_cache: Dict[str, List[float]] = {}
    total_candidates_processed = 0
    total_items_found = 0  # Generic name for job titles or skills
    total_embeddings_generated_api = 0
    total_potential_inserts = 0
    processed_candidate_ids: Set[uuid.UUID] = set()
    last_processed_id_for_checkpoint = None  # Track last ID for checkpoint

    # Connections - Initialize to None
    source_connection = None
    target_connection = None

    try:
        # Establish source connection from PROD
        source_env = PostgresEnvironment.PROD
        source_connector = PostgresConnectorV2(env=source_env, logger=logger)
        source_connection = source_connector.connect()
        source_schema = source_connector.schema

        # Establish target connection from PROD for inserting embeddings
        target_env = PostgresEnvironment.PROD
        target_connector = PostgresConnectorV2(env=target_env, logger=logger)
        target_connection = target_connector.connect()
        target_schema = target_connector.schema  # Target schema might be different

        # Prepare the fetch query based on mode
        if temp_mode:
            JSON_CANDIDATE_FILE = "temp_candidates.json"
            contact_ids_from_file = None
            if os.path.exists(JSON_CANDIDATE_FILE):
                try:
                    with open(JSON_CANDIDATE_FILE, 'r') as f:
                        contact_ids_from_file = json.load(f)
                    if not isinstance(contact_ids_from_file, list):
                        logger.error("JSON file must contain a list of contact_ids.")
                        return
                    logger.info(f"Loaded {len(contact_ids_from_file)} contact_ids from {JSON_CANDIDATE_FILE}")
                except Exception as e:
                    logger.error(f"Failed to load candidate list from {JSON_CANDIDATE_FILE}: {e}", exc_info=True)
                    return

            if contact_ids_from_file is not None:
                placeholders = ', '.join(['%s'] * len(contact_ids_from_file))
                fetch_query = f"""
                    SELECT contact_id, {source_column}
                    FROM {source_schema}.{SOURCE_TABLE}
                    WHERE contact_id IN ({placeholders});
                """
                try:
                    with source_connection.cursor(cursor_factory=RealDictCursor) as source_fetch_cursor:
                        source_fetch_cursor.execute(fetch_query, tuple(contact_ids_from_file))
                        all_candidates = source_fetch_cursor.fetchall()
                    logger.info(f"Fetched {len(all_candidates)} candidates using contact_ids from JSON.")
                    candidate_batches = [all_candidates[i:i + FETCH_BATCH_SIZE] for i in range(0, len(all_candidates), FETCH_BATCH_SIZE)]
                except Exception as fetch_err:
                    logger.error(f"Error fetching candidate data: {fetch_err}", exc_info=True)
                    candidate_batches = []
            else:
                logger.info(f"{JSON_CANDIDATE_FILE} not found. Fetching contact_ids from {source_schema}.{TEMP_SOURCE_TABLE} and joining with {source_schema}.{SOURCE_TABLE}")
                fetch_query = f"""
                    SELECT c.contact_id, c.{source_column}
                    FROM {source_schema}.{TEMP_SOURCE_TABLE} t2
                    JOIN {source_schema}.{SOURCE_TABLE} c ON t2.contact_id = c.contact_id;
                """
                try:
                    with source_connection.cursor(cursor_factory=RealDictCursor) as source_fetch_cursor:
                        source_fetch_cursor.execute(fetch_query)
                        all_candidates = source_fetch_cursor.fetchall()
                    logger.info(f"Fetched {len(all_candidates)} candidates based on {TEMP_SOURCE_TABLE}.")
                    candidate_batches = [all_candidates[i:i + FETCH_BATCH_SIZE] for i in range(0, len(all_candidates), FETCH_BATCH_SIZE)]
                except (Exception, psycopg2.Error) as fetch_err:
                    logger.error(f"Error during temp mode candidate fetch: {fetch_err}", exc_info=True)
                    candidate_batches = []  # Ensure loop doesn't run

        else:  # Normal mode (existing logic with batching, limit, offset, resume_id, created_before_date)
            # Build WHERE clauses
            where_clauses = []
            query_params = []

            if created_before_date:
                where_clauses.append("created_at < %s")
                query_params.append(created_before_date)
            
            if created_after_date: # New condition
                where_clauses.append("created_at >= %s")
                query_params.append(created_after_date)

            # `resume_id` here refers to the initial resume_id from args or checkpoint.
            # This determines the primary pagination strategy.
            if resume_id:
                # If resuming with a specific ID, this ID is part of the WHERE clause.
                where_clauses.append("contact_id::text > %s")
                query_params.append(resume_id)

            where_sql = f"WHERE {' AND '.join(where_clauses)}" if where_clauses else ""

            # --- MODIFICATION START 1 ---
            # Determine base_fetch_query based on whether initial resume_id is provided.
            if resume_id: 
                # If initial resume_id is set, pagination is driven by contact_id > %s.
                # The WHERE clause (where_sql) includes this condition.
                base_fetch_query = f"""
                    SELECT contact_id, {source_column}, created_at
                    FROM {source_schema}.{SOURCE_TABLE}
                    {where_sql}
                    ORDER BY created_at, contact_id
                    LIMIT %s;
                """
            else:
                # If initial resume_id is NOT set, use LIMIT %s OFFSET %s for pagination.
                # where_sql will contain date filters if they are active.
                base_fetch_query = f"""
                    SELECT contact_id, {source_column}, created_at
                    FROM {source_schema}.{SOURCE_TABLE}
                    {where_sql}
                    ORDER BY created_at, contact_id
                    LIMIT %s OFFSET %s;
                """
            # --- MODIFICATION END 1 ---
            current_offset = 0
            remaining_limit = limit

        # --- Processing Loop ---
        batch_iterator = candidate_batches if temp_mode else None
        batch_index = 0

        while True:
            candidate_batch = []

            if temp_mode:
                if batch_index < len(candidate_batches):
                    candidate_batch = candidate_batches[batch_index]
                    logger.info(f"Processing temp mode batch {batch_index + 1}/{len(candidate_batches)} (Size: {len(candidate_batch)})...")
                    batch_index += 1
                else:
                    logger.info("Finished processing all temp mode batches.")
                    break  # Exit loop after processing all temp batches
            else:  # Normal mode fetching
                if remaining_limit is not None and remaining_limit <= 0:
                    logger.info("Reached processing limit.")
                    break
                fetch_batch_limit = min(FETCH_BATCH_SIZE, remaining_limit) if remaining_limit is not None else FETCH_BATCH_SIZE
                
                # --- MODIFICATION START 2 ---
                # Prepare parameters for the SQL query.
                # `query_params` contains values for the WHERE clause (date filters, and initial/updated resume_id if applicable).
                # `resume_id` variable reflects the current pagination mode (None for offset, a value for ID-based).
                if resume_id: # If pagination is by resume_id (initial resume_id was set)
                    logger.info(f"Fetching next batch of up to {fetch_batch_limit} candidates (using resume_id: {query_params[-1] if query_params and 'contact_id::text > %s' in where_sql else 'N/A'})...")
                    current_params = tuple(query_params) + (fetch_batch_limit,)
                else: # If pagination is by offset (initial resume_id was None)
                    logger.info(f"Fetching next batch of up to {fetch_batch_limit} candidates (Offset: {current_offset})...")
                    current_params = tuple(query_params) + (fetch_batch_limit, current_offset)
                # --- MODIFICATION END 2 ---

                try:
                    with source_connection.cursor(cursor_factory=RealDictCursor) as source_fetch_cursor:
                        source_fetch_cursor.execute(base_fetch_query, current_params)
                        candidate_batch = source_fetch_cursor.fetchall()
                except (Exception, psycopg2.Error) as fetch_err:
                    logger.error(f"Error during candidate fetch: {fetch_err}", exc_info=True)
                    break

                if not candidate_batch:
                    logger.info("No more candidates found.")
                    break

            # --- Batch Processing Logic (Common for both modes) ---
            batch_size = len(candidate_batch)
            items_needing_embedding: Set[str] = set()  # Generic name
            candidate_item_details: List[Any] = []  # Stores (candidate_id, item_text) or (candidate_id, item_text, exp_idx)

            # 1. Extract items (titles or skills or industries) and identify unique items needing embedding
            for candidate_row in candidate_batch:
                candidate_id_val = candidate_row.get('contact_id') # Use candidate_id_val for clarity
                source_data = candidate_row.get(source_column)  # Get data from the correct column

                if not candidate_id_val:
                    logger.warning(f"Skipping row due to missing candidate_id: {candidate_row}")
                    continue

                # Track processed candidates regardless of whether they had items
                if candidate_id_val not in processed_candidate_ids:
                    processed_candidate_ids.add(candidate_id_val)
                    total_candidates_processed += 1

                # Use the appropriate extraction function
                # Check if target_experience_index_column is set by _get_mode_configurations
                if target_experience_index_column: # Covers 'industry' and 'job_title'
                    extracted_items_with_indices = extraction_function(source_data, candidate_id_val, logger)  # List[Tuple[str, int]]
                    total_items_found += len(extracted_items_with_indices)  # Count each occurrence
                    for item_text, exp_idx in extracted_items_with_indices:
                        candidate_item_details.append((candidate_id_val, item_text, exp_idx))
                        try:
                            if item_text not in embedding_cache:  # item_text is expected to be a string
                                items_needing_embedding.add(item_text)
                        except TypeError:
                            logger.error(f"Unhashable item_text detected for candidate {candidate_id_val}: type={type(item_text)}, value={item_text}")
                else:
                    extracted_items = extraction_function(source_data, candidate_id_val, logger)  # List[str]
                    total_items_found += len(extracted_items)
                    for item_text in extracted_items:
                        candidate_item_details.append((candidate_id_val, item_text))
                        if item_text not in embedding_cache:
                            items_needing_embedding.add(item_text)

            # Log processed items per candidate
            candidate_items_map: Dict[uuid.UUID, List[str]] = defaultdict(list)
            for detail_tuple in candidate_item_details:
                cid = detail_tuple[0]
                item_text = detail_tuple[1]  # Text is always the second element
                candidate_items_map[cid].append(item_text)

            logged_candidates_in_batch = 0
            max_logged_candidates_per_batch = 5
            for cid, items in candidate_items_map.items():
                if logged_candidates_in_batch < max_logged_candidates_per_batch:
                    items_info = ", ".join([
                        f"'{t}' [{len(embedding_cache[t])} dims]" if t in embedding_cache and embedding_cache[t]
                        else f"'{t}' [no embedding]"
                        for t in items
                    ])
                    logger.info(f"Candidate {cid}: Processed {data_type}s: {items_info}")
                    logged_candidates_in_batch += 1
                elif logged_candidates_in_batch == max_logged_candidates_per_batch:
                    logger.info(f"     (Logging limited to first {max_logged_candidates_per_batch} candidates with {data_type}s in this batch)")
                    logged_candidates_in_batch += 1  # Prevent re-logging this message

            # 2. Generate embeddings via API for items not in cache
            items_for_api = list(items_needing_embedding)
            logger.info(f"Found {len(items_for_api)} unique {data_type}s in batch requiring OpenAI API call.")

            if items_for_api:
                encoding = None
                try:
                    encoding = tiktoken.encoding_for_model(EMBEDDING_MODEL)
                except Exception as e:
                    logger.error(f"Failed to load tiktoken encoding for model {EMBEDDING_MODEL}: {e}. "
                                 f"Proceeding with item count based batching ({EMBEDDING_API_BATCH_SIZE} items per batch) without token checking.")

                current_sub_batch_texts: List[str] = []
                current_sub_batch_tokens = 0
                sub_batch_api_call_counter = 0

                for item_text_original in items_for_api:
                    item_tokens = 0
                    text_to_count = ""

                    if encoding:
                        # Prepare text for token counting similar to get_embeddings pre-processing
                        text_to_count = item_text_original.replace("\n", " ").strip()
                        if not text_to_count: # Should be rare if items_needing_embedding are pre-filtered
                            logger.debug(f"Skipping empty text derived from original: '{item_text_original[:50]}...'")
                            continue
                        try:
                            item_tokens = len(encoding.encode(text_to_count))
                        except Exception as e:
                            logger.warning(f"Could not encode text to count tokens: '{text_to_count[:50]}...'. Error: {e}. Skipping this item for API call.")
                            continue
                    
                    # Check if the current item itself is too large (only if encoding is available)
                    if encoding and item_tokens > SAFE_MAX_TOKENS_PER_API_CALL:
                        logger.warning(f"Item (cleaned: '{text_to_count[:100]}...') from original '{item_text_original[:50]}...' is too long ({item_tokens} tokens) "
                                       f"and exceeds SAFE_MAX_TOKENS_PER_API_CALL ({SAFE_MAX_TOKENS_PER_API_CALL}). It will be skipped.")
                        continue

                    # Determine if the current sub-batch needs to be sent
                    send_batch_now = False
                    if current_sub_batch_texts: # If there's anything in the current sub-batch
                        if encoding: # Token-based splitting
                            if current_sub_batch_tokens + item_tokens > SAFE_MAX_TOKENS_PER_API_CALL or \
                               len(current_sub_batch_texts) >= EMBEDDING_API_BATCH_SIZE:
                                send_batch_now = True
                        else: # Fallback to item count based splitting
                            if len(current_sub_batch_texts) >= EMBEDDING_API_BATCH_SIZE:
                                send_batch_now = True
                    
                    if send_batch_now:
                        sub_batch_api_call_counter += 1
                        logger.info(f"Generating embeddings via API for {len(current_sub_batch_texts)} {data_type}s "
                                    f"(Sub-batch {sub_batch_api_call_counter}, Tokens: {current_sub_batch_tokens if encoding else 'N/A'})...")
                        
                        embeddings_sub_batch_result = get_embeddings(current_sub_batch_texts, logger)
                        for text_processed, embedding in zip(current_sub_batch_texts, embeddings_sub_batch_result):
                            if embedding:
                                embedding_cache[text_processed] = embedding # text_processed is the original item text
                                total_embeddings_generated_api += 1
                            else:
                                logger.warning(f"Failed to generate/retrieve embedding via API for {data_type}: '{text_processed}'. It will be skipped.")
                        
                        current_sub_batch_texts = []
                        current_sub_batch_tokens = 0

                    # Add current item to the sub-batch
                    current_sub_batch_texts.append(item_text_original)
                    if encoding:
                        current_sub_batch_tokens += item_tokens

                # Process any remaining items in the last sub-batch
                if current_sub_batch_texts:
                    sub_batch_api_call_counter += 1
                    logger.info(f"Generating embeddings via API for {len(current_sub_batch_texts)} {data_type}s "
                                f"(Sub-batch {sub_batch_api_call_counter} [final], Tokens: {current_sub_batch_tokens if encoding else 'N/A'})...")
                    embeddings_sub_batch_result = get_embeddings(current_sub_batch_texts, logger)
                    for text_processed, embedding in zip(current_sub_batch_texts, embeddings_sub_batch_result):
                        if embedding:
                            embedding_cache[text_processed] = embedding
                            total_embeddings_generated_api += 1
                        else:
                            logger.warning(f"Failed to generate/retrieve embedding via API for {data_type}: '{text_processed}'. It will be skipped.")
            
            # 3. Prepare potential insert data
            potential_insert_data: List[Tuple] = []  # Generic tuple list
            if target_experience_index_column: # Covers 'industry' and 'job_title'
                for candidate_id_val, item_text, exp_idx in candidate_item_details:
                    # Ensure item is in cache AND the embedding is not empty/None
                    try:
                        if item_text in embedding_cache and embedding_cache[item_text]:
                            potential_insert_data.append((candidate_id_val, item_text, embedding_cache[item_text], exp_idx))
                    except TypeError:
                        logger.error(f"Unhashable item_text in insert phase for candidate {candidate_id_val}: type={type(item_text)}, value={item_text}")
            else:
                for candidate_id_val, item_text in candidate_item_details:  # This was candidate_item_pairs
                    if item_text in embedding_cache and embedding_cache[item_text]:
                        potential_insert_data.append((candidate_id_val, item_text, embedding_cache[item_text]))

            if potential_insert_data:
                logger.info(f"--- Would potentially insert {len(potential_insert_data)} {data_type} embeddings for this batch ---")
                total_potential_inserts += len(potential_insert_data)
                for idx, item_tuple in enumerate(potential_insert_data[:3]):
                    if target_experience_index_column:
                        logger.info(f"  CandidateID={item_tuple[0]}, {data_type}='{item_tuple[1]}', Embedding=[{len(item_tuple[2])} dims], ExpIdx={item_tuple[3]}")
                    else:
                        logger.info(f"  CandidateID={item_tuple[0]}, {data_type}='{item_tuple[1]}', Embedding=[{len(item_tuple[2])} dims]")

                # 4. If not dry-run, execute the actual INSERT into target DB using execute_batch
                if not dry_run:
                    try:
                        # Check and re-establish target_connection if it's closed or None
                        if target_connection is None or target_connection.closed != 0: # psycopg2 .closed is 0 if open, non-zero if closed
                            state_msg = 'None' if target_connection is None else f'closed (state: {target_connection.closed})'
                            logger.warning(f"Target connection is {state_msg}. Attempting to reconnect...")
                            
                            if target_connection and target_connection.closed != 0: # If it was closed but not None
                                try:
                                    target_connection.close() # Attempt a clean close
                                except Exception as e_close:
                                    logger.debug(f"Exception during close of problematic target connection: {e_close}")
                            
                            # Re-initialize the target connector and connection
                            # target_env is defined at the start of process_candidate_data
                            target_connector = PostgresConnectorV2(env=target_env, logger=logger) # Re-get connector
                            target_connection = target_connector.connect()
                            target_schema = target_connector.schema # IMPORTANT: Re-assign schema from new connector

                            if target_connection and target_connection.closed == 0:
                                logger.info("Target connection re-established successfully.")
                            else:
                                logger.error("Failed to re-establish target connection. Skipping insert for this batch.")
                                continue # Skip to the next batch if reconnection fails

                        with target_connection.cursor() as dest_cursor:
                            if target_experience_index_column:
                                # Special handling for candidate_latest_work_experience_embedding: PK is (contact_id, experience_index)
                                if target_table == "candidate_latest_work_experience_embedding":
                                    insert_query = f"""
                                        INSERT INTO {target_schema}.{target_table} (contact_id, {target_text_column}, {target_embedding_column}, {target_experience_index_column})
                                        VALUES (%s, %s, %s, %s)
                                        ON CONFLICT (contact_id, {target_experience_index_column}) DO UPDATE SET
                                            {target_embedding_column} = EXCLUDED.{target_embedding_column},
                                            {target_text_column} = EXCLUDED.{target_text_column};
                                    """
                                else:
                                    insert_query = f"""
                                        INSERT INTO {target_schema}.{target_table} (contact_id, {target_text_column}, {target_embedding_column}, {target_experience_index_column})
                                        VALUES (%s, %s, %s, %s)
                                        ON CONFLICT (contact_id, {target_text_column}) DO UPDATE SET
                                            {target_experience_index_column} = LEAST({target_schema}.{target_table}.{target_experience_index_column}, EXCLUDED.{target_experience_index_column}),
                                            {target_embedding_column} = EXCLUDED.{target_embedding_column};
                                    """
                            elif mode == 'degree_certification_object': # Specific insert for the new mode
                                insert_query = f"""
                                    INSERT INTO {target_schema}.{target_table} (contact_id, {target_text_column}, {target_embedding_column})
                                    VALUES (%s, %s::jsonb, %s)
                                    ON CONFLICT (contact_id) DO UPDATE SET
                                        {target_text_column} = EXCLUDED.{target_text_column},
                                        {target_embedding_column} = EXCLUDED.{target_embedding_column};
                                """
                            else: # For soft_skill, technical_skill, tool_platform, degree_certification (string)
                                insert_query = f"""
                                    INSERT INTO {target_schema}.{target_table} (contact_id, {target_text_column}, {target_embedding_column})
                                    VALUES (%s, %s, %s)
                                    ON CONFLICT DO NOTHING;
                                """
                            execute_batch(dest_cursor, insert_query, potential_insert_data, page_size=INSERT_BATCH_SIZE)
                        target_connection.commit()  # Commit after the batch execution
                        logger.info(f"Attempted insert of {len(potential_insert_data)} {data_type} embeddings using execute_batch (page size: {INSERT_BATCH_SIZE}).")
                    except Exception as insert_err:
                        # Log the error
                        logger.error(f"Error inserting {data_type} embeddings using execute_batch: {insert_err}", exc_info=True)
                        # Attempt to rollback if connection is still valid and open
                        if target_connection and target_connection.closed == 0:
                            try:
                                target_connection.rollback()
                                logger.info("Target connection rolled back due to insert error.")
                            except Exception as rb_err:
                                logger.error(f"Target connection rollback failed after insert error: {rb_err}")
                        # The check at the start of this try block will handle reconnection on the next iteration if needed.
                        continue  # Skip to next batch
            else:
                logger.info(f"No valid {data_type} embeddings generated or found in cache for this batch to potentially insert.")

            # --- Checkpoint Update ---
            # Update checkpoint after successful processing/insertion (or logging in dry-run) of the batch
            if candidate_batch:
                # Get the ID of the last candidate successfully processed in *this* batch
                last_candidate_in_batch = candidate_batch[-1]
                # Ensure we get the created_at as well for checkpointing if needed, though current checkpoint is ID based
                last_processed_id_for_checkpoint = last_candidate_in_batch.get('contact_id')
                # last_processed_created_at_for_checkpoint = last_candidate_in_batch.get('created_at')


                if last_processed_id_for_checkpoint:
                    try:
                        with open(checkpoint_file_name, 'w') as f:
                            # Checkpoint now stores ID and optionally created_at if logic changes
                            # For now, only ID is used by resume_id logic
                            f.write(str(last_processed_id_for_checkpoint))
                        logger.info(f"Checkpoint updated. Last successfully processed contact_id: {last_processed_id_for_checkpoint} in {checkpoint_file_name}")
                    except IOError as e:
                        logger.error(f"Failed to write checkpoint file '{checkpoint_file_name}': {e}")
                else:
                    logger.warning("Could not determine last processed contact_id in the batch for checkpoint.")

            # 5. Update batching parameters (only for normal mode):
            if not temp_mode:
                if candidate_batch:  # Ensure batch is not empty before proceeding
                    # `resume_id` variable reflects the pagination mode for the current run.
                    # If it's non-None, we are in ID-based pagination.
                    # If it's None, we are in offset-based pagination.
                    if resume_id:
                        # Update resume_id for the next iteration if in ID-based pagination mode
                        if last_processed_id_for_checkpoint:
                            new_resume_id_val = str(last_processed_id_for_checkpoint)
                            # Rebuild query_params to include the new resume_id for the next fetch
                            # The `resume_id` variable itself is updated, and `query_params` list is modified.
                            temp_query_params = []
                            if created_before_date:
                                temp_query_params.append(created_before_date)
                            if created_after_date: 
                                temp_query_params.append(created_after_date)
                            temp_query_params.append(new_resume_id_val) # Add new resume_id
                            
                            query_params = temp_query_params # query_params now has the updated resume_id
                            # The `resume_id` variable used in conditionals also needs to reflect this for the next loop start
                            # This was implicitly handled by `resume_id = str(last_processed_id_for_checkpoint)` in original logic.
                            # For clarity and to ensure `resume_id` variable matches `query_params` content:
                            resume_id = new_resume_id_val 
                        else:
                            logger.warning("Last candidate in batch missing contact_id, cannot update resume_id. Stopping.")
                            break
                    # --- MODIFICATION START 3 ---
                    else:  # We are in offset-based pagination mode (initial resume_id was None)
                        current_offset += len(candidate_batch)
                    # --- MODIFICATION END 3 ---

                    if remaining_limit is not None:
                        remaining_limit -= len(candidate_batch)
                else:
                    # Should have been caught earlier, but as a safeguard
                    logger.info("Empty candidate batch encountered unexpectedly in normal mode. Stopping.")
                    break

            logger.info(f"Batch finished. Total candidates processed so far: {total_candidates_processed}")
            if not temp_mode and limit is not None:
                logger.info(f"     (Overall limit: {limit})")

    except (Exception, psycopg2.DatabaseError) as error:
        logger.error(f"An error occurred: {error}", exc_info=True)
        try:
            if source_connection and not source_connection.closed:
                source_connection.rollback()
            if target_connection and not target_connection.closed:
                target_connection.rollback()
        except Exception as rb_err:
            logger.error(f"Connection rollback failed: {rb_err}")
    finally:
        # Close connections safely
        if source_connection:
            try:
                source_connection.close()
                logger.info("Closed connection to source database.")
            except Exception as conn_err:
                logger.error(f"Failed to close source_connection: {conn_err}")
        if target_connection:
            try:
                target_connection.close()
                logger.info("Closed connection to target database.")
            except Exception as conn_err:
                logger.error(f"Failed to close target_connection: {conn_err}")

    logger.info(f"--- Candidate {data_type} Embedding Generation Finished ---")
    logger.info(f"Summary: Mode={mode}, Candidates Processed={total_candidates_processed}, Total {data_type}s Found={total_items_found}, Embeddings Generated (API)={total_embeddings_generated_api}, Total Potential Inserts={total_potential_inserts}")
    if last_processed_id_for_checkpoint:
        logger.info(f"Final checkpoint recorded contact_id: {last_processed_id_for_checkpoint} in {checkpoint_file_name}")


# --- Date Validation Function ---
def valid_date(s):
    try:
        return datetime.datetime.strptime(s, "%Y-%m-%d").strftime("%Y-%m-%d")
    except ValueError:
        msg = f"Not a valid date: '{s}'. Expected format: YYYY-MM-DD."
        raise argparse.ArgumentTypeError(msg)

def valid_uuid(s):
    try:
        val = uuid.UUID(s)
        return str(val)
    except ValueError:
        msg = f"Not a valid UUID: '{s}'."
        raise argparse.ArgumentTypeError(msg)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(
       
        description="Generate embeddings for candidate job titles, soft skills, technical skills, tools/platforms, degrees/certifications, degree/certification JSON objects, or industries."
    )
    # --- Mode Selection ---
    # Mode group is not strictly required anymore, as --all with --contact-id is an alternative
    mode_group = parser.add_mutually_exclusive_group() 
    mode_group.add_argument(
        "--generate-job-title",
        action="store_true",
        help="Generate embeddings for job titles from the 'work_experience' column."
    )
    mode_group.add_argument(
        "--generate-soft-skills",
        action="store_true",
        help="Generate embeddings for soft skills from the 'resume_data' column."
    )
    mode_group.add_argument(
        "--generate-technical-skills",
        action="store_true",
        help="Generate embeddings for technical skills from the 'resume_data' column."
    )
    mode_group.add_argument(
        "--generate-tools-platforms",
        action="store_true",
        help="Generate embeddings for tools and platforms from the 'resume_data' column."
    )
    mode_group.add_argument(
        "--generate-degrees-certifications",
        action="store_true",
        help="Generate embeddings for degrees and certifications from the 'resume_data' column."
    )
    mode_group.add_argument(
        "--generate-degrees-certifications-as-json-object",
        action="store_true",
        help="Generate embeddings for the entire 'degrees and certifications' JSON object from 'resume_data'."
    )
    mode_group.add_argument(
        "--generate-industry",
        action="store_true",
        help="Generate embeddings for industries from the 'work_experience' column."
    )
    mode_group.add_argument(
        "--latest-work-experience",
        action="store_true",
        help="Generate embeddings for two years worth of 'work_experience'"
    )

    # --- Common Arguments ---
    parser.add_argument(
        "--limit",
        type=int,
        default=None,
        help="Limit processing to the first N candidates (default: 162000 if not specified, ignored if --contact-id is used)."
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="If passed, run in dry-run mode (no data will be inserted). If not passed, embeddings are inserted into the database."
    )
    parser.add_argument(
        "--resume-id",
        type=str,
        default="",
        help="Resume processing from candidates with contact_id greater than this value (reads from a mode-specific checkpoint file if empty and file exists, ignored if --contact-id is used)."
    )
    parser.add_argument(
        "--temp-mode",
        action="store_true",
        help=f"If passed, read contact_ids from the '{TEMP_SOURCE_TABLE}' table instead of '{SOURCE_TABLE}'. Limit, resume-id, created-before, created-after, and contact-id are ignored or have limited effect if --contact-id is not the primary driver."
    )
    parser.add_argument(
        "--created-before",
        type=valid_date,
        default=None,
        help="Only process candidates created before this date (format: YYYY-MM-DD). Ignored if --contact-id is used."
    )
    parser.add_argument(
        "--created-after",
        type=valid_date,
        default=None,
        help="Only process candidates created on or after this date (format: YYYY-MM-DD). Ignored if --contact-id is used."
    )
    parser.add_argument(
        "--contact-id",
        type=valid_uuid,
        default=None,
        help="Generate embeddings for a single candidate with this contact_id (UUID). If provided, --limit, --resume-id, --temp-mode, --created-before, and --created-after are typically ignored."
    )
    parser.add_argument(
        "--all",
        action="store_true",
        help="Used with --contact-id to generate embeddings for ALL available modes for that candidate. Specific mode flags (e.g., --generate-job-title) should not be used with --all."
    )
    parser.add_argument(
        "--skip-regenerate-embedding",
        action="store_true",
        help="If passed with --contact-id, skip regenerating embeddings if they already exist for the specified mode(s)."
    )
    args = parser.parse_args()

    # --- Setup ---
    load_secrets_env_variables()
    logger = AppLogger({"level": "INFO", "log_to_stdout": True})
    if not os.getenv("OPENAI_API_KEY"):
        logger.error("OPENAI_API_KEY environment variable is not set.")
        exit(1)

    # --- Determine active mode flags ---
    active_mode_map = {
        'job_title': args.generate_job_title,
        'soft_skill': args.generate_soft_skills,
        'technical_skill': args.generate_technical_skills,
        'tool_platform': args.generate_tools_platforms,
        'degree_certification': args.generate_degrees_certifications,
        'degree_certification_object': args.generate_degrees_certifications_as_json_object,
        'industry': args.generate_industry,
        'latest_work_experience': args.latest_work_experience,
    }
    selected_modes = [mode for mode, is_set in active_mode_map.items() if is_set]

    # --- Argument Validation and Execution Flow ---
    if args.all:
        if not args.contact_id:
            parser.error("--all requires --contact-id.")
        if selected_modes:
            parser.error(f"--all cannot be used with specific mode flags (e.g., --generate-job-title). Found: {', '.join(selected_modes)}")
        
        # Warn about ignored flags when using --contact-id with --all
        ignored_flags_for_contact_id_all = []
        if args.limit is not None: ignored_flags_for_contact_id_all.append("--limit")
        if args.resume_id: ignored_flags_for_contact_id_all.append("--resume-id")
        if args.temp_mode: ignored_flags_for_contact_id_all.append("--temp-mode")
        if args.created_before: ignored_flags_for_contact_id_all.append("--created-before")
        if args.created_after: ignored_flags_for_contact_id_all.append("--created-after")
        if ignored_flags_for_contact_id_all:
            logger.warning(f"The following flags are ignored when using --contact-id with --all: {', '.join(ignored_flags_for_contact_id_all)}")

        logger.info(f"--- Single Candidate (--all) Mode: Generating ALL embeddings for contact_id {args.contact_id} ---")
        if args.dry_run:
            logger.info("Executing in DRY-RUN mode. No data will be inserted into the database.")
        else:
            logger.info("Executing in LIVE mode. Embeddings will be inserted into the database.")

        pg_env_for_single = PostgresEnvironment.PROD 
        overall_success = True
        for mode_to_run in ALL_PROCESSING_MODES:
            logger.info(f"Processing mode: {mode_to_run} for contact_id {args.contact_id}")
            success, error_msg = generate_embeddings_for_single_candidate(
                logger=logger,
                mode=mode_to_run, # type: ignore
                contact_id=args.contact_id,
                pg_env=pg_env_for_single,
                dry_run=args.dry_run,
                skip_regenerate_embedding=args.skip_regenerate_embedding # Pass the new flag
            )
            if not success:
                overall_success = False
                logger.error(f"Failed to process mode {mode_to_run} for candidate {args.contact_id}. Reason: {error_msg}")
            else:
                logger.info(f"Successfully processed mode {mode_to_run} for candidate {args.contact_id}.")
        exit(0 if overall_success else 1)

    elif args.contact_id: # Single contact_id, specific mode (not --all)
        if not selected_modes:
            parser.error("A specific mode (e.g., --generate-job-title) must be selected when using --contact-id without --all.")
        # The mutually_exclusive_group ensures len(selected_modes) is at most 1.
        # If it's 0, it's caught above. So, it must be 1 here.
        processing_mode = selected_modes[0]

        # Warn about ignored flags when using --contact-id
        ignored_flags_for_contact_id = []
        if args.limit is not None: ignored_flags_for_contact_id.append("--limit")
        if args.resume_id: ignored_flags_for_contact_id.append("--resume-id")
        if args.temp_mode: ignored_flags_for_contact_id.append("--temp-mode")
        if args.created_before: ignored_flags_for_contact_id.append("--created-before")
        if args.created_after: ignored_flags_for_contact_id.append("--created-after")
        if ignored_flags_for_contact_id:
            logger.warning(f"The following flags are ignored when using --contact-id: {', '.join(ignored_flags_for_contact_id)}")

        logger.info(f"--- Single Candidate Mode: Generating {processing_mode} embeddings for contact_id {args.contact_id} ---")
        if args.dry_run:
            logger.info("Executing in DRY-RUN mode. No data will be inserted into the database.")
        else:
            logger.info("Executing in LIVE mode. Embeddings will be inserted into the database.")
        
        pg_env_for_single = PostgresEnvironment.PROD
        success, error_msg = generate_embeddings_for_single_candidate(
            logger=logger,
            mode=processing_mode, # type: ignore
            contact_id=args.contact_id,
            pg_env=pg_env_for_single,
            dry_run=args.dry_run,
            skip_regenerate_embedding=args.skip_regenerate_embedding # Pass the new flag
        )
        if success:
            logger.info(f"Successfully processed single candidate {args.contact_id} for mode {processing_mode}.")
        else:
            logger.error(f"Failed to process single candidate {args.contact_id} for mode {processing_mode}. Reason: {error_msg}")
        exit(0 if success else 1)

    else: # Batch processing mode (no --contact-id, no --all)
        if not selected_modes:
            parser.error("A processing mode (e.g., --generate-job-title) must be selected for batch processing.")
        # The mutually_exclusive_group ensures len(selected_modes) is at most 1.
        # If it's 0, it's caught above. So, it must be 1 here.
        processing_mode = selected_modes[0]

        current_checkpoint_file = f"candidate_{processing_mode}_embedding_checkpoint.txt"
        logger.info(f"Attempting to read checkpoint from: {current_checkpoint_file}")

        resume_id_arg = args.resume_id if args.resume_id.strip() and not args.temp_mode else None
        limit_arg = args.limit if not args.temp_mode else None
        created_before_arg = args.created_before if not args.temp_mode else None
        created_after_arg = args.created_after if not args.temp_mode else None

        if not resume_id_arg and not args.temp_mode:
            try:
                if os.path.exists(current_checkpoint_file):
                    with open(current_checkpoint_file, 'r') as f:
                        resume_id_from_file = f.read().strip()
                        if resume_id_from_file:
                            logger.info(f"No --resume-id provided, resuming from checkpoint file '{current_checkpoint_file}': {resume_id_from_file}")
                            resume_id_arg = resume_id_from_file
                        else:
                            logger.info(f"Checkpoint file '{current_checkpoint_file}' is empty, starting from the beginning.")
                else:
                    logger.info(f"Checkpoint file '{current_checkpoint_file}' not found, starting from the beginning.")
            except IOError as e:
                logger.error(f"Failed to read checkpoint file '{current_checkpoint_file}': {e}. Starting from the beginning.")

        if args.temp_mode:
            logger.info(f"--- TEMP MODE ENABLED: Reading contact_ids from {TEMP_SOURCE_TABLE} ---")
            if args.resume_id.strip(): logger.warning("--resume-id is ignored when --temp-mode is active.")
            if args.limit is not None: logger.warning("--limit is ignored when --temp-mode is active.")
            if args.created_before: logger.warning("--created-before is ignored when --temp-mode is active.")
            if args.created_after: logger.warning("--created-after is ignored when --temp-mode is active.")

        if resume_id_arg: logger.info(f"Resuming batch processing from candidate with contact_id > {resume_id_arg}")
        if created_before_arg: logger.info(f"Filtering batch processing for candidates created before {created_before_arg}")
        if created_after_arg: logger.info(f"Filtering batch processing for candidates created on or after {created_after_arg}")
        if args.dry_run:
            logger.info("Executing batch processing in DRY-RUN mode. No data will be inserted into the database.")
        else:
            logger.info("Executing batch processing in LIVE mode. Embeddings will be inserted into the database.")

        process_candidate_data(
            logger,
            mode=processing_mode,
            limit=limit_arg,
            dry_run=args.dry_run,
            resume_id=resume_id_arg,
            temp_mode=args.temp_mode,
            created_before_date=created_before_arg,
            created_after_date=created_after_arg
        )