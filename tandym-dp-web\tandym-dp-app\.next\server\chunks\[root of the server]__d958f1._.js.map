{"version": 3, "sources": [], "sections": [{"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/api/config.ts"], "sourcesContent": ["const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || \"http://0.0.0.0:8005\";\r\nconst PORTAL_SERVICE_BASE_URL =\r\n  process.env.DP_PORTAL_SERVICE || \"http://0.0.0.0:8006\";\r\n// Ensure this is the correct base URL for your experiment APIs,\r\n// the example uses localhost:8006, so adjust if necessary.\r\n// For this example, I will use the user-provided localhost:8006\r\nexport const EXPERIMENT_BASE_URL =\r\n  process.env.NEXT_PUBLIC_BASE_URL || \"http://localhost:8005\";\r\n\r\nexport const isADLogin = (): boolean =>\r\n  process.env.NEXT_PUBLIC_AD_LOGIN === \"true\";\r\n\r\nexport const isParsedResume =\r\n  process.env.NEXT_PUBLIC_PARSE_RESUME_FROM_MERCURY === \"true\";\r\n\r\nexport const IS_WHY_FIT_EDITABLE = process.env.NEXT_PUBLIC_IS_WHY_FIT_EDITABLE;\r\n\r\nexport const IS_LOCK_FEATURE_DISABLED =\r\n  process.env.NEXT_PUBLIC_IS_LOCK_FEATURE_DISABLED;\r\n\r\nexport const API_ENDPOINTS = {\r\n  categories: `${BASE_URL}/categories`,\r\n  subCategories: `${BASE_URL}/subcategories`,\r\n  subCategoriesPools: `${BASE_URL}/subcategory/pools`,\r\n  jobTitlesBySubCategory: `${BASE_URL}/jobtitles/:sub_category_id?limit=10000000`,\r\n  deleteAttribute: `${BASE_URL}/attribute/delete/:attribute_id`,\r\n  fetchAttributesBySubCategory: `${BASE_URL}/attributes/:sub_category_id?limit=10000000`,\r\n  fetchWeightsBySubCategory: `${BASE_URL}/weights/:sub_category_id`,\r\n  updateAttributeWeight: `${BASE_URL}/attributes/:sub_category_id/update`,\r\n  updateSubcategoryOfAttribute: `${BASE_URL}/attributes/:attribute_id/subcategory`,\r\n  updateAttributeApprovalStatus: `${BASE_URL}/attributes/:attribute_id/approval`,\r\n  candidatesData: `${BASE_URL}/candidates`,\r\n  updateCandidatesReviewData: `${BASE_URL}/candidates/update_in_db`,\r\n  jobsData: `${BASE_URL}/jobs`,\r\n  getVacancies: `${BASE_URL}/vacancies`,\r\n  getVacanciesFromFiles: `${BASE_URL}/files/vacancies`,\r\n  getCandidatesByVacancyId: `${BASE_URL}/candidates/:vacancy_id`,\r\n  getResumeByContactId: `${BASE_URL}/resume/:contact_id`,\r\n  getResumeFromFileByContactId: `${BASE_URL}/files/candidate-resume/:contact_id`,\r\n  getAllSubcategoryWeightConfigs: `${BASE_URL}/v1/subcategory/weight-configs`,\r\n  updateSubcategoryWeightConfig: `${BASE_URL}/v1/subcategory/weight-configs/:subcategory_id`,\r\n  getCandidateStats: `${BASE_URL}/api/candidate-stats`,\r\n  getEntitlements: `${PORTAL_SERVICE_BASE_URL}/api/entitlement`,\r\n  getVacancyConfigAndStatus: `${BASE_URL}/vacancies/:vacancy_id/status`,\r\n  startVacancyReview: `${BASE_URL}/vacancies/:vacancy_id/start`,\r\n  completeVacancyReview: `${BASE_URL}/vacancies/:vacancy_id/complete`,\r\n  updateWhyFitData: `${BASE_URL}/candidates/fitness_reason`,\r\n  regenerateCatalystMatch: `${BASE_URL}/vacancies/:vacancy_id/regenerate-catalyst-match`,\r\n\r\n  // New Experiment Endpoints\r\n  experimentGetVacancies: `${EXPERIMENT_BASE_URL}/experiment/vacancies`,\r\n  experimentGetVacancyRunDetails: `${EXPERIMENT_BASE_URL}/experiment/vacancies/:vacancy_id/runs/:run_id/details`,\r\n  experimentGetCandidatesForVacancyRun: `${EXPERIMENT_BASE_URL}/experiment/vacancies/:vacancy_id/runs/:run_id/candidates`,\r\n  experimentGetRunConfig: `${EXPERIMENT_BASE_URL}/experiment/runs/:run_id/config`,\r\n  experimentGetCandidateResume: `${EXPERIMENT_BASE_URL}/experiment/candidates/:contact_id/resume`,\r\n  experimentArchiveVacancy: `${EXPERIMENT_BASE_URL}/experiment/vacancies/archive`, // Added new endpoint\r\n  experimentPromoteResults: `${EXPERIMENT_BASE_URL}/experiment/results/promote`, // + New endpoint for promoting results\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;AAAA,MAAM,WAAW,2DAAoC;AACrD,MAAM,0BACJ,QAAQ,GAAG,CAAC,iBAAiB,IAAI;AAI5B,MAAM,sBACX,2DAAoC;AAE/B,MAAM,YAAY,IACvB,6CAAqC;AAEhC,MAAM,iBACX,6CAAsD;AAEjD,MAAM;AAEN,MAAM;AAGN,MAAM,gBAAgB;IAC3B,YAAY,GAAG,SAAS,WAAW,CAAC;IACpC,eAAe,GAAG,SAAS,cAAc,CAAC;IAC1C,oBAAoB,GAAG,SAAS,kBAAkB,CAAC;IACnD,wBAAwB,GAAG,SAAS,0CAA0C,CAAC;IAC/E,iBAAiB,GAAG,SAAS,+BAA+B,CAAC;IAC7D,8BAA8B,GAAG,SAAS,2CAA2C,CAAC;IACtF,2BAA2B,GAAG,SAAS,yBAAyB,CAAC;IACjE,uBAAuB,GAAG,SAAS,mCAAmC,CAAC;IACvE,8BAA8B,GAAG,SAAS,qCAAqC,CAAC;IAChF,+BAA+B,GAAG,SAAS,kCAAkC,CAAC;IAC9E,gBAAgB,GAAG,SAAS,WAAW,CAAC;IACxC,4BAA4B,GAAG,SAAS,wBAAwB,CAAC;IACjE,UAAU,GAAG,SAAS,KAAK,CAAC;IAC5B,cAAc,GAAG,SAAS,UAAU,CAAC;IACrC,uBAAuB,GAAG,SAAS,gBAAgB,CAAC;IACpD,0BAA0B,GAAG,SAAS,uBAAuB,CAAC;IAC9D,sBAAsB,GAAG,SAAS,mBAAmB,CAAC;IACtD,8BAA8B,GAAG,SAAS,mCAAmC,CAAC;IAC9E,gCAAgC,GAAG,SAAS,8BAA8B,CAAC;IAC3E,+BAA+B,GAAG,SAAS,8CAA8C,CAAC;IAC1F,mBAAmB,GAAG,SAAS,oBAAoB,CAAC;IACpD,iBAAiB,GAAG,wBAAwB,gBAAgB,CAAC;IAC7D,2BAA2B,GAAG,SAAS,6BAA6B,CAAC;IACrE,oBAAoB,GAAG,SAAS,4BAA4B,CAAC;IAC7D,uBAAuB,GAAG,SAAS,+BAA+B,CAAC;IACnE,kBAAkB,GAAG,SAAS,0BAA0B,CAAC;IACzD,yBAAyB,GAAG,SAAS,gDAAgD,CAAC;IAEtF,2BAA2B;IAC3B,wBAAwB,GAAG,oBAAoB,qBAAqB,CAAC;IACrE,gCAAgC,GAAG,oBAAoB,sDAAsD,CAAC;IAC9G,sCAAsC,GAAG,oBAAoB,yDAAyD,CAAC;IACvH,wBAAwB,GAAG,oBAAoB,+BAA+B,CAAC;IAC/E,8BAA8B,GAAG,oBAAoB,yCAAyC,CAAC;IAC/F,0BAA0B,GAAG,oBAAoB,6BAA6B,CAAC;IAC/E,0BAA0B,GAAG,oBAAoB,2BAA2B,CAAC;AAC/E"}}, {"offset": {"line": 99, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 201, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/api/post.ts"], "sourcesContent": ["import axios from \"axios\";\r\n\r\nexport const postData = async (url: string, data: any) => {\r\n  try {\r\n    const response = await axios(url, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      data,\r\n    });\r\n    return response;\r\n  } catch (error: any) {\r\n    throw new Error(error.message);\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,WAAW,OAAO,KAAa;IAC1C,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,uIAAA,CAAA,UAAK,AAAD,EAAE,KAAK;YAChC,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA;QACF;QACA,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,MAAM,IAAI,MAAM,MAAM,OAAO;IAC/B;AACF"}}, {"offset": {"line": 220, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 226, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/api/put.ts"], "sourcesContent": ["import axios from \"axios\";\r\n\r\nexport const updateData = async (url: string, data: any) => {\r\n  try {\r\n    const response = await axios(url, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      data,\r\n    });\r\n    return response;\r\n  } catch (error: any) {\r\n    throw new Error(error.message);\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,aAAa,OAAO,KAAa;IAC5C,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,uIAAA,CAAA,UAAK,AAAD,EAAE,KAAK;YAChC,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA;QACF;QACA,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,MAAM,IAAI,MAAM,MAAM,OAAO;IAC/B;AACF"}}, {"offset": {"line": 245, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 251, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/api/serverActions.ts"], "sourcesContent": ["// api/serverActions.ts\r\n\"use server\";\r\nimport { API_ENDPOINTS } from \"./config\";\r\nimport { getData } from \"./get\";\r\nimport { postData } from \"./post\";\r\nimport { updateData } from \"./put\";\r\nimport { EntitlementResponse } from \"./types\";\r\n\r\nexport async function fetchAllCategories() {\r\n  try {\r\n    const response = await fetch(API_ENDPOINTS.categories);\r\n    const data = await response.json();\r\n    return data?.categories || [];\r\n  } catch (error) {\r\n    console.error(\"Error fetching subcategories:\", error);\r\n    return [];\r\n  }\r\n}\r\n\r\nexport async function fetchAllSubCategories() {\r\n  try {\r\n    const response = await fetch(API_ENDPOINTS.subCategories);\r\n    const data = await response.json();\r\n    return data?.subcategories || [];\r\n  } catch (error) {\r\n    console.error(\"Error fetching subcategories:\", error);\r\n    return [];\r\n  }\r\n}\r\n\r\nexport async function fetchJobTitlesBySubCategory(subCategoryId: number) {\r\n  try {\r\n    const response = await fetch(\r\n      API_ENDPOINTS.jobTitlesBySubCategory.replace(\r\n        \":sub_category_id\",\r\n        subCategoryId.toString()\r\n      )\r\n    );\r\n    const data = await response.json();\r\n    return data?.job_titles || [];\r\n  } catch (error) {\r\n    console.error(\"Error fetching job titles:\", error);\r\n    return [];\r\n  }\r\n}\r\n\r\nexport async function deleteAttributeTitleById(attributeId: number) {\r\n  try {\r\n    const response = await fetch(\r\n      API_ENDPOINTS.deleteAttribute.replace(\r\n        \":attribute_id\",\r\n        attributeId.toString()\r\n      ),\r\n      { method: \"DELETE\" }\r\n    );\r\n    return response.ok;\r\n  } catch (error) {\r\n    console.error(\"Error deleting job title:\", error);\r\n    return false;\r\n  }\r\n}\r\n\r\nexport async function fetchAttributeBySubcategoryId(subCategoryId: number) {\r\n  try {\r\n    const response = await fetch(\r\n      API_ENDPOINTS.fetchAttributesBySubCategory.replace(\r\n        \":sub_category_id\",\r\n        subCategoryId.toString()\r\n      )\r\n    );\r\n    const data = await response.json();\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error deleting job title:\", error);\r\n    return false;\r\n  }\r\n}\r\n\r\nexport async function updateAttributeWeight(\r\n  subCategoryId: number,\r\n  updatedData: any\r\n) {\r\n  const url = API_ENDPOINTS.updateAttributeWeight.replace(\r\n    \":sub_category_id\",\r\n    subCategoryId.toString()\r\n  );\r\n\r\n  try {\r\n    const response = await postData(url, updatedData);\r\n    return response; // Return the API response\r\n  } catch (error) {\r\n    console.error(\"Error updating attribute weight:\", error);\r\n    throw new Error(\"Failed to update attribute weight\");\r\n  }\r\n}\r\n\r\nexport async function updateSubcategoryOfAttribute(\r\n  attributeId: number,\r\n  data: { new_subcategory_id: number }\r\n) {\r\n  const url = API_ENDPOINTS.updateSubcategoryOfAttribute.replace(\r\n    \":attribute_id\",\r\n    attributeId.toString()\r\n  );\r\n\r\n  try {\r\n    const response = await updateData(url, data);\r\n    return response;\r\n  } catch (error) {\r\n    throw new Error(\"Failed to update attribute subcategory\");\r\n  }\r\n}\r\n\r\nexport async function updateAttributeApprovalStatus(\r\n  attributeId: number,\r\n  data: { is_approved: boolean }\r\n) {\r\n  const url = API_ENDPOINTS.updateAttributeApprovalStatus.replace(\r\n    \":attribute_id\",\r\n    attributeId.toString()\r\n  );\r\n\r\n  try {\r\n    const response = await updateData(url, data);\r\n    return response; // Return the API response\r\n  } catch (error) {\r\n    console.error(\"Error updating attribute approval status:\", error);\r\n    throw new Error(\"Failed to update attribute approval status\");\r\n  }\r\n}\r\n\r\nexport async function fetchWeightsBySubcategoryId(subCategoryId: number) {\r\n  try {\r\n    const response = await fetch(\r\n      API_ENDPOINTS.fetchWeightsBySubCategory.replace(\r\n        \":sub_category_id\",\r\n        subCategoryId.toString()\r\n      )\r\n    );\r\n    const data = await response.json();\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error deleting job title:\", error);\r\n    return false;\r\n  }\r\n}\r\n\r\nexport async function fetchVacancies() {\r\n  try {\r\n    const response = await fetch(API_ENDPOINTS.getVacancies);\r\n    const data = await response.json();\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error deleting job title:\", error);\r\n    return false;\r\n  }\r\n}\r\n\r\nexport async function fetchVacanciesFromFiles() {\r\n  try {\r\n    const response = await fetch(API_ENDPOINTS.getVacanciesFromFiles);\r\n    const data = await response.json();\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error deleting job title:\", error);\r\n    return false;\r\n  }\r\n}\r\n\r\nexport async function fetchCandidatesByVacancyId(vacancyId: string) {\r\n  try {\r\n    const response = await fetch(\r\n      API_ENDPOINTS.getCandidatesByVacancyId.replace(\r\n        \":vacancy_id\",\r\n        vacancyId.toString()\r\n      )\r\n    );\r\n    const data = await response.json();\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error deleting job title:\", error);\r\n    return false;\r\n  }\r\n}\r\n\r\nexport async function fetchResumeByCandidateId(contactId: string) {\r\n  try {\r\n    const response = await fetch(\r\n      API_ENDPOINTS.getResumeByContactId.replace(\r\n        \":contact_id\",\r\n        contactId.toString()\r\n      )\r\n    );\r\n    const data = await response.json();\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error while fetching resume:\", error);\r\n    return false;\r\n  }\r\n}\r\n\r\nexport async function fetchResumeByCandidateIdFromFiles(contactId: string) {\r\n  try {\r\n    const response = await fetch(\r\n      API_ENDPOINTS.getResumeFromFileByContactId.replace(\r\n        \":contact_id\",\r\n        contactId.toString()\r\n      )\r\n    );\r\n    const data = await response.json();\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error while fetching resume:\", error);\r\n    return false;\r\n  }\r\n}\r\n\r\ninterface RecruiterPostReview {\r\n  hiring_decision: string;\r\n  review_message: string;\r\n  candidate_contact_id: string;\r\n  recruiter_email: string;\r\n}\r\n\r\nexport async function updateRecruiterReview(data: any) {\r\n  const url = API_ENDPOINTS.updateCandidatesReviewData;\r\n  console.log(\"updateRecruiterReview data::\", data);\r\n  try {\r\n    const response = await postData(url, data);\r\n    return response; // Return the API response\r\n  } catch (error) {\r\n    console.error(\"Error updating attribute weight:\", error);\r\n    throw new Error(\"Failed to update attribute weight\");\r\n  }\r\n}\r\n\r\nexport async function getVacancyConfigAndStatus(vacancyId: string) {\r\n  try {\r\n    const response = await fetch(\r\n      API_ENDPOINTS.getVacancyConfigAndStatus.replace(\r\n        \":vacancy_id\",\r\n        vacancyId.toString()\r\n      )\r\n    );\r\n    const data = await response.json();\r\n    console.log(\"data::\", data);\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error while fetching vacancy status:\", error);\r\n    return false;\r\n  }\r\n}\r\n\r\nexport async function startVacancyreview(\r\n  vacancyId: string,\r\n  payload: { reviewer: string }\r\n) {\r\n  try {\r\n    const url = API_ENDPOINTS.startVacancyReview.replace(\r\n      \":vacancy_id\",\r\n      vacancyId.toString()\r\n    );\r\n    const response = await postData(url, payload);\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Error while start vacancy review:\", error);\r\n    return false;\r\n  }\r\n}\r\n\r\nexport async function completeVacancyreview(\r\n  vacancyId: string,\r\n  payload: { reviewer: string }\r\n) {\r\n  try {\r\n    const url = API_ENDPOINTS.completeVacancyReview.replace(\r\n      \":vacancy_id\",\r\n      vacancyId.toString()\r\n    );\r\n    const response = await postData(url, payload);\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Error while start vacancy review:\", error);\r\n    return false;\r\n  }\r\n}\r\n\r\nexport async function updateWhyFit(data: any) {\r\n  try {\r\n    const url = API_ENDPOINTS.updateWhyFitData;\r\n    const response = await postData(url, data);\r\n    return response;\r\n  } catch (error) {\r\n    console.log(\"Error while updating whyfit: \", error);\r\n    return false;\r\n  }\r\n}\r\nexport async function fetchEntitlements(\r\n  email_id: string\r\n): Promise<EntitlementResponse | false> {\r\n  try {\r\n    const isEntitlementEnabled = process.env.IS_ENTITLEMENT_ENABLED === \"true\";\r\n    if (!isEntitlementEnabled) {\r\n      return {\r\n        error: false,\r\n        code: \"TR_01\",\r\n        message: \"Successful\",\r\n        entitlement: {\r\n          Work_force_Index: true,\r\n          Sub_Catregory: true,\r\n          Vacancy: true,\r\n          Search_Match: true,\r\n          Sc_Score_Config: true,\r\n        },\r\n      };\r\n    }\r\n    const portal_name = \"recruiter\";\r\n    const url = `${API_ENDPOINTS.getEntitlements}?email_id=${encodeURIComponent(\r\n      email_id\r\n    )}&portal_name=${encodeURIComponent(portal_name)}`;\r\n    const response = await fetch(url);\r\n    if (!response.ok) {\r\n      throw new Error(`Failed to fetch entitlements: ${response.statusText}`);\r\n    }\r\n\r\n    const data: EntitlementResponse = await response.json();\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error fetching entitlement data:\", error);\r\n    return false;\r\n  }\r\n}\r\nexport async function getAllSubcategoryWeightConfigs() {\r\n  try {\r\n    const response = await fetch(API_ENDPOINTS.getAllSubcategoryWeightConfigs);\r\n    const data = await response.json();\r\n    return data?.subcategory_weight_configs || [];\r\n  } catch (error) {\r\n    console.error(\"Error fetching subcategories:\", error);\r\n    return [];\r\n  }\r\n}\r\n\r\nexport async function updateSubcategoryWeightConfig(\r\n  subcategoryId: number,\r\n  data: any\r\n) {\r\n  const url = API_ENDPOINTS.updateSubcategoryWeightConfig.replace(\r\n    \":subcategory_id\",\r\n    subcategoryId.toString()\r\n  );\r\n\r\n  try {\r\n    const response = await updateData(url, data);\r\n    return response;\r\n  } catch (error) {\r\n    throw new Error(\"Failed to update SubcategoryWeightConfig\");\r\n  }\r\n}\r\n\r\nexport async function regenerateCatalystMatch (vacancy_id: string, data: any) {\r\n  const url = API_ENDPOINTS.regenerateCatalystMatch.replace(\":vacancy_id\", vacancy_id);\r\n  try {\r\n    const response = await postData(url, data);\r\n    return response;\r\n  } catch (error) {\r\n    throw new Error(\"Failed to update SubcategoryWeightConfig\");\r\n  }\r\n} "], "names": [], "mappings": "AAAA,uBAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEvB;AAEA;AACA;;;;;;;AAGO,eAAe,uCAAgB,GAAhB;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,+GAAA,CAAA,gBAAa,CAAC,UAAU;QACrD,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,MAAM,cAAc,EAAE;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,EAAE;IACX;AACF;AAEO,eAAe,uCAAmB,GAAnB;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,+GAAA,CAAA,gBAAa,CAAC,aAAa;QACxD,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,MAAM,iBAAiB,EAAE;IAClC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,EAAE;IACX;AACF;AAEO,eAAe,uCAAyB,GAAzB,4BAA4B,aAAqB;IACrE,IAAI;QACF,MAAM,WAAW,MAAM,MACrB,+GAAA,CAAA,gBAAa,CAAC,sBAAsB,CAAC,OAAO,CAC1C,oBACA,cAAc,QAAQ;QAG1B,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,MAAM,cAAc,EAAE;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO,EAAE;IACX;AACF;AAEO,eAAe,uCAAsB,GAAtB,yBAAyB,WAAmB;IAChE,IAAI;QACF,MAAM,WAAW,MAAM,MACrB,+GAAA,CAAA,gBAAa,CAAC,eAAe,CAAC,OAAO,CACnC,iBACA,YAAY,QAAQ,KAEtB;YAAE,QAAQ;QAAS;QAErB,OAAO,SAAS,EAAE;IACpB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;IACT;AACF;AAEO,eAAe,uCAA2B,GAA3B,8BAA8B,aAAqB;IACvE,IAAI;QACF,MAAM,WAAW,MAAM,MACrB,+GAAA,CAAA,gBAAa,CAAC,4BAA4B,CAAC,OAAO,CAChD,oBACA,cAAc,QAAQ;QAG1B,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;IACT;AACF;AAEO,eAAe,uCAAmB,GAAnB,sBACpB,aAAqB,EACrB,WAAgB;IAEhB,MAAM,MAAM,+GAAA,CAAA,gBAAa,CAAC,qBAAqB,CAAC,OAAO,CACrD,oBACA,cAAc,QAAQ;IAGxB,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,6GAAA,CAAA,WAAQ,AAAD,EAAE,KAAK;QACrC,OAAO,UAAU,0BAA0B;IAC7C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,MAAM,IAAI,MAAM;IAClB;AACF;AAEO,eAAe,uCAA0B,GAA1B,6BACpB,WAAmB,EACnB,IAAoC;IAEpC,MAAM,MAAM,+GAAA,CAAA,gBAAa,CAAC,4BAA4B,CAAC,OAAO,CAC5D,iBACA,YAAY,QAAQ;IAGtB,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,4GAAA,CAAA,aAAU,AAAD,EAAE,KAAK;QACvC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM;IAClB;AACF;AAEO,eAAe,uCAA2B,GAA3B,8BACpB,WAAmB,EACnB,IAA8B;IAE9B,MAAM,MAAM,+GAAA,CAAA,gBAAa,CAAC,6BAA6B,CAAC,OAAO,CAC7D,iBACA,YAAY,QAAQ;IAGtB,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,4GAAA,CAAA,aAAU,AAAD,EAAE,KAAK;QACvC,OAAO,UAAU,0BAA0B;IAC7C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6CAA6C;QAC3D,MAAM,IAAI,MAAM;IAClB;AACF;AAEO,eAAe,uCAAyB,GAAzB,4BAA4B,aAAqB;IACrE,IAAI;QACF,MAAM,WAAW,MAAM,MACrB,+GAAA,CAAA,gBAAa,CAAC,yBAAyB,CAAC,OAAO,CAC7C,oBACA,cAAc,QAAQ;QAG1B,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;IACT;AACF;AAEO,eAAe,uCAAY,GAAZ;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,+GAAA,CAAA,gBAAa,CAAC,YAAY;QACvD,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;IACT;AACF;AAEO,eAAe,uCAAqB,GAArB;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,+GAAA,CAAA,gBAAa,CAAC,qBAAqB;QAChE,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;IACT;AACF;AAEO,eAAe,uCAAwB,GAAxB,2BAA2B,SAAiB;IAChE,IAAI;QACF,MAAM,WAAW,MAAM,MACrB,+GAAA,CAAA,gBAAa,CAAC,wBAAwB,CAAC,OAAO,CAC5C,eACA,UAAU,QAAQ;QAGtB,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;IACT;AACF;AAEO,eAAe,uCAAsB,GAAtB,yBAAyB,SAAiB;IAC9D,IAAI;QACF,MAAM,WAAW,MAAM,MACrB,+GAAA,CAAA,gBAAa,CAAC,oBAAoB,CAAC,OAAO,CACxC,eACA,UAAU,QAAQ;QAGtB,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;IACT;AACF;AAEO,eAAe,uCAA+B,GAA/B,kCAAkC,SAAiB;IACvE,IAAI;QACF,MAAM,WAAW,MAAM,MACrB,+GAAA,CAAA,gBAAa,CAAC,4BAA4B,CAAC,OAAO,CAChD,eACA,UAAU,QAAQ;QAGtB,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;IACT;AACF;AASO,eAAe,uCAAmB,GAAnB,sBAAsB,IAAS;IACnD,MAAM,MAAM,+GAAA,CAAA,gBAAa,CAAC,0BAA0B;IACpD,QAAQ,GAAG,CAAC,gCAAgC;IAC5C,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,6GAAA,CAAA,WAAQ,AAAD,EAAE,KAAK;QACrC,OAAO,UAAU,0BAA0B;IAC7C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,MAAM,IAAI,MAAM;IAClB;AACF;AAEO,eAAe,uCAAuB,GAAvB,0BAA0B,SAAiB;IAC/D,IAAI;QACF,MAAM,WAAW,MAAM,MACrB,+GAAA,CAAA,gBAAa,CAAC,yBAAyB,CAAC,OAAO,CAC7C,eACA,UAAU,QAAQ;QAGtB,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,QAAQ,GAAG,CAAC,UAAU;QACtB,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wCAAwC;QACtD,OAAO;IACT;AACF;AAEO,eAAe,uCAAgB,GAAhB,mBACpB,SAAiB,EACjB,OAA6B;IAE7B,IAAI;QACF,MAAM,MAAM,+GAAA,CAAA,gBAAa,CAAC,kBAAkB,CAAC,OAAO,CAClD,eACA,UAAU,QAAQ;QAEpB,MAAM,WAAW,MAAM,CAAA,GAAA,6GAAA,CAAA,WAAQ,AAAD,EAAE,KAAK;QACrC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO;IACT;AACF;AAEO,eAAe,uCAAmB,GAAnB,sBACpB,SAAiB,EACjB,OAA6B;IAE7B,IAAI;QACF,MAAM,MAAM,+GAAA,CAAA,gBAAa,CAAC,qBAAqB,CAAC,OAAO,CACrD,eACA,UAAU,QAAQ;QAEpB,MAAM,WAAW,MAAM,CAAA,GAAA,6GAAA,CAAA,WAAQ,AAAD,EAAE,KAAK;QACrC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO;IACT;AACF;AAEO,eAAe,uCAAU,GAAV,aAAa,IAAS;IAC1C,IAAI;QACF,MAAM,MAAM,+GAAA,CAAA,gBAAa,CAAC,gBAAgB;QAC1C,MAAM,WAAW,MAAM,CAAA,GAAA,6GAAA,CAAA,WAAQ,AAAD,EAAE,KAAK;QACrC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,GAAG,CAAC,iCAAiC;QAC7C,OAAO;IACT;AACF;AACO,eAAe,uCAAe,GAAf,kBACpB,QAAgB;IAEhB,IAAI;QACF,MAAM,uBAAuB,QAAQ,GAAG,CAAC,sBAAsB,KAAK;QACpE,IAAI,CAAC,sBAAsB;YACzB,OAAO;gBACL,OAAO;gBACP,MAAM;gBACN,SAAS;gBACT,aAAa;oBACX,kBAAkB;oBAClB,eAAe;oBACf,SAAS;oBACT,cAAc;oBACd,iBAAiB;gBACnB;YACF;QACF;QACA,MAAM,cAAc;QACpB,MAAM,MAAM,GAAG,+GAAA,CAAA,gBAAa,CAAC,eAAe,CAAC,UAAU,EAAE,mBACvD,UACA,aAAa,EAAE,mBAAmB,cAAc;QAClD,MAAM,WAAW,MAAM,MAAM;QAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,8BAA8B,EAAE,SAAS,UAAU,EAAE;QACxE;QAEA,MAAM,OAA4B,MAAM,SAAS,IAAI;QACrD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;IACT;AACF;AACO,eAAe,uCAA4B,GAA5B;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,+GAAA,CAAA,gBAAa,CAAC,8BAA8B;QACzE,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,MAAM,8BAA8B,EAAE;IAC/C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,EAAE;IACX;AACF;AAEO,eAAe,uCAA2B,GAA3B,8BACpB,aAAqB,EACrB,IAAS;IAET,MAAM,MAAM,+GAAA,CAAA,gBAAa,CAAC,6BAA6B,CAAC,OAAO,CAC7D,mBACA,cAAc,QAAQ;IAGxB,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,4GAAA,CAAA,aAAU,AAAD,EAAE,KAAK;QACvC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM;IAClB;AACF;AAEO,eAAe,uCAAqB,GAArB,wBAAyB,UAAkB,EAAE,IAAS;IAC1E,MAAM,MAAM,+GAAA,CAAA,gBAAa,CAAC,uBAAuB,CAAC,OAAO,CAAC,eAAe;IACzE,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,6GAAA,CAAA,WAAQ,AAAD,EAAE,KAAK;QACrC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM;IAClB;AACF;;;IAxWsB;IAWA;IAWA;IAgBA;IAgBA;IAgBA;IAkBA;IAiBA;IAkBA;IAgBA;IAWA;IAWA;IAgBA;IAgBA;IAuBA;IAYA;IAiBA;IAiBA;IAiBA;IAUA;IAmCA;IAWA;IAiBA;;AAhWA,iPAAA;AAWA,iPAAA;AAWA,iPAAA;AAgBA,iPAAA;AAgBA,iPAAA;AAgBA,iPAAA;AAkBA,iPAAA;AAiBA,iPAAA;AAkBA,iPAAA;AAgBA,iPAAA;AAWA,iPAAA;AAWA,iPAAA;AAgBA,iPAAA;AAgBA,iPAAA;AAuBA,iPAAA;AAYA,iPAAA;AAiBA,iPAAA;AAiBA,iPAAA;AAiBA,iPAAA;AAUA,iPAAA;AAmCA,iPAAA;AAWA,iPAAA;AAiBA,iPAAA"}}, {"offset": {"line": 587, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 593, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/app/api/vacancies/%5Bid%5D/regenerate-catalyst-match/route.ts"], "sourcesContent": ["import { regenerateCatalystMatch } from \"@/api/serverActions\";\r\nimport { NextRequest } from \"next/server\";\r\n\r\nexport async function POST(\r\n  req: NextRequest,\r\n  { params }: { params: Promise<{ id: string }> }\r\n) {\r\n  const { id } = await params;\r\n  const body = await req.json();\r\n  const response = await regenerateCatalystMatch(id, body);\r\n  return Response.json(response);\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAGO,eAAe,KACpB,GAAgB,EAChB,EAAE,MAAM,EAAuC;IAE/C,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;IACrB,MAAM,OAAO,MAAM,IAAI,IAAI;IAC3B,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI;IACnD,OAAO,SAAS,IAAI,CAAC;AACvB"}}, {"offset": {"line": 604, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}