{"version": 3, "sources": [], "sections": [{"offset": {"line": 12, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/public/assets/noCandidates.svg.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 336, height: 252, blurDataURL: null, blurWidth: 0, blurHeight: 0 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,gIAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAAM,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 28, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 34, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/Loading.tsx"], "sourcesContent": ["import React from \"react\";\r\n\r\nconst Loading = ({ height }: { height?: string }) => {\r\n  return (\r\n    <div\r\n      className={`flex justify-center items-center ${\r\n        height ? height : \"h-[100vh]\"\r\n      } w-full`}\r\n    >\r\n      <svg\r\n        className=\"ml-2 mt-0.5 size-10 animate-spin\"\r\n        xmlns=\"http://www.w3.org/2000/svg\"\r\n        fill=\"none\"\r\n        viewBox=\"0 0 24 24\"\r\n      >\r\n        <circle\r\n          className=\"opacity-25\"\r\n          cx=\"12\"\r\n          cy=\"12\"\r\n          r=\"10\"\r\n          stroke=\"currentColor\"\r\n          strokeWidth=\"4\"\r\n        ></circle>\r\n        <path\r\n          className=\"opacity-75\"\r\n          fill=\"currentColor\"\r\n          d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\r\n        ></path>\r\n      </svg>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Loading;\r\n"], "names": [], "mappings": ";;;;;AAEA,MAAM,UAAU,CAAC,EAAE,MAAM,EAAuB;IAC9C,qBACE,6LAAC;QACC,WAAW,CAAC,iCAAiC,EAC3C,SAAS,SAAS,YACnB,OAAO,CAAC;kBAET,cAAA,6LAAC;YACC,WAAU;YACV,OAAM;YACN,MAAK;YACL,SAAQ;;8BAER,6LAAC;oBACC,WAAU;oBACV,IAAG;oBACH,IAAG;oBACH,GAAE;oBACF,QAAO;oBACP,aAAY;;;;;;8BAEd,6LAAC;oBACC,WAAU;oBACV,MAAK;oBACL,GAAE;;;;;;;;;;;;;;;;;AAKZ;KA7BM;uCA+BS"}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/ui/input.tsx"], "sourcesContent": ["import { cn } from \"@/library/utils\";\r\nimport * as React from \"react\";\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    );\r\n  }\r\n);\r\nInput.displayName = \"Input\";\r\n\r\nexport { Input };\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEA,MAAM,sBAAQ,8JAAM,UAAU,MAC5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2WACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG"}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/ui/button.tsx"], "sourcesContent": ["import * as React from 'react';\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport { cva, type VariantProps } from 'class-variance-authority';\r\n\r\nimport { cn } from '@/library/utils';\r\n\r\nconst buttonVariants = cva(\r\n  'inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0',\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          'bg-primaryButton text-primaryButton-foreground shadow hover:bg-primaryButton/90 active:bg-primaryButton/80',\r\n        destructive:\r\n          'bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90',\r\n        outline:\r\n          'border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground',\r\n        secondary:\r\n          'bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80',\r\n        ghost: 'hover:bg-accent hover:text-accent-foreground',\r\n        link: 'text-primary underline-offset-4 hover:underline',\r\n      },\r\n      size: {\r\n        default: 'h-9 px-4 py-2',\r\n        sm: 'h-8 rounded-md px-3 text-xs',\r\n        lg: 'h-10 rounded-md px-8',\r\n        icon: 'h-9 w-9',\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default',\r\n      size: 'default',\r\n    },\r\n  }\r\n);\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean;\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : 'button';\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    );\r\n  }\r\n);\r\nButton.displayName = 'Button';\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;AAEA;AAHA;;;;;;AAKA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,sRACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,8JAAM,UAAU,MAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG"}}, {"offset": {"line": 191, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 202, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/public/assets/info_outline_black.svg.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 16, height: 16, blurDataURL: null, blurWidth: 0, blurHeight: 0 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,sIAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAM,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 218, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 224, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\";\r\n\r\nimport { cn } from \"@/library/utils\";\r\n\r\nconst TooltipProvider = TooltipPrimitive.Provider;\r\n\r\nconst Tooltip = TooltipPrimitive.Root;\r\n\r\nconst TooltipTrigger = TooltipPrimitive.Trigger;\r\n\r\nconst TooltipContent = React.forwardRef<\r\n  React.ElementRef<typeof TooltipPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>\r\n>(({ className, sideOffset = 4, ...props }, ref) => (\r\n  <TooltipPrimitive.Portal>\r\n    <TooltipPrimitive.Content\r\n      ref={ref}\r\n      sideOffset={sideOffset}\r\n      className={cn(\r\n        \"z-50 overflow-hidden rounded-md bg-primary px-3 py-1.5 text-xs text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  </TooltipPrimitive.Portal>\r\n));\r\nTooltipContent.displayName = TooltipPrimitive.Content.displayName;\r\n\r\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider };\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AAGA;AAFA;AAHA;;;;;AAOA,MAAM,kBAAkB,uKAAiB,QAAQ;AAEjD,MAAM,UAAU,uKAAiB,IAAI;AAErC,MAAM,iBAAiB,uKAAiB,OAAO;AAE/C,MAAM,+BAAiB,8JAAM,UAAU,MAGrC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,6LAAC,uKAAiB,MAAM;kBACtB,cAAA,6LAAC,uKAAiB,OAAO;YACvB,KAAK;YACL,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qXACA;YAED,GAAG,KAAK;;;;;;;;;;;;AAIf,eAAe,WAAW,GAAG,uKAAiB,OAAO,CAAC,WAAW"}}, {"offset": {"line": 267, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 273, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/AppToolTip.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport Image from \"next/image\";\r\nimport Info from \"@/public/assets/info_outline_black.svg\";\r\n\r\nimport { cn } from \"@/library/utils\";\r\n\r\nimport {\r\n  Tooltip,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from \"./ui/tooltip\";\r\n\r\ninterface AppToolTipProps {\r\n  text: React.ReactNode;\r\n  direction?: \"bottom\" | \"top\" | \"left\" | \"right\" | undefined;\r\n  header?: React.ReactNode;\r\n  className?: string;\r\n  triggerClassName?: string;\r\n  iconClass?: string;\r\n}\r\n\r\nconst AppToolTip = ({\r\n  text,\r\n  direction = \"left\",\r\n  header,\r\n  className,\r\n  triggerClassName,\r\n  iconClass,\r\n}: AppToolTipProps) => {\r\n  return (\r\n    <TooltipProvider delayDuration={0}>\r\n      <Tooltip>\r\n        <TooltipTrigger\r\n          className={cn(`flex min-w-4 items-center`, triggerClassName)}\r\n        >\r\n          {header ? (\r\n            header\r\n          ) : (\r\n            <Image\r\n              id=\"info-button\"\r\n              className={`custom-target-icon ml-1 hidden size-3.5 sm:inline ${iconClass}`}\r\n              src={Info}\r\n              alt=\"c\"\r\n            />\r\n          )}\r\n        </TooltipTrigger>\r\n        <TooltipContent\r\n          className={cn(\" bg-[#646464] text-white\", className)}\r\n          side={direction}\r\n        >\r\n          {text}\r\n        </TooltipContent>\r\n      </Tooltip>\r\n    </TooltipProvider>\r\n  );\r\n};\r\n\r\nexport default AppToolTip;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAEA;AAEA;;;;;;AAgBA,MAAM,aAAa,CAAC,EAClB,IAAI,EACJ,YAAY,MAAM,EAClB,MAAM,EACN,SAAS,EACT,gBAAgB,EAChB,SAAS,EACO;IAChB,qBACE,6LAAC,+HAAA,CAAA,kBAAe;QAAC,eAAe;kBAC9B,cAAA,6LAAC,+HAAA,CAAA,UAAO;;8BACN,6LAAC,+HAAA,CAAA,iBAAc;oBACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,CAAC,yBAAyB,CAAC,EAAE;8BAE1C,SACC,uBAEA,6LAAC,gIAAA,CAAA,UAAK;wBACJ,IAAG;wBACH,WAAW,CAAC,kDAAkD,EAAE,WAAW;wBAC3E,KAAK,sTAAA,CAAA,UAAI;wBACT,KAAI;;;;;;;;;;;8BAIV,6LAAC,+HAAA,CAAA,iBAAc;oBACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;oBAC1C,MAAM;8BAEL;;;;;;;;;;;;;;;;;AAKX;KAlCM;uCAoCS"}}, {"offset": {"line": 336, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 342, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/Modal.tsx"], "sourcesContent": ["import { ReactNode } from \"react\";\r\n\r\ninterface ModalProps extends React.HTMLAttributes<HTMLDivElement> {\r\n  title?: string;\r\n  isOpen: boolean;\r\n  children: ReactNode;\r\n  onClose: () => void;\r\n  width?: string;\r\n  height?: string;\r\n  mercury?: boolean;\r\n}\r\n\r\nconst Modal = ({\r\n  isOpen,\r\n  title,\r\n  children,\r\n  onClose,\r\n  width = \"max-w-md\",\r\n  height,\r\n  mercury = false,\r\n  ...rest\r\n}: ModalProps) => {\r\n  if (!isOpen) return <></>;\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black bg-opacity-20 flex justify-center items-center z-50\">\r\n      <div\r\n        className={`bg-white rounded-lg shadow-lg w-[90%] ${width} ${height} p-6 relative`}\r\n        {...rest}\r\n      >\r\n        {/* Header */}\r\n        {title ? (\r\n          <div className=\"flex justify-between items-center border-b pb-2 mb-4\">\r\n            <h2 className=\"text-xl font-semibold\">{title}</h2>\r\n            <button\r\n              onClick={onClose}\r\n              className=\"text-gray-500 hover:text-gray-700\"\r\n            >\r\n              ✖\r\n            </button>\r\n          </div>\r\n        ) : null}\r\n\r\n        {/* Content */}\r\n        <div className={mercury ? \"p-4 w-[100%] h-[100%]\" : undefined}>\r\n          {children}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Modal;\r\n"], "names": [], "mappings": ";;;;;AAYA,MAAM,QAAQ,CAAC,EACb,MAAM,EACN,KAAK,EACL,QAAQ,EACR,OAAO,EACP,QAAQ,UAAU,EAClB,MAAM,EACN,UAAU,KAAK,EACf,GAAG,MACQ;IACX,IAAI,CAAC,QAAQ,qBAAO;IACpB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YACC,WAAW,CAAC,sCAAsC,EAAE,MAAM,CAAC,EAAE,OAAO,aAAa,CAAC;YACjF,GAAG,IAAI;;gBAGP,sBACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAyB;;;;;;sCACvC,6LAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;2BAID;8BAGJ,6LAAC;oBAAI,WAAW,UAAU,0BAA0B;8BACjD;;;;;;;;;;;;;;;;;AAKX;KArCM;uCAuCS"}}, {"offset": {"line": 408, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 414, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\";\r\nimport { cva, type VariantProps } from \"class-variance-authority\";\r\nimport { cn } from \"@/library/utils\";\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80\",\r\n        outline: \"text-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n);\r\n\r\nexport interface BadgeProps\r\n  extends React.HTMLAttributes<HTMLDivElement>,\r\n    VariantProps<typeof badgeVariants> {}\r\n\r\nfunction Badge({ className, variant, ...props }: BadgeProps) {\r\n  return (\r\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\r\n  );\r\n}\r\n\r\nexport { Badge, badgeVariants };\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,wKACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS"}}, {"offset": {"line": 456, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 462, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/candidates/VacancyDetails.tsx"], "sourcesContent": ["import { X } from \"lucide-react\";\r\nimport React from \"react\";\r\nimport { Vacancy } from \"../CandidateTable/helper\";\r\n\r\nconst SkillBadge = ({ skill, weight }: { skill: string; weight: string }) => {\r\n  const getBadgeStyle = (weight: string) => {\r\n    switch (weight) {\r\n      case \"high\":\r\n        return \"bg-green-200/50 text-green-800 border border-green-300 shadow-md\";\r\n      case \"medium\":\r\n        return \"bg-blue-200/50 text-blue-800 border border-blue-300 shadow-md\";\r\n      case \"normal\":\r\n        return \"bg-yellow-200/50 text-yellow-800 border border-yellow-300 shadow-md\";\r\n      case \"low\":\r\n        return \"bg-red-200/50 text-red-800 border border-red-300 shadow-md\";\r\n      default:\r\n        return \"bg-gray-200 text-gray-700 border border-gray-300\";\r\n    }\r\n  };\r\n\r\n  return (\r\n    <span\r\n      className={`px-2 py-0.5 rounded-full text-xs font-semibold flex items-center gap-1 ${getBadgeStyle(\r\n        weight\r\n      )}`}\r\n    >\r\n      <span className=\"text-[12px] capitalize\">{skill}</span>\r\n      <span className=\"text-[10px] opacity-80 font-semibold\">({weight})</span>\r\n    </span>\r\n  );\r\n};\r\n\r\ninterface VacancyDetailsProps {\r\n  vacancy: Vacancy | null;\r\n  setActiveVacancy: (vacancy: Vacancy | null) => void;\r\n  mercuryPortal?: boolean;\r\n}\r\n\r\nexport default function VacancyDetails({\r\n  vacancy,\r\n  setActiveVacancy,\r\n  mercuryPortal,\r\n}: VacancyDetailsProps) {\r\n  return (\r\n    <div\r\n      onClick={(e: React.MouseEvent<HTMLElement>) => {\r\n        e.stopPropagation();\r\n      }}\r\n      className={`absolute w-[55%] left-[calc((100vw-900px)/2)] ${\r\n        mercuryPortal ? \"top-[5%]\" : \"top-[17%]\"\r\n      } bg-white shadow-lg border p-4 text-xs rounded-md z-[999] p-5`}\r\n    >\r\n      {/* Close Button */}\r\n      <button\r\n        className=\"absolute top-3.5 right-3 text-gray-600 hover:text-black\"\r\n        onClick={() => setActiveVacancy(null)}\r\n      >\r\n        <X size={18} />\r\n      </button>\r\n\r\n      <h3 className=\"text-base font-bold text-gray-800 mb-3 border-b pb-2\">\r\n        Vacancy Details\r\n      </h3>\r\n\r\n      <div className=\"max-h-[70vh] overflow-y-auto\">\r\n        <div className=\"mb-3\">\r\n          <p>\r\n            <strong className=\"text-gray-800 text-md\">Ref No:</strong>{\" \"}\r\n            <span className=\"text-gray-800\">{vacancy?.refno}</span>\r\n          </p>\r\n        </div>\r\n        <div className=\"mb-3\">\r\n          <p>\r\n            <strong className=\"text-gray-800 text-md\">Vancancy Id:</strong>{\" \"}\r\n            <span className=\"text-gray-800\">{vacancy?.vacancy_id}</span>\r\n          </p>\r\n        </div>\r\n\r\n        <div className=\"mb-3\">\r\n          <h4 className=\"text-lg font-semibold text-gray-800\">Job Title</h4>\r\n          <p className=\"text-gray-800\">\r\n            {vacancy?.vacancy_data?.[\"job title\"]?.join(\", \")}\r\n          </p>\r\n        </div>\r\n\r\n        <div className=\"mb-3\">\r\n          <h4 className=\"text-lg font-semibold text-gray-800\">Location</h4>\r\n          <p className=\"text-gray-800\">\r\n            {vacancy?.vacancy_data?.[\"joblocation\"]\r\n              ?.map((loc: any) =>\r\n                [loc.city, loc.state].filter(Boolean).join(\", \")\r\n              )\r\n              .join(\" | \")}\r\n          </p>\r\n        </div>\r\n\r\n        {/* Soft Skills */}\r\n        <div className=\"mb-3\">\r\n          <h4 className=\"text-lg font-semibold text-gray-800 mb-1\">\r\n            Soft Skills\r\n          </h4>\r\n          <div className=\"flex flex-wrap gap-2\">\r\n            {vacancy?.vacancy_data?.[\"soft skills\"].map((skill, index) => (\r\n              <SkillBadge\r\n                key={index}\r\n                skill={skill?.name}\r\n                weight={skill?.weight}\r\n              />\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Technical Skills */}\r\n        <div className=\"mb-3\">\r\n          <h4 className=\"text-lg font-semibold text-gray-800 mb-1\">\r\n            Technical Skills\r\n          </h4>\r\n          <div className=\"flex flex-wrap gap-2\">\r\n            {vacancy?.vacancy_data?.[\"technical skills\"].map((skill, index) => (\r\n              <SkillBadge\r\n                key={index}\r\n                skill={skill?.name}\r\n                weight={skill?.weight}\r\n              />\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Tools & Platforms */}\r\n        <div className=\"mb-3\">\r\n          <h4 className=\"text-lg font-semibold text-gray-800 mb-1\">\r\n            Tools & Platforms\r\n          </h4>\r\n          <div className=\"flex flex-wrap gap-2\">\r\n            {vacancy?.vacancy_data?.[\"tools and platforms\"].map(\r\n              (tool, index) => (\r\n                <SkillBadge\r\n                  key={index}\r\n                  skill={tool?.name}\r\n                  weight={tool?.weight}\r\n                />\r\n              )\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Degrees & Certifications */}\r\n        {vacancy?.vacancy_data?.[\"degrees and certifications\"]?.length ? (\r\n          <div className=\"mb-3\">\r\n            <h4 className=\"text-lg font-semibold text-gray-700 mb-3\">\r\n              Degrees & Certifications\r\n            </h4>\r\n            <ul className=\"text-gray-800 list-disc list-inside\">\r\n              {vacancy?.vacancy_data?.[\"degrees and certifications\"].map(\r\n                (degree, index) => (\r\n                  <li key={index} className=\"text-xs capitalize\">\r\n                    {degree?.name}\r\n                  </li>\r\n                )\r\n              )}\r\n            </ul>\r\n          </div>\r\n        ) : null}\r\n\r\n        {/* Years Of Experience */}\r\n        {vacancy?.vacancy_data?.[\"years of experience\"]?.length ? (\r\n          <div className=\"mb-3\">\r\n            <h4 className=\"text-lg font-semibold text-gray-700 mb-3\">\r\n              Relavant Years Of Experience\r\n            </h4>\r\n            <div className=\"flex flex-wrap gap-2\">\r\n              {vacancy?.vacancy_data?.[\"years of experience\"].map(\r\n                (exp, index) => (\r\n                  <SkillBadge\r\n                    key={index}\r\n                    skill={exp?.years}\r\n                    weight={exp?.weight}\r\n                  />\r\n                )\r\n              )}\r\n            </div>\r\n          </div>\r\n        ) : null}\r\n\r\n        {vacancy?.vacancy_data?.job_description ? (\r\n          <div className=\"mb-1\">\r\n            <h4 className=\"text-lg font-semibold text-gray-700 mb-1\">\r\n              Job Description\r\n            </h4>\r\n            <p\r\n              dangerouslySetInnerHTML={{\r\n                __html: vacancy?.vacancy_data?.job_description,\r\n              }}\r\n              className=\"text-gray-700 mt-1 mb-2\"\r\n            ></p>\r\n          </div>\r\n        ) : null}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAIA,MAAM,aAAa,CAAC,EAAE,KAAK,EAAE,MAAM,EAAqC;IACtE,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QACC,WAAW,CAAC,uEAAuE,EAAE,cACnF,SACC;;0BAEH,6LAAC;gBAAK,WAAU;0BAA0B;;;;;;0BAC1C,6LAAC;gBAAK,WAAU;;oBAAuC;oBAAE;oBAAO;;;;;;;;;;;;;AAGtE;KA1BM;AAkCS,SAAS,eAAe,EACrC,OAAO,EACP,gBAAgB,EAChB,aAAa,EACO;IACpB,qBACE,6LAAC;QACC,SAAS,CAAC;YACR,EAAE,eAAe;QACnB;QACA,WAAW,CAAC,8CAA8C,EACxD,gBAAgB,aAAa,YAC9B,6DAA6D,CAAC;;0BAG/D,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,iBAAiB;0BAEhC,cAAA,6LAAC,+LAAA,CAAA,IAAC;oBAAC,MAAM;;;;;;;;;;;0BAGX,6LAAC;gBAAG,WAAU;0BAAuD;;;;;;0BAIrE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;;8CACC,6LAAC;oCAAO,WAAU;8CAAwB;;;;;;gCAAiB;8CAC3D,6LAAC;oCAAK,WAAU;8CAAiB,SAAS;;;;;;;;;;;;;;;;;kCAG9C,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;;8CACC,6LAAC;oCAAO,WAAU;8CAAwB;;;;;;gCAAsB;8CAChE,6LAAC;oCAAK,WAAU;8CAAiB,SAAS;;;;;;;;;;;;;;;;;kCAI9C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,6LAAC;gCAAE,WAAU;0CACV,SAAS,cAAc,CAAC,YAAY,EAAE,KAAK;;;;;;;;;;;;kCAIhD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,6LAAC;gCAAE,WAAU;0CACV,SAAS,cAAc,CAAC,cAAc,EACnC,IAAI,CAAC,MACL;wCAAC,IAAI,IAAI;wCAAE,IAAI,KAAK;qCAAC,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,OAE5C,KAAK;;;;;;;;;;;;kCAKZ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAGzD,6LAAC;gCAAI,WAAU;0CACZ,SAAS,cAAc,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,sBAClD,6LAAC;wCAEC,OAAO,OAAO;wCACd,QAAQ,OAAO;uCAFV;;;;;;;;;;;;;;;;kCASb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAGzD,6LAAC;gCAAI,WAAU;0CACZ,SAAS,cAAc,CAAC,mBAAmB,CAAC,IAAI,CAAC,OAAO,sBACvD,6LAAC;wCAEC,OAAO,OAAO;wCACd,QAAQ,OAAO;uCAFV;;;;;;;;;;;;;;;;kCASb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAGzD,6LAAC;gCAAI,WAAU;0CACZ,SAAS,cAAc,CAAC,sBAAsB,CAAC,IAC9C,CAAC,MAAM,sBACL,6LAAC;wCAEC,OAAO,MAAM;wCACb,QAAQ,MAAM;uCAFT;;;;;;;;;;;;;;;;oBAUd,SAAS,cAAc,CAAC,6BAA6B,EAAE,uBACtD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAGzD,6LAAC;gCAAG,WAAU;0CACX,SAAS,cAAc,CAAC,6BAA6B,CAAC,IACrD,CAAC,QAAQ,sBACP,6LAAC;wCAAe,WAAU;kDACvB,QAAQ;uCADF;;;;;;;;;;;;;;;+BAOf;oBAGH,SAAS,cAAc,CAAC,sBAAsB,EAAE,uBAC/C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAGzD,6LAAC;gCAAI,WAAU;0CACZ,SAAS,cAAc,CAAC,sBAAsB,CAAC,IAC9C,CAAC,KAAK,sBACJ,6LAAC;wCAEC,OAAO,KAAK;wCACZ,QAAQ,KAAK;uCAFR;;;;;;;;;;;;;;;+BAQb;oBAEH,SAAS,cAAc,gCACtB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAGzD,6LAAC;gCACC,yBAAyB;oCACvB,QAAQ,SAAS,cAAc;gCACjC;gCACA,WAAU;;;;;;;;;;;+BAGZ;;;;;;;;;;;;;AAIZ;MAlKwB"}}, {"offset": {"line": 872, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 878, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/api/config.ts"], "sourcesContent": ["const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || \"http://0.0.0.0:8005\";\r\nconst PORTAL_SERVICE_BASE_URL =\r\n  process.env.DP_PORTAL_SERVICE || \"http://0.0.0.0:8006\";\r\n// Ensure this is the correct base URL for your experiment APIs,\r\n// the example uses localhost:8006, so adjust if necessary.\r\n// For this example, I will use the user-provided localhost:8006\r\nexport const EXPERIMENT_BASE_URL =\r\n  process.env.NEXT_PUBLIC_BASE_URL || \"http://localhost:8005\";\r\n\r\nexport const isADLogin = (): boolean =>\r\n  process.env.NEXT_PUBLIC_AD_LOGIN === \"true\";\r\n\r\nexport const isParsedResume =\r\n  process.env.NEXT_PUBLIC_PARSE_RESUME_FROM_MERCURY === \"true\";\r\n\r\nexport const IS_WHY_FIT_EDITABLE = process.env.NEXT_PUBLIC_IS_WHY_FIT_EDITABLE;\r\n\r\nexport const IS_LOCK_FEATURE_DISABLED =\r\n  process.env.NEXT_PUBLIC_IS_LOCK_FEATURE_DISABLED;\r\n\r\nexport const API_ENDPOINTS = {\r\n  categories: `${BASE_URL}/categories`,\r\n  subCategories: `${BASE_URL}/subcategories`,\r\n  subCategoriesPools: `${BASE_URL}/subcategory/pools`,\r\n  jobTitlesBySubCategory: `${BASE_URL}/jobtitles/:sub_category_id?limit=10000000`,\r\n  deleteAttribute: `${BASE_URL}/attribute/delete/:attribute_id`,\r\n  fetchAttributesBySubCategory: `${BASE_URL}/attributes/:sub_category_id?limit=10000000`,\r\n  fetchWeightsBySubCategory: `${BASE_URL}/weights/:sub_category_id`,\r\n  updateAttributeWeight: `${BASE_URL}/attributes/:sub_category_id/update`,\r\n  updateSubcategoryOfAttribute: `${BASE_URL}/attributes/:attribute_id/subcategory`,\r\n  updateAttributeApprovalStatus: `${BASE_URL}/attributes/:attribute_id/approval`,\r\n  candidatesData: `${BASE_URL}/candidates`,\r\n  updateCandidatesReviewData: `${BASE_URL}/candidates/update_in_db`,\r\n  jobsData: `${BASE_URL}/jobs`,\r\n  getVacancies: `${BASE_URL}/vacancies`,\r\n  getVacanciesFromFiles: `${BASE_URL}/files/vacancies`,\r\n  getCandidatesByVacancyId: `${BASE_URL}/candidates/:vacancy_id`,\r\n  getResumeByContactId: `${BASE_URL}/resume/:contact_id`,\r\n  getResumeFromFileByContactId: `${BASE_URL}/files/candidate-resume/:contact_id`,\r\n  getAllSubcategoryWeightConfigs: `${BASE_URL}/v1/subcategory/weight-configs`,\r\n  updateSubcategoryWeightConfig: `${BASE_URL}/v1/subcategory/weight-configs/:subcategory_id`,\r\n  getCandidateStats: `${BASE_URL}/api/candidate-stats`,\r\n  getEntitlements: `${PORTAL_SERVICE_BASE_URL}/api/entitlement`,\r\n  getVacancyConfigAndStatus: `${BASE_URL}/vacancies/:vacancy_id/status`,\r\n  startVacancyReview: `${BASE_URL}/vacancies/:vacancy_id/start`,\r\n  completeVacancyReview: `${BASE_URL}/vacancies/:vacancy_id/complete`,\r\n  updateWhyFitData: `${BASE_URL}/candidates/fitness_reason`,\r\n  vacanciesShortlisted: `${BASE_URL}/vacancies/shortlist`,\r\n  saveHistoryLogs: `${PORTAL_SERVICE_BASE_URL}/api/add_historical_data?email_id={email_id}&portal_name={portal_name}&feature={feature}`,\r\n  regenerateCatalystMatch: `${BASE_URL}/vacancies/:vacancy_id/regenerate-catalyst-match`,\r\n  catalystMatchStatus: `${BASE_URL}/vacancies/:vacancy_id/catalystmatchstatus`,\r\n\r\n  // New Experiment Endpoints\r\n  experimentGetVacancies: `${EXPERIMENT_BASE_URL}/experiment/vacancies`,\r\n  experimentGetVacancyRunDetails: `${EXPERIMENT_BASE_URL}/experiment/vacancies/:vacancy_id/runs/:run_id/details`,\r\n  experimentGetCandidatesForVacancyRun: `${EXPERIMENT_BASE_URL}/experiment/vacancies/:vacancy_id/runs/:run_id/candidates`,\r\n  experimentGetRunConfig: `${EXPERIMENT_BASE_URL}/experiment/runs/:run_id/config`,\r\n  experimentGetCandidateResume: `${EXPERIMENT_BASE_URL}/experiment/candidates/:contact_id/resume`,\r\n  experimentArchiveVacancy: `${EXPERIMENT_BASE_URL}/experiment/vacancies/archive`, // Added new endpoint\r\n  experimentPromoteResults: `${EXPERIMENT_BASE_URL}/experiment/results/promote`, // + New endpoint for promoting results\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;AAAiB;AAAjB,MAAM,WAAW,2DAAoC;AACrD,MAAM,0BACJ,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI;AAI5B,MAAM,sBACX,2DAAoC;AAE/B,MAAM,YAAY,IACvB,6CAAqC;AAEhC,MAAM,iBACX,6CAAsD;AAEjD,MAAM;AAEN,MAAM;AAGN,MAAM,gBAAgB;IAC3B,YAAY,GAAG,SAAS,WAAW,CAAC;IACpC,eAAe,GAAG,SAAS,cAAc,CAAC;IAC1C,oBAAoB,GAAG,SAAS,kBAAkB,CAAC;IACnD,wBAAwB,GAAG,SAAS,0CAA0C,CAAC;IAC/E,iBAAiB,GAAG,SAAS,+BAA+B,CAAC;IAC7D,8BAA8B,GAAG,SAAS,2CAA2C,CAAC;IACtF,2BAA2B,GAAG,SAAS,yBAAyB,CAAC;IACjE,uBAAuB,GAAG,SAAS,mCAAmC,CAAC;IACvE,8BAA8B,GAAG,SAAS,qCAAqC,CAAC;IAChF,+BAA+B,GAAG,SAAS,kCAAkC,CAAC;IAC9E,gBAAgB,GAAG,SAAS,WAAW,CAAC;IACxC,4BAA4B,GAAG,SAAS,wBAAwB,CAAC;IACjE,UAAU,GAAG,SAAS,KAAK,CAAC;IAC5B,cAAc,GAAG,SAAS,UAAU,CAAC;IACrC,uBAAuB,GAAG,SAAS,gBAAgB,CAAC;IACpD,0BAA0B,GAAG,SAAS,uBAAuB,CAAC;IAC9D,sBAAsB,GAAG,SAAS,mBAAmB,CAAC;IACtD,8BAA8B,GAAG,SAAS,mCAAmC,CAAC;IAC9E,gCAAgC,GAAG,SAAS,8BAA8B,CAAC;IAC3E,+BAA+B,GAAG,SAAS,8CAA8C,CAAC;IAC1F,mBAAmB,GAAG,SAAS,oBAAoB,CAAC;IACpD,iBAAiB,GAAG,wBAAwB,gBAAgB,CAAC;IAC7D,2BAA2B,GAAG,SAAS,6BAA6B,CAAC;IACrE,oBAAoB,GAAG,SAAS,4BAA4B,CAAC;IAC7D,uBAAuB,GAAG,SAAS,+BAA+B,CAAC;IACnE,kBAAkB,GAAG,SAAS,0BAA0B,CAAC;IACzD,sBAAsB,GAAG,SAAS,oBAAoB,CAAC;IACvD,iBAAiB,GAAG,wBAAwB,wFAAwF,CAAC;IACrI,yBAAyB,GAAG,SAAS,gDAAgD,CAAC;IACtF,qBAAqB,GAAG,SAAS,0CAA0C,CAAC;IAE5E,2BAA2B;IAC3B,wBAAwB,GAAG,oBAAoB,qBAAqB,CAAC;IACrE,gCAAgC,GAAG,oBAAoB,sDAAsD,CAAC;IAC9G,sCAAsC,GAAG,oBAAoB,yDAAyD,CAAC;IACvH,wBAAwB,GAAG,oBAAoB,+BAA+B,CAAC;IAC/E,8BAA8B,GAAG,oBAAoB,yCAAyC,CAAC;IAC/F,0BAA0B,GAAG,oBAAoB,6BAA6B,CAAC;IAC/E,0BAA0B,GAAG,oBAAoB,2BAA2B,CAAC;AAC/E"}}, {"offset": {"line": 937, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 943, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/candidates/VacancyItem.tsx"], "sourcesContent": ["import { Vacancy } from \"@/app/candidates/helper\";\r\nimport { Info, Lock, X } from \"lucide-react\";\r\nimport React, { useEffect, useRef } from \"react\";\r\nimport { But<PERSON> } from \"../ui/button\";\r\nimport AppToolTip from \"../AppToolTip\";\r\nimport Modal from \"../Modal\";\r\nimport { Input } from \"../ui/input\";\r\nimport { Badge } from \"../ui/badge\";\r\nimport VacancyDetails from \"./VacancyDetails\";\r\nimport { IS_LOCK_FEATURE_DISABLED } from \"@/api/config\";\r\n\r\nconst Loading = () => (\r\n  <svg\r\n    className=\"ml-2 mt-0.5 size-5 animate-spin text-white\"\r\n    xmlns=\"http://www.w3.org/2000/svg\"\r\n    fill=\"none\"\r\n    viewBox=\"0 0 24 24\"\r\n  >\r\n    <circle\r\n      className=\"opacity-25\"\r\n      cx=\"12\"\r\n      cy=\"12\"\r\n      r=\"10\"\r\n      stroke=\"currentColor\"\r\n      strokeWidth=\"4\"\r\n    ></circle>\r\n    <path\r\n      className=\"opacity-75\"\r\n      fill=\"currentColor\"\r\n      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\r\n    ></path>\r\n  </svg>\r\n);\r\n\r\nconst VacancyItem = ({\r\n  vacancy,\r\n  selectedVacancy,\r\n  handleVacancyClick,\r\n  setSearch,\r\n  activeVacancy,\r\n  setActiveVacancy,\r\n  startRating,\r\n  setStartRating,\r\n  completeRating,\r\n  setCompleteRating,\r\n  userName,\r\n  setUserName,\r\n  reviewLoading,\r\n  completeReview,\r\n  startReview,\r\n}: {\r\n  vacancy: Vacancy;\r\n  selectedVacancy: Vacancy | null;\r\n  handleVacancyClick: (vacancy: Vacancy) => void;\r\n  setSearch: React.Dispatch<React.SetStateAction<string>>;\r\n  activeVacancy: Vacancy | null;\r\n  setActiveVacancy: React.Dispatch<React.SetStateAction<Vacancy | null>>;\r\n  startRating: boolean;\r\n  setStartRating: React.Dispatch<React.SetStateAction<boolean>>;\r\n  completeRating: boolean;\r\n  setCompleteRating: React.Dispatch<React.SetStateAction<boolean>>;\r\n  reviewLoading: boolean;\r\n  userName: string;\r\n  setUserName: React.Dispatch<React.SetStateAction<string>>;\r\n  completeReview: () => void;\r\n  startReview: () => void;\r\n}) => {\r\n  // Inside your component\r\n  const hoverTimeout = useRef<NodeJS.Timeout | null>(null);\r\n\r\n  useEffect(() => {\r\n    const uName = localStorage.getItem(\"userName\");\r\n    if (uName) {\r\n      setUserName(uName);\r\n    }\r\n    const uEmail = localStorage.getItem(\"emailId\");\r\n    if (uEmail) {\r\n      setUserName(uEmail);\r\n    }\r\n  }, [setUserName]);\r\n\r\n  const handleStartReview = (vacancy: Vacancy) => {\r\n    if (selectedVacancy?.vacancy_id !== vacancy.vacancy_id) {\r\n      handleVacancyClick(vacancy);\r\n    }\r\n    setStartRating(true);\r\n  };\r\n\r\n  const handleLockReview = (vacancy: Vacancy) => {\r\n    if (selectedVacancy?.vacancy_id !== vacancy.vacancy_id) {\r\n      handleVacancyClick(vacancy);\r\n    }\r\n    setCompleteRating(true);\r\n  };\r\n\r\n  const isSelected = selectedVacancy?.refno === vacancy?.refno;\r\n  const locked = vacancy?.is_locked;\r\n  const lockedBy = vacancy?.locked_by;\r\n\r\n  const isLockFeatureDisabled = IS_LOCK_FEATURE_DISABLED === \"true\";\r\n\r\n  return (\r\n    <>\r\n      <li\r\n        className={`flex justify-between items-center p-2 border-b cursor-pointer hover:bg-gray-100 ${\r\n          isSelected\r\n            ? \"bg-gray-900 text-white hover:bg-gray-900 rounded-md\"\r\n            : \"\"\r\n        }`}\r\n        onClick={() => {\r\n          handleVacancyClick(vacancy);\r\n          setActiveVacancy(null);\r\n          setSearch(\"\");\r\n        }}\r\n      >\r\n        <span>{vacancy?.refno}</span>\r\n\r\n        <span\r\n          className=\"flex items-center\"\r\n          onClick={(e: React.MouseEvent<HTMLSpanElement>) => {\r\n            e.stopPropagation();\r\n          }}\r\n        >\r\n          {isLockFeatureDisabled ? null : (\r\n            <>\r\n              {locked ? (\r\n                <span className=\"text-sm font-medium\">\r\n                  <span className=\"text-sm font-medium\">\r\n                    <AppToolTip\r\n                      text={<p>Locked By: {vacancy?.locked_by}</p>}\r\n                      header={\r\n                        <div className=\"flex cursor-text items-center gap-2\">\r\n                          <Lock size={16} />\r\n                          <Badge\r\n                            variant=\"outline\"\r\n                            className={`truncate max-w-[150px] ml-1.5 ${\r\n                              isSelected\r\n                                ? \"border-white bg-gray-900 text-white\"\r\n                                : \"border-gray-900 text-gray-900\"\r\n                            }`}\r\n                          >\r\n                            {lockedBy}\r\n                          </Badge>\r\n                        </div>\r\n                      }\r\n                      direction=\"top\"\r\n                    />\r\n                  </span>\r\n                </span>\r\n              ) : !locked && lockedBy ? (\r\n                <>\r\n                  <AppToolTip\r\n                    text={<p>Under Review By: {lockedBy}</p>}\r\n                    header={\r\n                      <Badge\r\n                        variant=\"outline\"\r\n                        className={`truncate cursor-text max-w-[100px] ml-1.5 mr-2 ${\r\n                          isSelected\r\n                            ? \"border-white bg-gray-900 text-white\"\r\n                            : \"border-gray-900 text-gray-900\"\r\n                        }`}\r\n                      >\r\n                        {lockedBy}\r\n                      </Badge>\r\n                    }\r\n                    direction=\"top\"\r\n                  />\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    size=\"sm\"\r\n                    className={`text-xs h-6 px-2 ${\r\n                      isSelected\r\n                        ? \"border-white bg-gray-900 text-white\"\r\n                        : \"border-gray-900 text-gray-900\"\r\n                    }`}\r\n                    onClick={() => handleLockReview(vacancy)}\r\n                  >\r\n                    Lock Review\r\n                  </Button>\r\n                </>\r\n              ) : (\r\n                <Button\r\n                  variant=\"outline\"\r\n                  size=\"sm\"\r\n                  className={`text-xs h-6 px-2 ${\r\n                    isSelected\r\n                      ? \"border-white bg-gray-900 text-white\"\r\n                      : \"border-gray-900 text-gray-900\"\r\n                  }`}\r\n                  onClick={() => handleStartReview(vacancy)}\r\n                >\r\n                  Start Review\r\n                </Button>\r\n              )}\r\n            </>\r\n          )}\r\n\r\n          <span className=\"group ml-2\">\r\n            <Info\r\n              size={18}\r\n              className={`cursor-pointer ${\r\n                isSelected ? \"text-black fill-white\" : \"text-white fill-black\"\r\n              }`}\r\n              onMouseEnter={() => {\r\n                hoverTimeout.current = setTimeout(() => {\r\n                  setActiveVacancy(vacancy);\r\n                }, 200);\r\n              }}\r\n              onMouseLeave={() => {\r\n                if (hoverTimeout.current) {\r\n                  clearTimeout(hoverTimeout.current);\r\n                  hoverTimeout.current = null;\r\n                }\r\n              }}\r\n              onClick={(e: React.MouseEvent<SVGSVGElement>) => {\r\n                e.stopPropagation();\r\n              }}\r\n            />\r\n            {activeVacancy?.refno === vacancy?.refno && (\r\n              <VacancyDetails\r\n                vacancy={activeVacancy}\r\n                setActiveVacancy={setActiveVacancy}\r\n              />\r\n            )}\r\n          </span>\r\n        </span>\r\n      </li>\r\n      <Modal isOpen={startRating} onClose={() => setStartRating(false)}>\r\n        <p className=\"mb-5\">\r\n          {\" \"}\r\n          Enter your email to initiate the review process for candidates of\r\n          vacancy <strong>({selectedVacancy?.refno})</strong>.\r\n        </p>\r\n        <p>\r\n          <Input\r\n            type=\"email\"\r\n            placeholder=\"Enter your email\"\r\n            value={userName}\r\n            onChange={(e) => setUserName(e.target.value)}\r\n            className=\"w-full border border-gray-300 rounded-lg p-2 my-2\"\r\n          />\r\n        </p>\r\n        <div className=\"flex justify-end mt-10\">\r\n          <Button\r\n            onClick={() => setStartRating(false)}\r\n            className=\"rounded-md border text-gray-800 border-gray-800 bg-white hover:bg-white\"\r\n          >\r\n            Cancel\r\n          </Button>\r\n          <Button\r\n            onClick={startReview}\r\n            className=\"bg-gray-800 hover:bg-gray-900 ml-3 rounded-md\"\r\n          >\r\n            Start Review {reviewLoading && <Loading />}\r\n          </Button>\r\n        </div>\r\n      </Modal>\r\n      <Modal isOpen={completeRating} onClose={() => setCompleteRating(false)}>\r\n        <p className=\"mb-5\">\r\n          {\" \"}\r\n          Are you sure you want to lock your review for the{\" \"}\r\n          <strong>({selectedVacancy?.refno})</strong>&nbsp; vacancy? Once\r\n          locked, no further changes can be made.\r\n        </p>\r\n\r\n        <div className=\"flex justify-end mt-10\">\r\n          <Button\r\n            onClick={() => setCompleteRating(false)}\r\n            className=\"rounded-md border text-gray-800 border-gray-800 bg-white hover:bg-white\"\r\n          >\r\n            Cancel\r\n          </Button>\r\n          <Button\r\n            onClick={completeReview}\r\n            className=\"bg-gray-800 hover:bg-gray-900 ml-3 rounded-md\"\r\n          >\r\n            Lock Review {reviewLoading && <Loading />}\r\n          </Button>\r\n        </div>\r\n      </Modal>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default VacancyItem;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;AAAA;;;;;;;;;;;;AAUA,MAAM,UAAU,kBACd,6LAAC;QACC,WAAU;QACV,OAAM;QACN,MAAK;QACL,SAAQ;;0BAER,6LAAC;gBACC,WAAU;gBACV,IAAG;gBACH,IAAG;gBACH,GAAE;gBACF,QAAO;gBACP,aAAY;;;;;;0BAEd,6LAAC;gBACC,WAAU;gBACV,MAAK;gBACL,GAAE;;;;;;;;;;;;KAlBF;AAuBN,MAAM,cAAc,CAAC,EACnB,OAAO,EACP,eAAe,EACf,kBAAkB,EAClB,SAAS,EACT,aAAa,EACb,gBAAgB,EAChB,WAAW,EACX,cAAc,EACd,cAAc,EACd,iBAAiB,EACjB,QAAQ,EACR,WAAW,EACX,aAAa,EACb,cAAc,EACd,WAAW,EAiBZ;;IACC,wBAAwB;IACxB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IAEnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,IAAI,OAAO;gBACT,YAAY;YACd;YACA,MAAM,SAAS,aAAa,OAAO,CAAC;YACpC,IAAI,QAAQ;gBACV,YAAY;YACd;QACF;gCAAG;QAAC;KAAY;IAEhB,MAAM,oBAAoB,CAAC;QACzB,IAAI,iBAAiB,eAAe,QAAQ,UAAU,EAAE;YACtD,mBAAmB;QACrB;QACA,eAAe;IACjB;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,iBAAiB,eAAe,QAAQ,UAAU,EAAE;YACtD,mBAAmB;QACrB;QACA,kBAAkB;IACpB;IAEA,MAAM,aAAa,iBAAiB,UAAU,SAAS;IACvD,MAAM,SAAS,SAAS;IACxB,MAAM,WAAW,SAAS;IAE1B,MAAM,wBAAwB,gHAAA,CAAA,2BAAwB,KAAK;IAE3D,qBACE;;0BACE,6LAAC;gBACC,WAAW,CAAC,gFAAgF,EAC1F,aACI,wDACA,IACJ;gBACF,SAAS;oBACP,mBAAmB;oBACnB,iBAAiB;oBACjB,UAAU;gBACZ;;kCAEA,6LAAC;kCAAM,SAAS;;;;;;kCAEhB,6LAAC;wBACC,WAAU;wBACV,SAAS,CAAC;4BACR,EAAE,eAAe;wBACnB;;4BAEC,wBAAwB,qBACvB;0CACG,uBACC,6LAAC;oCAAK,WAAU;8CACd,cAAA,6LAAC;wCAAK,WAAU;kDACd,cAAA,6LAAC,4HAAA,CAAA,UAAU;4CACT,oBAAM,6LAAC;;oDAAE;oDAAY,SAAS;;;;;;;4CAC9B,sBACE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qMAAA,CAAA,OAAI;wDAAC,MAAM;;;;;;kEACZ,6LAAC,6HAAA,CAAA,QAAK;wDACJ,SAAQ;wDACR,WAAW,CAAC,8BAA8B,EACxC,aACI,wCACA,iCACJ;kEAED;;;;;;;;;;;;4CAIP,WAAU;;;;;;;;;;;;;;;2CAId,CAAC,UAAU,yBACb;;sDACE,6LAAC,4HAAA,CAAA,UAAU;4CACT,oBAAM,6LAAC;;oDAAE;oDAAkB;;;;;;;4CAC3B,sBACE,6LAAC,6HAAA,CAAA,QAAK;gDACJ,SAAQ;gDACR,WAAW,CAAC,+CAA+C,EACzD,aACI,wCACA,iCACJ;0DAED;;;;;;4CAGL,WAAU;;;;;;sDAEZ,6LAAC,8HAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAW,CAAC,iBAAiB,EAC3B,aACI,wCACA,iCACJ;4CACF,SAAS,IAAM,iBAAiB;sDACjC;;;;;;;iEAKH,6LAAC,8HAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAW,CAAC,iBAAiB,EAC3B,aACI,wCACA,iCACJ;oCACF,SAAS,IAAM,kBAAkB;8CAClC;;;;;;;0CAOP,6LAAC;gCAAK,WAAU;;kDACd,6LAAC,qMAAA,CAAA,OAAI;wCACH,MAAM;wCACN,WAAW,CAAC,eAAe,EACzB,aAAa,0BAA0B,yBACvC;wCACF,cAAc;4CACZ,aAAa,OAAO,GAAG,WAAW;gDAChC,iBAAiB;4CACnB,GAAG;wCACL;wCACA,cAAc;4CACZ,IAAI,aAAa,OAAO,EAAE;gDACxB,aAAa,aAAa,OAAO;gDACjC,aAAa,OAAO,GAAG;4CACzB;wCACF;wCACA,SAAS,CAAC;4CACR,EAAE,eAAe;wCACnB;;;;;;oCAED,eAAe,UAAU,SAAS,uBACjC,6LAAC,8IAAA,CAAA,UAAc;wCACb,SAAS;wCACT,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;0BAM5B,6LAAC,uHAAA,CAAA,UAAK;gBAAC,QAAQ;gBAAa,SAAS,IAAM,eAAe;;kCACxD,6LAAC;wBAAE,WAAU;;4BACV;4BAAI;0CAEG,6LAAC;;oCAAO;oCAAE,iBAAiB;oCAAM;;;;;;;4BAAU;;;;;;;kCAErD,6LAAC;kCACC,cAAA,6LAAC,6HAAA,CAAA,QAAK;4BACJ,MAAK;4BACL,aAAY;4BACZ,OAAO;4BACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4BAC3C,WAAU;;;;;;;;;;;kCAGd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,8HAAA,CAAA,SAAM;gCACL,SAAS,IAAM,eAAe;gCAC9B,WAAU;0CACX;;;;;;0CAGD,6LAAC,8HAAA,CAAA,SAAM;gCACL,SAAS;gCACT,WAAU;;oCACX;oCACe,+BAAiB,6LAAC;;;;;;;;;;;;;;;;;;;;;;;0BAItC,6LAAC,uHAAA,CAAA,UAAK;gBAAC,QAAQ;gBAAgB,SAAS,IAAM,kBAAkB;;kCAC9D,6LAAC;wBAAE,WAAU;;4BACV;4BAAI;4BAC6C;0CAClD,6LAAC;;oCAAO;oCAAE,iBAAiB;oCAAM;;;;;;;4BAAU;;;;;;;kCAI7C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,8HAAA,CAAA,SAAM;gCACL,SAAS,IAAM,kBAAkB;gCACjC,WAAU;0CACX;;;;;;0CAGD,6LAAC,8HAAA,CAAA,SAAM;gCACL,SAAS;gCACT,WAAU;;oCACX;oCACc,+BAAiB,6LAAC;;;;;;;;;;;;;;;;;;;;;;;;;AAM3C;GAxPM;MAAA;uCA0PS"}}, {"offset": {"line": 1382, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1388, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/candidates/CandidateResume.tsx"], "sourcesContent": ["import { ResumeData, WorkExperience } from \"@/app/candidates/helper\";\r\nimport React from \"react\";\r\nimport Loading from \"../Loading\";\r\nimport { DownloadIcon, ExternalLink } from \"lucide-react\";\r\nimport { isParsedResume } from \"@/api/config\";\r\nimport { Button } from \"../ui/button\";\r\nimport { Vacancy } from \"../CandidateTable/helper\";\r\nimport { getAppInsights } from \"@/library/appInsights\";\r\n\r\nconst showSoftSkills = process.env.NEXT_PUBLIC_IS_SHOW_SOFT_SKILLS_ENABLED === \"true\";\r\n\r\nconst SkillBadge = ({\r\n  skill,\r\n  styleClass,\r\n}: {\r\n  skill: string;\r\n  styleClass?: string;\r\n}) => {\r\n  return (\r\n    <span\r\n      className={`px-3 py-1 rounded-full text-xs font-semibold text-gray-800 border border-gray-300 shadow-sm ${\r\n        styleClass ?? \"\"\r\n      }`}\r\n    >\r\n      {skill}\r\n    </span>\r\n  );\r\n};\r\n\r\nconst CandidateResume = ({\r\n  vacancy,\r\n  selectedResume,\r\n  setSelectedResume,\r\n  isResumeModalOpen,\r\n  setIsResumeModalOpen,\r\n  isLoading,\r\n}: {\r\n  vacancy: Vacancy | null;\r\n  selectedResume: ResumeData | null;\r\n  setSelectedResume: React.Dispatch<React.SetStateAction<null | ResumeData>>;\r\n  isResumeModalOpen: boolean;\r\n  setIsResumeModalOpen: React.Dispatch<React.SetStateAction<boolean>>;\r\n  isLoading: boolean;\r\n}) => {\r\n  const cv = selectedResume?.candidate?.resume_file;\r\n\r\n  const legendSkillWeights = [\"high\", \"medium\", \"normal\"];\r\n\r\n  const getSharePointURL = (url: string) => {\r\n    url = url.toLocaleLowerCase();\r\n    const baseURL = url.split(\"/contact\")[0] || \"\";\r\n    const idMatch = url.match(/\\/sites.*$/);\r\n    const encodedFileName = idMatch ? encodeURIComponent(idMatch[0]) : \"\";\r\n    return `${baseURL}/_layouts/15/embed.aspx?Id=${encodedFileName}`;\r\n  };\r\n\r\n  const resultURL = cv ? getSharePointURL(cv) : \"\";\r\n\r\n  const getFullWorkExperience = (selectedResume: ResumeData) => {\r\n    return (\r\n      <div className=\"mb-6 p-4 bg-gray-100 rounded-lg\">\r\n        <h3 className=\"text-xl font-bold text-gray-900 mb-2\">\r\n          Full Work Experience\r\n        </h3>\r\n        {selectedResume?.candidate?.[\"work experience\"].length > 0 ? (\r\n          <ul className=\"list-disc ml-5 space-y-3\">\r\n            {selectedResume?.candidate?.[\"work experience\"].map(\r\n              (exp: WorkExperience, index: number) => (\r\n                <li key={index} className=\"p-3 bg-white rounded-md shadow\">\r\n                  <p className=\"font-semibold text-lg\">\r\n                    {exp.title} at {exp.company}\r\n                  </p>\r\n                  <p className=\"text-gray-600 text-sm\">\r\n                    {exp.start_date} - {exp.end_date || \"Present\"}\r\n                  </p>\r\n                  <p className=\"text-gray-700 max-h-[150px] overflow-y-auto\">\r\n                    {(() => {\r\n                      const description = exp.description || \"\";\r\n                      const softSkills =\r\n                        vacancy?.vacancy_data?.[\"soft skills\"] || [];\r\n                      const technicalSkills =\r\n                        vacancy?.vacancy_data?.[\"technical skills\"] || [];\r\n                      const toolsAndPlatforms =\r\n                        vacancy?.vacancy_data?.[\"tools and platforms\"] || [];\r\n                      const allSkills = [\r\n                        ...softSkills,\r\n                        ...technicalSkills,\r\n                        ...toolsAndPlatforms,\r\n                      ];\r\n                      const regex = new RegExp(\r\n                        allSkills\r\n                          .map(\r\n                            (skill) =>\r\n                              `\\\\b${skill.name.replace(\r\n                                /[.*+?^${}()|[\\]\\\\]/g,\r\n                                \"\\\\$&\"\r\n                              )}\\\\b`\r\n                          )\r\n                          .join(\"|\"),\r\n                        \"gi\"\r\n                      );\r\n                      const result: (string | React.ReactNode)[] = [];\r\n                      let lastIndex = 0;\r\n                      let match: RegExpExecArray | null;\r\n                      while ((match = regex.exec(description)) !== null) {\r\n                        if (match.index > lastIndex) {\r\n                          result.push(\r\n                            description.slice(lastIndex, match.index)\r\n                          );\r\n                        }\r\n                        const matchedSkill = allSkills.find((skill) => {\r\n                          const skillName =\r\n                            typeof skill === \"string\" ? skill : skill.name;\r\n                          return (\r\n                            skillName &&\r\n                            match &&\r\n                            match[0] &&\r\n                            skillName.toLowerCase() === match[0].toLowerCase()\r\n                          );\r\n                        });\r\n                        const weight =\r\n                          typeof matchedSkill === \"object\" &&\r\n                          matchedSkill !== null\r\n                            ? matchedSkill.weight\r\n                            : undefined;\r\n                        const colorClass = weight\r\n                          ? getHighlightStyle(weight)\r\n                          : \"bg-gray-200 text-gray-700 border border-gray-300\";\r\n                        result.push(\r\n                          <span\r\n                            key={match.index + \"-\" + match[0]}\r\n                            className={colorClass}\r\n                          >\r\n                            {match[0]}\r\n                          </span>\r\n                        );\r\n                        lastIndex = regex.lastIndex;\r\n                      }\r\n                      if (lastIndex < description.length) {\r\n                        result.push(description.slice(lastIndex));\r\n                      }\r\n                      return result;\r\n                    })()}\r\n                  </p>\r\n                </li>\r\n              )\r\n            )}\r\n          </ul>\r\n        ) : (\r\n          <p className=\"text-gray-500\">No work experience available</p>\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  const getSkillBadgeHighlightClassName = (skill: string) => {\r\n    if (!skill) return \"\";\r\n    const skillLower = skill.toLowerCase();\r\n    const softSkills = vacancy?.vacancy_data?.[\"soft skills\"] || [];\r\n    const technicalSkills = vacancy?.vacancy_data?.[\"technical skills\"] || [];\r\n    const toolsAndPlatforms =\r\n      vacancy?.vacancy_data?.[\"tools and platforms\"] || [];\r\n    const allSkills = [...softSkills, ...technicalSkills, ...toolsAndPlatforms];\r\n    const matchedSkill = allSkills.find(\r\n      (skill) => skill.name.toLowerCase() === skillLower\r\n    );\r\n    return matchedSkill ? getHighlightStyle(matchedSkill.weight) : \"\";\r\n  };\r\n\r\n  const getParsedResume = () => {\r\n    return (\r\n      <>\r\n        {cv ? (\r\n          <>\r\n            <div className=\"mb-6\">\r\n              <div className=\"flex justify-between my-4\">\r\n                <h3 className=\"text-xl font-bold text-gray-900\">CV</h3>\r\n                <a\r\n                  href={cv}\r\n                  target=\"_blank\"\r\n                  download\r\n                  onClick={() => {\r\n                    getAppInsights()?.trackEvent({\r\n                      name: \"FE_DownloadCVClicked\",\r\n                      properties: { resume: cv },\r\n                    });\r\n                  }}\r\n                  className=\"bg-blue-600 hover:bg-blue-700 text-white font-semibold px-4 py-2 rounded-lg transition-all flex items-center gap-2 z-10\"\r\n                >\r\n                  Download CV <DownloadIcon />\r\n                </a>\r\n              </div>\r\n              <iframe\r\n                className=\"cv-renderer\"\r\n                src={resultURL}\r\n                width=\"100%\"\r\n                height=\"600px\"\r\n              />\r\n            </div>\r\n          </>\r\n        ) : (\r\n          <div className=\"flex relative justify-center items-center text-3xl my-5\">\r\n            Resume is not available\r\n          </div>\r\n        )}\r\n      </>\r\n    );\r\n  };\r\n\r\n  // Add getHighlightStyle function for color based on weight\r\n  const getHighlightStyle = (weight: string) => {\r\n    switch (weight) {\r\n      case \"high\":\r\n        return \"p-1 bg-green-200/50 text-green-800 border border-green-300 shadow-md\";\r\n      case \"medium\":\r\n        return \"p-1 bg-blue-200/50 text-blue-800 border border-blue-300 shadow-md\";\r\n      case \"normal\":\r\n        return \"p-1 bg-yellow-200/50 text-yellow-800 border border-yellow-300 shadow-md\";\r\n      case \"low\":\r\n        return \"p-1 bg-red-200/50 text-red-800 border border-red-300 shadow-md\";\r\n      default:\r\n        return \"p-1 bg-gray-200 text-gray-700 border border-gray-300\";\r\n    }\r\n  };\r\n  const handleResumeLinkClick = () => {\r\n    getAppInsights()?.trackEvent({\r\n      name: \"FE_ResumeLinkClicked\",\r\n      properties: { resume: cv },\r\n    });\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      {\" \"}\r\n      {/* Modal */}\r\n      {isResumeModalOpen && (\r\n        <div className=\"fixed z-50 inset-0 bg-gray-900 bg-opacity-60 flex items-center justify-center\">\r\n          <div className=\"bg-white w-[80vw] min-h-[60vh] px-8 rounded-lg max-h-[90vh] overflow-y-auto shadow-lg\">\r\n            {/* Header */}\r\n            <div className=\"flex items-center justify-between text-gray-900 border-b pb-2 mb-4 sticky top-0 bg-white z-10 pt-8\">\r\n              <h2 className=\"text-2xl font-extrabold \">Candidate Profile</h2>\r\n              <div className=\"flex\">\r\n                <Button\r\n                  asChild\r\n                  className=\"text-blue-500 text-lg\"\r\n                  variant=\"link\"\r\n                >\r\n                  <a\r\n                    href={cv}\r\n                    target=\"_blank\"\r\n                    rel=\"noopener noreferrer\"\r\n                    onClick={handleResumeLinkClick}\r\n                  >\r\n                    Resume Link <ExternalLink />\r\n                  </a>\r\n                </Button>\r\n                {/* Close Button */}\r\n                <button\r\n                  onClick={() => {\r\n                    setSelectedResume(null);\r\n                    setIsResumeModalOpen(false);\r\n                  }}\r\n                  className=\"bg-red-600 hover:bg-red-700 text-white font-semibold px-6 py-2 rounded-lg transition-all\"\r\n                >\r\n                  Close\r\n                </button>\r\n              </div>\r\n            </div>\r\n            {selectedResume?.candidate &&\r\n            Object.keys(selectedResume.candidate)?.length > 0 ? (\r\n              <>\r\n                {/* Personal Details */}\r\n                <div className=\"mb-6\">\r\n                  <p className=\"text-lg font-semibold text-gray-700\">\r\n                    <span className=\"text-gray-900\">👤 Name:</span>{\" \"}\r\n                    {selectedResume?.candidate?.name}\r\n                  </p>\r\n                  <p className=\"text-lg font-semibold text-gray-700\">\r\n                    <span className=\"text-gray-900\">✉️ Email:</span>{\" \"}\r\n                    {selectedResume?.candidate?.email}\r\n                  </p>\r\n                  <p className=\"text-lg font-semibold text-gray-700\">\r\n                    <span className=\"text-gray-900\">📞 Phone:</span>{\" \"}\r\n                    {selectedResume?.candidate?.phone}\r\n                  </p>\r\n                  {selectedResume?.candidate?.city &&\r\n                    selectedResume?.candidate?.state && (\r\n                      <p className=\"text-lg font-semibold text-gray-700\">\r\n                        <span className=\"text-gray-900\">📍 location:</span>{\" \"}\r\n                        {`${selectedResume?.candidate?.city}, ${selectedResume?.candidate?.state}`}\r\n                      </p>\r\n                    )}\r\n                </div>\r\n\r\n                {/* Skills & Certifications */}\r\n                <div className=\"mb-6\">\r\n                  <p className=\"flex justify-content items-start mb-5\">\r\n                    <strong className=\"w-[250px]\"> Skills</strong>\r\n                    <span className=\"flex-1 flex flex-wrap gap-2 capitalize\">\r\n                      {legendSkillWeights.map((weight, index) => (\r\n                        <SkillBadge\r\n                          key={index}\r\n                          skill={weight}\r\n                          styleClass={getHighlightStyle(weight)}\r\n                        />\r\n                      ))}\r\n                    </span>\r\n                  </p>\r\n                  {/* Soft Skills */}\r\n                  {showSoftSkills && (\r\n                    <p className=\"flex items-start mb-5\">\r\n                      <strong className=\"w-[250px]\">🗣 Soft Skills:</strong>\r\n                      <span className=\"flex-1 flex flex-wrap gap-2 capitalize\">\r\n                        {selectedResume?.candidate?.[\"soft skills\"]?.length > 0\r\n                          ? selectedResume?.candidate?.[\"soft skills\"].map(\r\n                              (skill, index) => (\r\n                                <SkillBadge\r\n                                  key={index}\r\n                                  skill={skill}\r\n                                  styleClass={getSkillBadgeHighlightClassName(\r\n                                    skill\r\n                                  )}\r\n                                />\r\n                              )\r\n                            )\r\n                          : \"N/A\"}\r\n                      </span>\r\n                    </p>\r\n                  )}\r\n\r\n                  {/* Technical Skills */}\r\n                  <p className=\"flex items-start mb-5\">\r\n                    <strong className=\"w-[250px]\">💻 Technical Skills:</strong>\r\n                    <span className=\"flex-1 flex flex-wrap gap-2 capitalize\">\r\n                      {selectedResume?.candidate?.[\"technical skills\"]?.length >\r\n                      0\r\n                        ? selectedResume?.candidate?.[\"technical skills\"]?.map(\r\n                            (skill, index) => (\r\n                              <SkillBadge\r\n                                key={index}\r\n                                skill={skill}\r\n                                styleClass={getSkillBadgeHighlightClassName(\r\n                                  skill\r\n                                )}\r\n                              />\r\n                            )\r\n                          )\r\n                        : \"N/A\"}\r\n                    </span>\r\n                  </p>\r\n\r\n                  {/* Tools & Platforms */}\r\n                  <p className=\"flex items-start mb-5\">\r\n                    <strong className=\"w-[250px]\">🛠 Tools & Platforms:</strong>\r\n                    <span className=\"flex-1 flex flex-wrap gap-2 capitalize\">\r\n                      {selectedResume?.candidate?.[\"tools and platforms\"]\r\n                        ?.length > 0\r\n                        ? selectedResume?.candidate?.[\r\n                            \"tools and platforms\"\r\n                          ]?.map((skill, index) => (\r\n                            <SkillBadge\r\n                              key={index}\r\n                              skill={skill}\r\n                              styleClass={getSkillBadgeHighlightClassName(\r\n                                skill\r\n                              )}\r\n                            />\r\n                          ))\r\n                        : \"N/A\"}\r\n                    </span>\r\n                  </p>\r\n                </div>\r\n\r\n                {!isParsedResume\r\n                  ? getParsedResume()\r\n                  : getFullWorkExperience(selectedResume)}\r\n              </>\r\n            ) : selectedResume?.candidate &&\r\n              Object.keys(selectedResume.candidate)?.length === 0 ? (\r\n              <div className=\"flex relative top-28 pt-10 justify-center items-center text-3xl\">\r\n                Resume details not found\r\n              </div>\r\n            ) : isResumeModalOpen && isLoading ? (\r\n              <Loading height=\"h-[80vh]\" />\r\n            ) : null}\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CandidateResume;\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AAEA;AAEuB;AANvB;AAAA;;;;;;;AAMA,MAAM,iBAAiB,8CAAwD;AAE/E,MAAM,aAAa,CAAC,EAClB,KAAK,EACL,UAAU,EAIX;IACC,qBACE,6LAAC;QACC,WAAW,CAAC,4FAA4F,EACtG,cAAc,IACd;kBAED;;;;;;AAGP;KAhBM;AAkBN,MAAM,kBAAkB,CAAC,EACvB,OAAO,EACP,cAAc,EACd,iBAAiB,EACjB,iBAAiB,EACjB,oBAAoB,EACpB,SAAS,EAQV;IACC,MAAM,KAAK,gBAAgB,WAAW;IAEtC,MAAM,qBAAqB;QAAC;QAAQ;QAAU;KAAS;IAEvD,MAAM,mBAAmB,CAAC;QACxB,MAAM,IAAI,iBAAiB;QAC3B,MAAM,UAAU,IAAI,KAAK,CAAC,WAAW,CAAC,EAAE,IAAI;QAC5C,MAAM,UAAU,IAAI,KAAK,CAAC;QAC1B,MAAM,kBAAkB,UAAU,mBAAmB,OAAO,CAAC,EAAE,IAAI;QACnE,OAAO,GAAG,QAAQ,2BAA2B,EAAE,iBAAiB;IAClE;IAEA,MAAM,YAAY,KAAK,iBAAiB,MAAM;IAE9C,MAAM,wBAAwB,CAAC;QAC7B,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAuC;;;;;;gBAGpD,gBAAgB,WAAW,CAAC,kBAAkB,CAAC,SAAS,kBACvD,6LAAC;oBAAG,WAAU;8BACX,gBAAgB,WAAW,CAAC,kBAAkB,CAAC,IAC9C,CAAC,KAAqB,sBACpB,6LAAC;4BAAe,WAAU;;8CACxB,6LAAC;oCAAE,WAAU;;wCACV,IAAI,KAAK;wCAAC;wCAAK,IAAI,OAAO;;;;;;;8CAE7B,6LAAC;oCAAE,WAAU;;wCACV,IAAI,UAAU;wCAAC;wCAAI,IAAI,QAAQ,IAAI;;;;;;;8CAEtC,6LAAC;oCAAE,WAAU;8CACV,CAAC;wCACA,MAAM,cAAc,IAAI,WAAW,IAAI;wCACvC,MAAM,aACJ,SAAS,cAAc,CAAC,cAAc,IAAI,EAAE;wCAC9C,MAAM,kBACJ,SAAS,cAAc,CAAC,mBAAmB,IAAI,EAAE;wCACnD,MAAM,oBACJ,SAAS,cAAc,CAAC,sBAAsB,IAAI,EAAE;wCACtD,MAAM,YAAY;+CACb;+CACA;+CACA;yCACJ;wCACD,MAAM,QAAQ,IAAI,OAChB,UACG,GAAG,CACF,CAAC,QACC,CAAC,GAAG,EAAE,MAAM,IAAI,CAAC,OAAO,CACtB,uBACA,QACA,GAAG,CAAC,EAET,IAAI,CAAC,MACR;wCAEF,MAAM,SAAuC,EAAE;wCAC/C,IAAI,YAAY;wCAChB,IAAI;wCACJ,MAAO,CAAC,QAAQ,MAAM,IAAI,CAAC,YAAY,MAAM,KAAM;4CACjD,IAAI,MAAM,KAAK,GAAG,WAAW;gDAC3B,OAAO,IAAI,CACT,YAAY,KAAK,CAAC,WAAW,MAAM,KAAK;4CAE5C;4CACA,MAAM,eAAe,UAAU,IAAI,CAAC,CAAC;gDACnC,MAAM,YACJ,OAAO,UAAU,WAAW,QAAQ,MAAM,IAAI;gDAChD,OACE,aACA,SACA,KAAK,CAAC,EAAE,IACR,UAAU,WAAW,OAAO,KAAK,CAAC,EAAE,CAAC,WAAW;4CAEpD;4CACA,MAAM,SACJ,OAAO,iBAAiB,YACxB,iBAAiB,OACb,aAAa,MAAM,GACnB;4CACN,MAAM,aAAa,SACf,kBAAkB,UAClB;4CACJ,OAAO,IAAI,eACT,6LAAC;gDAEC,WAAW;0DAEV,KAAK,CAAC,EAAE;+CAHJ,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC,EAAE;;;;;4CAMrC,YAAY,MAAM,SAAS;wCAC7B;wCACA,IAAI,YAAY,YAAY,MAAM,EAAE;4CAClC,OAAO,IAAI,CAAC,YAAY,KAAK,CAAC;wCAChC;wCACA,OAAO;oCACT,CAAC;;;;;;;2BA1EI;;;;;;;;;yCAiFf,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;IAIrC;IAEA,MAAM,kCAAkC,CAAC;QACvC,IAAI,CAAC,OAAO,OAAO;QACnB,MAAM,aAAa,MAAM,WAAW;QACpC,MAAM,aAAa,SAAS,cAAc,CAAC,cAAc,IAAI,EAAE;QAC/D,MAAM,kBAAkB,SAAS,cAAc,CAAC,mBAAmB,IAAI,EAAE;QACzE,MAAM,oBACJ,SAAS,cAAc,CAAC,sBAAsB,IAAI,EAAE;QACtD,MAAM,YAAY;eAAI;eAAe;eAAoB;SAAkB;QAC3E,MAAM,eAAe,UAAU,IAAI,CACjC,CAAC,QAAU,MAAM,IAAI,CAAC,WAAW,OAAO;QAE1C,OAAO,eAAe,kBAAkB,aAAa,MAAM,IAAI;IACjE;IAEA,MAAM,kBAAkB;QACtB,qBACE;sBACG,mBACC;0BACE,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAkC;;;;;;8CAChD,6LAAC;oCACC,MAAM;oCACN,QAAO;oCACP,QAAQ;oCACR,SAAS;wCACP,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,WAAW;4CAC3B,MAAM;4CACN,YAAY;gDAAE,QAAQ;4CAAG;wCAC3B;oCACF;oCACA,WAAU;;wCACX;sDACa,6LAAC,iNAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;sCAG7B,6LAAC;4BACC,WAAU;4BACV,KAAK;4BACL,OAAM;4BACN,QAAO;;;;;;;;;;;;8CAKb,6LAAC;gBAAI,WAAU;0BAA0D;;;;;;;IAMjF;IAEA,2DAA2D;IAC3D,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IACA,MAAM,wBAAwB;QAC5B,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,WAAW;YAC3B,MAAM;YACN,YAAY;gBAAE,QAAQ;YAAG;QAC3B;IACF;IAEA,qBACE,6LAAC;;YACE;YAEA,mCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA2B;;;;;;8CACzC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,8HAAA,CAAA,SAAM;4CACL,OAAO;4CACP,WAAU;4CACV,SAAQ;sDAER,cAAA,6LAAC;gDACC,MAAM;gDACN,QAAO;gDACP,KAAI;gDACJ,SAAS;;oDACV;kEACa,6LAAC,yNAAA,CAAA,eAAY;;;;;;;;;;;;;;;;sDAI7B,6LAAC;4CACC,SAAS;gDACP,kBAAkB;gDAClB,qBAAqB;4CACvB;4CACA,WAAU;sDACX;;;;;;;;;;;;;;;;;;wBAKJ,gBAAgB,aACjB,OAAO,IAAI,CAAC,eAAe,SAAS,GAAG,SAAS,kBAC9C;;8CAEE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;;8DACX,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;gDAAgB;gDAC/C,gBAAgB,WAAW;;;;;;;sDAE9B,6LAAC;4CAAE,WAAU;;8DACX,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;gDAAiB;gDAChD,gBAAgB,WAAW;;;;;;;sDAE9B,6LAAC;4CAAE,WAAU;;8DACX,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;gDAAiB;gDAChD,gBAAgB,WAAW;;;;;;;wCAE7B,gBAAgB,WAAW,QAC1B,gBAAgB,WAAW,uBACzB,6LAAC;4CAAE,WAAU;;8DACX,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;gDAAoB;gDACnD,GAAG,gBAAgB,WAAW,KAAK,EAAE,EAAE,gBAAgB,WAAW,OAAO;;;;;;;;;;;;;8CAMlF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;;8DACX,6LAAC;oDAAO,WAAU;8DAAY;;;;;;8DAC9B,6LAAC;oDAAK,WAAU;8DACb,mBAAmB,GAAG,CAAC,CAAC,QAAQ,sBAC/B,6LAAC;4DAEC,OAAO;4DACP,YAAY,kBAAkB;2DAFzB;;;;;;;;;;;;;;;;wCAQZ,gCACC,6LAAC;4CAAE,WAAU;;8DACX,6LAAC;oDAAO,WAAU;8DAAY;;;;;;8DAC9B,6LAAC;oDAAK,WAAU;8DACb,gBAAgB,WAAW,CAAC,cAAc,EAAE,SAAS,IAClD,gBAAgB,WAAW,CAAC,cAAc,CAAC,IACzC,CAAC,OAAO,sBACN,6LAAC;4DAEC,OAAO;4DACP,YAAY,gCACV;2DAHG;;;;oEAQX;;;;;;;;;;;;sDAMV,6LAAC;4CAAE,WAAU;;8DACX,6LAAC;oDAAO,WAAU;8DAAY;;;;;;8DAC9B,6LAAC;oDAAK,WAAU;8DACb,gBAAgB,WAAW,CAAC,mBAAmB,EAAE,SAClD,IACI,gBAAgB,WAAW,CAAC,mBAAmB,EAAE,IAC/C,CAAC,OAAO,sBACN,6LAAC;4DAEC,OAAO;4DACP,YAAY,gCACV;2DAHG;;;;oEAQX;;;;;;;;;;;;sDAKR,6LAAC;4CAAE,WAAU;;8DACX,6LAAC;oDAAO,WAAU;8DAAY;;;;;;8DAC9B,6LAAC;oDAAK,WAAU;8DACb,gBAAgB,WAAW,CAAC,sBAAsB,EAC/C,SAAS,IACT,gBAAgB,WAAW,CACzB,sBACD,EAAE,IAAI,CAAC,OAAO,sBACb,6LAAC;4DAEC,OAAO;4DACP,YAAY,gCACV;2DAHG;;;;oEAOT;;;;;;;;;;;;;;;;;;gCAKT,CAAC,gHAAA,CAAA,iBAAc,GACZ,oBACA,sBAAsB;;2CAE1B,gBAAgB,aAClB,OAAO,IAAI,CAAC,eAAe,SAAS,GAAG,WAAW,kBAClD,6LAAC;4BAAI,WAAU;sCAAkE;;;;;mCAG/E,qBAAqB,0BACvB,6LAAC,yHAAA,CAAA,UAAO;4BAAC,QAAO;;;;;mCACd;;;;;;;;;;;;;;;;;;AAMhB;MAzWM;uCA2WS"}}, {"offset": {"line": 1995, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2001, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/ui/table.tsx"], "sourcesContent": ["import * as React from \"react\";\r\n\r\nimport { cn } from \"@/library/utils\";\r\n\r\nconst Table = React.forwardRef<\r\n  HTMLTableElement,\r\n  React.HTMLAttributes<HTMLTableElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div className=\"relative w-full overflow-auto\">\r\n    <table\r\n      ref={ref}\r\n      className={cn(\"w-full caption-bottom text-sm\", className)}\r\n      {...props}\r\n    />\r\n  </div>\r\n));\r\nTable.displayName = \"Table\";\r\n\r\nconst TableHeader = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <thead ref={ref} className={cn(\"[&_tr]:border-b\", className)} {...props} />\r\n));\r\nTableHeader.displayName = \"TableHeader\";\r\n\r\nconst TableBody = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tbody\r\n    ref={ref}\r\n    className={cn(\"[&_tr:last-child]:border-0\", className)}\r\n    {...props}\r\n  />\r\n));\r\nTableBody.displayName = \"TableBody\";\r\n\r\nconst TableFooter = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tfoot\r\n    ref={ref}\r\n    className={cn(\r\n      \"border-t bg-muted/50 font-medium [&>tr]:last:border-b-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nTableFooter.displayName = \"TableFooter\";\r\n\r\nconst TableRow = React.forwardRef<\r\n  HTMLTableRowElement,\r\n  React.HTMLAttributes<HTMLTableRowElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tr\r\n    ref={ref}\r\n    className={cn(\r\n      \"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nTableRow.displayName = \"TableRow\";\r\n\r\nconst TableHead = React.forwardRef<\r\n  HTMLTableCellElement,\r\n  React.ThHTMLAttributes<HTMLTableCellElement>\r\n>(({ className, ...props }, ref) => (\r\n  <th\r\n    ref={ref}\r\n    className={cn(\r\n      \"h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nTableHead.displayName = \"TableHead\";\r\n\r\nconst TableCell = React.forwardRef<\r\n  HTMLTableCellElement,\r\n  React.TdHTMLAttributes<HTMLTableCellElement>\r\n>(({ className, ...props }, ref) => (\r\n  <td\r\n    ref={ref}\r\n    className={cn(\r\n      \"p-2 align-middle [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nTableCell.displayName = \"TableCell\";\r\n\r\nconst TableCaption = React.forwardRef<\r\n  HTMLTableCaptionElement,\r\n  React.HTMLAttributes<HTMLTableCaptionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <caption\r\n    ref={ref}\r\n    className={cn(\"mt-4 text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n));\r\nTableCaption.displayName = \"TableCaption\";\r\n\r\nexport {\r\n  Table,\r\n  TableHeader,\r\n  TableBody,\r\n  TableFooter,\r\n  TableHead,\r\n  TableRow,\r\n  TableCell,\r\n  TableCaption,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,8JAAM,UAAU,MAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YACC,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;;AAIf,MAAM,WAAW,GAAG;AAEpB,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAM,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAAa,GAAG,KAAK;;;;;;;AAEzE,YAAY,WAAW,GAAG;AAE1B,MAAM,0BAAY,8JAAM,UAAU,OAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,yBAAW,8JAAM,UAAU,OAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;;AAGb,SAAS,WAAW,GAAG;AAEvB,MAAM,0BAAY,8JAAM,UAAU,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0IACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,0BAAY,8JAAM,UAAU,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wFACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,6BAAe,8JAAM,UAAU,QAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;;AAGb,aAAa,WAAW,GAAG"}}, {"offset": {"line": 2133, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2139, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/candidates/WhyFitAction.tsx"], "sourcesContent": ["import { CircleX, Expand, Minimize, Copy } from \"lucide-react\";\r\nimport React, { useState, useEffect, useRef } from \"react\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { Button } from \"../ui/button\";\r\nimport {\r\n  Candidate,\r\n  Vacancy,\r\n  WhyFitReasonPayload,\r\n} from \"@/app/candidates/helper\";\r\nimport { useNotification } from \"@/hooks/useNotification\";\r\nimport Modal from \"@/components/Modal\";\r\nimport AppToolTip from \"../AppToolTip\";\r\nimport Loading from \"../Loading\";\r\nimport { IS_WHY_FIT_EDITABLE } from \"@/api/config\";\r\n\r\nconst WhyFitAction = ({\r\n  candidate,\r\n  setCandidates,\r\n  selectedVacancy,\r\n  existingEdit,\r\n  setExistingEdit,\r\n  candidateEditId,\r\n  setCandidateEditId,\r\n  candidateEditId2,\r\n  setCandidateEditId2,\r\n  setExpandPopupOpen,\r\n}: {\r\n  candidate: Candidate;\r\n  setCandidates?: React.Dispatch<React.SetStateAction<Candidate[]>>;\r\n  selectedVacancy: Vacancy;\r\n  existingEdit: boolean;\r\n  setExistingEdit: React.Dispatch<React.SetStateAction<boolean>>;\r\n  candidateEditId: string;\r\n  setCandidateEditId: React.Dispatch<React.SetStateAction<string>>;\r\n  candidateEditId2: string;\r\n  setCandidateEditId2: React.Dispatch<React.SetStateAction<string>>;\r\n  setExpandPopupOpen: React.Dispatch<React.SetStateAction<boolean>>;\r\n}) => {\r\n  const [showCommentBox, setShowCommentBox] = useState(false);\r\n\r\n  const commentBoxRef = useRef<HTMLDivElement | null>(null);\r\n  const buttonRef = useRef<HTMLDivElement | null>(null);\r\n  const [openAbove, setOpenAbove] = useState(false);\r\n  const [confirmModal, setConfirmModal] = useState(false);\r\n  const { showNotification } = useNotification();\r\n  const [originalComment, setOriginalComment] = useState(\r\n    candidate?.candidate_data?.current_fitness_reason?.reason ?? \"\"\r\n  );\r\n  const [comment, setComment] = useState(\r\n    candidate?.candidate_data?.current_fitness_reason?.reason ?? \"\"\r\n  );\r\n\r\n  const [loading, setLoading] = useState(false);\r\n  const expandedTextareaRef = useRef<HTMLTextAreaElement | null>(null);\r\n\r\n  useEffect(() => {\r\n    if (showCommentBox && expandedTextareaRef.current) {\r\n      const textarea = expandedTextareaRef.current;\r\n      // Wait for DOM to paint\r\n      requestAnimationFrame(() => {\r\n        textarea.focus();\r\n        textarea.setSelectionRange(\r\n          textarea.value.length,\r\n          textarea.value.length\r\n        );\r\n      });\r\n    }\r\n  }, [showCommentBox]);\r\n\r\n  useEffect(() => {\r\n    const decision = candidate?.candidate_data?.current_fitness_reason;\r\n    if (decision) {\r\n      setComment(decision.reason ?? \"\");\r\n      setOriginalComment(decision.reason ?? \"\");\r\n    }\r\n  }, [candidate]);\r\n\r\n  useEffect(() => {\r\n    if (comment && comment !== originalComment) {\r\n      setExistingEdit(true);\r\n    } else {\r\n      setExistingEdit(false);\r\n    }\r\n  }, [comment]);\r\n\r\n  useEffect(() => {\r\n    if (showCommentBox && buttonRef.current) {\r\n      const buttonRect = buttonRef.current.getBoundingClientRect();\r\n      const windowHeight = window.innerHeight;\r\n      setOpenAbove(buttonRect.bottom + 220 > windowHeight);\r\n    }\r\n  }, [showCommentBox]);\r\n\r\n  const handleReviewClick = () => {\r\n    setShowCommentBox((prev) => {\r\n      if (!prev) {\r\n        setOriginalComment(\r\n          candidate?.candidate_data?.current_fitness_reason?.reason ?? \"\"\r\n        );\r\n      }\r\n      return !prev;\r\n    });\r\n  };\r\n\r\n  const handleCopy = () => {\r\n    if (!comment) {\r\n      showNotification(\"Nothing to copy.\", \"warning\");\r\n      return;\r\n    }\r\n    navigator.clipboard\r\n      .writeText(comment)\r\n      .then(() => {\r\n        showNotification(\"Copied to clipboard!\", \"success\");\r\n      })\r\n      .catch(() => {\r\n        showNotification(\"Failed to copy.\", \"error\");\r\n      });\r\n  };\r\n\r\n  const handleSend = async () => {\r\n    setConfirmModal(false);\r\n    setExistingEdit(false);\r\n    setShowCommentBox(false);\r\n    setCandidateEditId(\"\");\r\n    setCandidateEditId2(\"\");\r\n    setLoading(true);\r\n    const requestObj: WhyFitReasonPayload = {\r\n      candidate_contact_id: candidate.candidate_contactid,\r\n      vacancy_refno: selectedVacancy?.refno,\r\n      fitness_reason_text: comment,\r\n      author_email: selectedVacancy.locked_by ?? \"\",\r\n    };\r\n    try {\r\n      let response: any = await fetch(\"/api/candidates/fitness_reason\", {\r\n        method: \"POST\",\r\n        headers: {\r\n          Accept: \"application/json\",\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n        body: JSON.stringify(requestObj),\r\n      });\r\n      response = await response.json();\r\n      if (response?.status_code === 200) {\r\n        showNotification(\"Comment submitted successfully!\", \"success\");\r\n        if (setCandidates) {\r\n          setCandidates((prev) =>\r\n            prev.map((c) =>\r\n              c.candidate_contactid === candidate.candidate_contactid\r\n                ? {\r\n                    ...c,\r\n                    candidate_data: {\r\n                      ...c.candidate_data,\r\n                      current_fitness_reason: {\r\n                        reason: comment,\r\n                        author:\r\n                          candidate.candidate_data.current_fitness_reason\r\n                            ?.author || \"\",\r\n                        timestamp:\r\n                          candidate.candidate_data.current_fitness_reason\r\n                            ?.timestamp || \"\",\r\n                      },\r\n                    },\r\n                  }\r\n                : c\r\n            )\r\n          );\r\n        }\r\n      } else {\r\n        showNotification(\r\n          \"Error submitting comment. Please try again.\",\r\n          \"error\"\r\n        );\r\n      }\r\n      setLoading(false);\r\n    } catch (error) {\r\n      setLoading(false);\r\n      console.error(error);\r\n      showNotification(\"Error submitting comment. Please try again.\", \"error\");\r\n    }\r\n  };\r\n\r\n  const handleSave = () => {\r\n    if (comment.length > 0) {\r\n      handleSend();\r\n    } else {\r\n      setConfirmModal(true);\r\n    }\r\n  };\r\n\r\n  function cancelRevertChanges() {\r\n    setCandidateEditId2(\"\");\r\n  }\r\n\r\n  function confirmDiscardChanges() {\r\n    setExistingEdit(false);\r\n    setShowCommentBox(false);\r\n    setComment(originalComment);\r\n    setCandidateEditId(\"\");\r\n    setCandidateEditId2(\"\");\r\n  }\r\n\r\n  useEffect(() => {\r\n    if (showCommentBox) {\r\n      document.body.style.overflow = \"hidden\";\r\n    } else {\r\n      document.body.style.overflow = \"auto\";\r\n    }\r\n    setExpandPopupOpen(showCommentBox);\r\n\r\n    return () => {\r\n      document.body.style.overflow = \"auto\"; // Clean up on unmount\r\n    };\r\n  }, [showCommentBox]);\r\n\r\n  const isEditable =\r\n    IS_WHY_FIT_EDITABLE === \"false\"\r\n      ? !(IS_WHY_FIT_EDITABLE === \"false\")\r\n      : !selectedVacancy?.is_locked && selectedVacancy?.locked_by;\r\n\r\n  return (\r\n    <div className=\"flex flex-col items-center w-full\" ref={buttonRef}>\r\n      <div className=\"flex items-center space-x-2 relative w-full\">\r\n        <motion.div className=\"cursor-pointer w-full\">\r\n          <textarea\r\n            cols={3}\r\n            value={comment}\r\n            onChange={(e) => {\r\n              setCandidateEditId2(candidate.candidate_contactid);\r\n              if (\r\n                existingEdit &&\r\n                candidateEditId !== candidate.candidate_contactid\r\n              ) {\r\n                return;\r\n              }\r\n              setCandidateEditId(candidate.candidate_contactid);\r\n              setComment(e.target.value);\r\n            }}\r\n            className=\"w-full border border-gray-300 rounded-lg p-2 my-1\"\r\n            disabled={!isEditable}\r\n          />\r\n          <div className=\"flex gap-1.5 mb-1\">\r\n            {comment !== originalComment && (\r\n              <>\r\n                <Button\r\n                  onClick={() => confirmDiscardChanges()}\r\n                  className=\"bg-gray-800 text-white py-1 rounded hover:bg-gray-900 transition px-0 ml-0 h-6 w-11 text-[10px]\"\r\n                >\r\n                  Discard\r\n                </Button>\r\n\r\n                <Button\r\n                  size=\"sm\"\r\n                  onClick={() => handleSave()}\r\n                  disabled={!isEditable || comment === originalComment}\r\n                  className=\"bg-gray-800 text-white py-1 rounded hover:bg-gray-900 transition px-0 ml-0 h-6 w-11 text-[10px]\"\r\n                >\r\n                  {loading ? <Loading /> : \"Save\"}\r\n                </Button>\r\n              </>\r\n            )}\r\n            <div className=\"absolute -top-0.5 -right-0.5\">\r\n              <AppToolTip\r\n                text=\"Click To Expand\"\r\n                header={\r\n                  <Expand\r\n                    onClick={() => handleReviewClick()}\r\n                    size={8}\r\n                    className=\"bg-gray-800 text-white py-1 rounded hover:bg-gray-900 transition px-0 ml-0 h-4 w-4 text-[6px]\"\r\n                  />\r\n                }\r\n              />\r\n            </div>\r\n          </div>\r\n        </motion.div>\r\n      </div>\r\n      <AnimatePresence>\r\n        {showCommentBox && (\r\n          <div className=\"fixed top-0 left-0 w-full h-full z-[999] bg-[#33333320] flex justify-center items-center\">\r\n            <motion.div\r\n              ref={commentBoxRef}\r\n              initial={{ opacity: 0, scale: 0.95 }}\r\n              animate={{ opacity: 1, scale: 1 }}\r\n              exit={{ opacity: 0, scale: 0.95 }}\r\n              transition={{ duration: 0.25 }}\r\n              className=\"fixed top-[40vh] left-[45vw] w-[500px] h-[300px] bg-white shadow-lg rounded-lg p-4 border\"\r\n            >\r\n              <div className=\"flex justify-between items-center mb-1\">\r\n                <p className=\"text-sm font-semibold\">Why-Fit</p>\r\n                <div className=\"flex gap-2\">\r\n                  <AppToolTip\r\n                    text=\"Copy Text\"\r\n                    header={\r\n                      <Copy\r\n                        onClick={handleCopy}\r\n                        className=\"bg-gray-800 text-white p-1 rounded hover:bg-gray-900 transition size-5\"\r\n                      />\r\n                    }\r\n                  />\r\n                  <AppToolTip\r\n                    text=\"Click To Minimize\"\r\n                    header={\r\n                      <Minimize\r\n                        onClick={() => setShowCommentBox(false)}\r\n                        className=\"bg-gray-800 text-white p-1 rounded hover:bg-gray-900 transition size-5\"\r\n                      />\r\n                    }\r\n                  />\r\n                </div>\r\n              </div>\r\n\r\n              <textarea\r\n                ref={expandedTextareaRef}\r\n                autoFocus\r\n                disabled={!isEditable}\r\n                className={`w-full ${\r\n                  isEditable ? \"h-[200px]\" : \"h-[235px]\"\r\n                } border p-2 rounded resize-none focus:outline-none focus:ring-2 focus:ring-blue-500`}\r\n                placeholder=\"Share your thoughts...\"\r\n                value={comment}\r\n                onChange={(e) => setComment(e.target.value)}\r\n              />\r\n\r\n              {isEditable && (\r\n                <div className=\"flex justify-end gap-1.5 mt-2\">\r\n                  <Button\r\n                    onClick={confirmDiscardChanges}\r\n                    className=\"bg-gray-800 text-white py-1 rounded hover:bg-gray-900 transition\"\r\n                    size=\"sm\"\r\n                  >\r\n                    Discard\r\n                  </Button>\r\n                  <Button\r\n                    onClick={handleSave}\r\n                    disabled={comment === originalComment}\r\n                    className=\"bg-gray-800 text-white py-1 rounded hover:bg-gray-900 transition\"\r\n                    size=\"sm\"\r\n                  >\r\n                    Save {loading && <Loading />}\r\n                  </Button>\r\n                </div>\r\n              )}\r\n            </motion.div>\r\n          </div>\r\n        )}\r\n      </AnimatePresence>\r\n\r\n      <AnimatePresence>\r\n        {confirmModal && (\r\n          <motion.div\r\n            ref={commentBoxRef}\r\n            initial={{ opacity: 0, y: openAbove ? -10 : 10, x: -10 }}\r\n            animate={{ opacity: 1, y: 0, x: -50 }}\r\n            exit={{ opacity: 0, y: openAbove ? -10 : 10 }}\r\n            transition={{ duration: 0.3 }}\r\n            className={`absolute z-[9] ${\r\n              openAbove ? \"bottom-10\" : \"top-10\"\r\n            } w-[300px] h-[170px] bg-white shadow-lg rounded-lg p-4 border`}\r\n          >\r\n            <div className=\"flex justify-between items-center mb-1\">\r\n              <p className=\"text-sm font-semibold\">Please confirm</p>\r\n              <CircleX\r\n                size={14}\r\n                className=\"cursor-pointer\"\r\n                onClick={() => setConfirmModal(false)}\r\n              />\r\n            </div>\r\n            <p className=\"text-sm font-semibold\">\r\n              Comments are empty. Please confirm to submit.\r\n            </p>\r\n\r\n            {/* {viewState ? null : ( */}\r\n            <div className=\"flex gap-4\">\r\n              <Button\r\n                onClick={() => handleSend()}\r\n                className=\"w-full bg-gray-800 text-white py-1 rounded hover:bg-gray-900 transition px-0 ml-0\"\r\n              >\r\n                Confirm\r\n              </Button>\r\n              <Button\r\n                onClick={() => setConfirmModal(false)}\r\n                className=\"w-full bg-gray-800 text-white py-1 rounded hover:bg-gray-900 transition px-0 ml-0\"\r\n              >\r\n                Cancel\r\n              </Button>\r\n            </div>\r\n          </motion.div>\r\n        )}\r\n      </AnimatePresence>\r\n\r\n      <Modal\r\n        isOpen={Boolean(\r\n          candidateEditId &&\r\n            candidateEditId2 &&\r\n            candidateEditId === candidate.candidate_contactid &&\r\n            candidateEditId2 !== candidate.candidate_contactid\r\n        )}\r\n        onClose={() => setCandidateEditId2(\"\")}\r\n        title=\"Changes not saved!\"\r\n      >\r\n        <p className=\"mb-5 h-20\">\r\n          Your recent change to the why fit field has not been saved yet. Choose\r\n          &lsquo;Cancel&lsquo; to continue text changes or &lsquo;Discard\r\n          Chanages&lsquo; to revert or &lsquo;Save&lsquo; your changes.\r\n        </p>\r\n        <div className=\"flex justify-end gap-1.5\">\r\n          <Button\r\n            onClick={() => {\r\n              handleSave();\r\n            }}\r\n            className=\"rounded-md\"\r\n            style={{\r\n              color: \"green\",\r\n              border: \"1px solid green\",\r\n              background: \"#fff\",\r\n            }}\r\n          >\r\n            Save {loading && <Loading />}\r\n          </Button>\r\n          <Button\r\n            onClick={() => {\r\n              confirmDiscardChanges();\r\n            }}\r\n            className=\"rounded-md\"\r\n            style={{\r\n              color: \"#e95151\",\r\n              border: \"1px solid #e95151\",\r\n              background: \"#fff\",\r\n            }}\r\n          >\r\n            Discard Changes\r\n          </Button>\r\n          <Button\r\n            onClick={() => {\r\n              cancelRevertChanges();\r\n            }}\r\n            style={{ background: \"white\" }}\r\n            className=\"text-gray-900 border border-gray-900 rounded-md\"\r\n          >\r\n            Cancel\r\n          </Button>\r\n        </div>\r\n      </Modal>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default WhyFitAction;\r\n"], "names": [], "mappings": ";;;;AACA;AAEA;AAMA;AACA;AACA;AACA;AACA;AAXA;AAFA;AAEA;AAFA;AAAA;AAAA;;;;;;;;;;;;AAeA,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,aAAa,EACb,eAAe,EACf,YAAY,EACZ,eAAe,EACf,eAAe,EACf,kBAAkB,EAClB,gBAAgB,EAChB,mBAAmB,EACnB,kBAAkB,EAYnB;;IACC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IACpD,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IAChD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,kBAAe,AAAD;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACnD,WAAW,gBAAgB,wBAAwB,UAAU;IAE/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACnC,WAAW,gBAAgB,wBAAwB,UAAU;IAG/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAA8B;IAE/D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,kBAAkB,oBAAoB,OAAO,EAAE;gBACjD,MAAM,WAAW,oBAAoB,OAAO;gBAC5C,wBAAwB;gBACxB;8CAAsB;wBACpB,SAAS,KAAK;wBACd,SAAS,iBAAiB,CACxB,SAAS,KAAK,CAAC,MAAM,EACrB,SAAS,KAAK,CAAC,MAAM;oBAEzB;;YACF;QACF;iCAAG;QAAC;KAAe;IAEnB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,WAAW,WAAW,gBAAgB;YAC5C,IAAI,UAAU;gBACZ,WAAW,SAAS,MAAM,IAAI;gBAC9B,mBAAmB,SAAS,MAAM,IAAI;YACxC;QACF;iCAAG;QAAC;KAAU;IAEd,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,WAAW,YAAY,iBAAiB;gBAC1C,gBAAgB;YAClB,OAAO;gBACL,gBAAgB;YAClB;QACF;iCAAG;QAAC;KAAQ;IAEZ,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,kBAAkB,UAAU,OAAO,EAAE;gBACvC,MAAM,aAAa,UAAU,OAAO,CAAC,qBAAqB;gBAC1D,MAAM,eAAe,OAAO,WAAW;gBACvC,aAAa,WAAW,MAAM,GAAG,MAAM;YACzC;QACF;iCAAG;QAAC;KAAe;IAEnB,MAAM,oBAAoB;QACxB,kBAAkB,CAAC;YACjB,IAAI,CAAC,MAAM;gBACT,mBACE,WAAW,gBAAgB,wBAAwB,UAAU;YAEjE;YACA,OAAO,CAAC;QACV;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,SAAS;YACZ,iBAAiB,oBAAoB;YACrC;QACF;QACA,UAAU,SAAS,CAChB,SAAS,CAAC,SACV,IAAI,CAAC;YACJ,iBAAiB,wBAAwB;QAC3C,GACC,KAAK,CAAC;YACL,iBAAiB,mBAAmB;QACtC;IACJ;IAEA,MAAM,aAAa;QACjB,gBAAgB;QAChB,gBAAgB;QAChB,kBAAkB;QAClB,mBAAmB;QACnB,oBAAoB;QACpB,WAAW;QACX,MAAM,aAAkC;YACtC,sBAAsB,UAAU,mBAAmB;YACnD,eAAe,iBAAiB;YAChC,qBAAqB;YACrB,cAAc,gBAAgB,SAAS,IAAI;QAC7C;QACA,IAAI;YACF,IAAI,WAAgB,MAAM,MAAM,kCAAkC;gBAChE,QAAQ;gBACR,SAAS;oBACP,QAAQ;oBACR,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YACA,WAAW,MAAM,SAAS,IAAI;YAC9B,IAAI,UAAU,gBAAgB,KAAK;gBACjC,iBAAiB,mCAAmC;gBACpD,IAAI,eAAe;oBACjB,cAAc,CAAC,OACb,KAAK,GAAG,CAAC,CAAC,IACR,EAAE,mBAAmB,KAAK,UAAU,mBAAmB,GACnD;gCACE,GAAG,CAAC;gCACJ,gBAAgB;oCACd,GAAG,EAAE,cAAc;oCACnB,wBAAwB;wCACtB,QAAQ;wCACR,QACE,UAAU,cAAc,CAAC,sBAAsB,EAC3C,UAAU;wCAChB,WACE,UAAU,cAAc,CAAC,sBAAsB,EAC3C,aAAa;oCACrB;gCACF;4BACF,IACA;gBAGV;YACF,OAAO;gBACL,iBACE,+CACA;YAEJ;YACA,WAAW;QACb,EAAE,OAAO,OAAO;YACd,WAAW;YACX,QAAQ,KAAK,CAAC;YACd,iBAAiB,+CAA+C;QAClE;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,QAAQ,MAAM,GAAG,GAAG;YACtB;QACF,OAAO;YACL,gBAAgB;QAClB;IACF;IAEA,SAAS;QACP,oBAAoB;IACtB;IAEA,SAAS;QACP,gBAAgB;QAChB,kBAAkB;QAClB,WAAW;QACX,mBAAmB;QACnB,oBAAoB;IACtB;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,gBAAgB;gBAClB,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC,OAAO;gBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC;YACA,mBAAmB;YAEnB;0CAAO;oBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,QAAQ,sBAAsB;gBAC/D;;QACF;iCAAG;QAAC;KAAe;IAEnB,MAAM,aACJ,gHAAA,CAAA,sBAAmB,KAAK,UACpB,CAAC,CAAC,gHAAA,CAAA,sBAAmB,KAAK,OAAO,IACjC,CAAC,iBAAiB,aAAa,iBAAiB;IAEtD,qBACE,6LAAC;QAAI,WAAU;QAAoC,KAAK;;0BACtD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAAC,WAAU;;sCACpB,6LAAC;4BACC,MAAM;4BACN,OAAO;4BACP,UAAU,CAAC;gCACT,oBAAoB,UAAU,mBAAmB;gCACjD,IACE,gBACA,oBAAoB,UAAU,mBAAmB,EACjD;oCACA;gCACF;gCACA,mBAAmB,UAAU,mBAAmB;gCAChD,WAAW,EAAE,MAAM,CAAC,KAAK;4BAC3B;4BACA,WAAU;4BACV,UAAU,CAAC;;;;;;sCAEb,6LAAC;4BAAI,WAAU;;gCACZ,YAAY,iCACX;;sDACE,6LAAC,8HAAA,CAAA,SAAM;4CACL,SAAS,IAAM;4CACf,WAAU;sDACX;;;;;;sDAID,6LAAC,8HAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAS,IAAM;4CACf,UAAU,CAAC,cAAc,YAAY;4CACrC,WAAU;sDAET,wBAAU,6LAAC,yHAAA,CAAA,UAAO;;;;uDAAM;;;;;;;;8CAI/B,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,4HAAA,CAAA,UAAU;wCACT,MAAK;wCACL,sBACE,6LAAC,yMAAA,CAAA,SAAM;4CACL,SAAS,IAAM;4CACf,MAAM;4CACN,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQxB,6LAAC,4LAAA,CAAA,kBAAe;0BACb,gCACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,KAAK;wBACL,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAK;wBACnC,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAE;wBAChC,MAAM;4BAAE,SAAS;4BAAG,OAAO;wBAAK;wBAChC,YAAY;4BAAE,UAAU;wBAAK;wBAC7B,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;kDACrC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,4HAAA,CAAA,UAAU;gDACT,MAAK;gDACL,sBACE,6LAAC,qMAAA,CAAA,OAAI;oDACH,SAAS;oDACT,WAAU;;;;;;;;;;;0DAIhB,6LAAC,4HAAA,CAAA,UAAU;gDACT,MAAK;gDACL,sBACE,6LAAC,6MAAA,CAAA,WAAQ;oDACP,SAAS,IAAM,kBAAkB;oDACjC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAOpB,6LAAC;gCACC,KAAK;gCACL,SAAS;gCACT,UAAU,CAAC;gCACX,WAAW,CAAC,OAAO,EACjB,aAAa,cAAc,YAC5B,mFAAmF,CAAC;gCACrF,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;;;;;;4BAG3C,4BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,8HAAA,CAAA,SAAM;wCACL,SAAS;wCACT,WAAU;wCACV,MAAK;kDACN;;;;;;kDAGD,6LAAC,8HAAA,CAAA,SAAM;wCACL,SAAS;wCACT,UAAU,YAAY;wCACtB,WAAU;wCACV,MAAK;;4CACN;4CACO,yBAAW,6LAAC,yHAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASvC,6LAAC,4LAAA,CAAA,kBAAe;0BACb,8BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,KAAK;oBACL,SAAS;wBAAE,SAAS;wBAAG,GAAG,YAAY,CAAC,KAAK;wBAAI,GAAG,CAAC;oBAAG;oBACvD,SAAS;wBAAE,SAAS;wBAAG,GAAG;wBAAG,GAAG,CAAC;oBAAG;oBACpC,MAAM;wBAAE,SAAS;wBAAG,GAAG,YAAY,CAAC,KAAK;oBAAG;oBAC5C,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAW,CAAC,eAAe,EACzB,YAAY,cAAc,SAC3B,6DAA6D,CAAC;;sCAE/D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;8CACrC,6LAAC,+MAAA,CAAA,UAAO;oCACN,MAAM;oCACN,WAAU;oCACV,SAAS,IAAM,gBAAgB;;;;;;;;;;;;sCAGnC,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;sCAKrC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,8HAAA,CAAA,SAAM;oCACL,SAAS,IAAM;oCACf,WAAU;8CACX;;;;;;8CAGD,6LAAC,8HAAA,CAAA,SAAM;oCACL,SAAS,IAAM,gBAAgB;oCAC/B,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAQT,6LAAC,uHAAA,CAAA,UAAK;gBACJ,QAAQ,QACN,mBACE,oBACA,oBAAoB,UAAU,mBAAmB,IACjD,qBAAqB,UAAU,mBAAmB;gBAEtD,SAAS,IAAM,oBAAoB;gBACnC,OAAM;;kCAEN,6LAAC;wBAAE,WAAU;kCAAY;;;;;;kCAKzB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,8HAAA,CAAA,SAAM;gCACL,SAAS;oCACP;gCACF;gCACA,WAAU;gCACV,OAAO;oCACL,OAAO;oCACP,QAAQ;oCACR,YAAY;gCACd;;oCACD;oCACO,yBAAW,6LAAC,yHAAA,CAAA,UAAO;;;;;;;;;;;0CAE3B,6LAAC,8HAAA,CAAA,SAAM;gCACL,SAAS;oCACP;gCACF;gCACA,WAAU;gCACV,OAAO;oCACL,OAAO;oCACP,QAAQ;oCACR,YAAY;gCACd;0CACD;;;;;;0CAGD,6LAAC,8HAAA,CAAA,SAAM;gCACL,SAAS;oCACP;gCACF;gCACA,OAAO;oCAAE,YAAY;gCAAQ;gCAC7B,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;AAOX;GA7aM;;QA6ByB,4HAAA,CAAA,kBAAe;;;KA7BxC;uCA+aS"}}, {"offset": {"line": 2766, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2772, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/Button.tsx"], "sourcesContent": ["import { ReactNode } from \"react\";\r\n\r\ninterface ButtonProps {\r\n  onClick: () => void;\r\n  children: ReactNode;\r\n  disabled?: boolean;\r\n  loading?: boolean;\r\n  className?: string;\r\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n  style?: any;\r\n}\r\n\r\nconst Button = ({\r\n  onClick,\r\n  children,\r\n  disabled,\r\n  loading,\r\n  className,\r\n  style,\r\n}: ButtonProps) => {\r\n  return (\r\n    <button\r\n      onClick={onClick}\r\n      style={style}\r\n      className={`flex justify-center ml-4 px-4 py-2 bg-gray-800 text-white rounded-md hover:bg-gray-900 disabled:bg-gray-600 ${className}`}\r\n      disabled={disabled}\r\n    >\r\n      {children}\r\n      {loading && (\r\n        <svg\r\n          className=\"ml-2 mt-0.5 size-5 animate-spin text-white\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n          fill=\"none\"\r\n          viewBox=\"0 0 24 24\"\r\n        >\r\n          <circle\r\n            className=\"opacity-25\"\r\n            cx=\"12\"\r\n            cy=\"12\"\r\n            r=\"10\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"4\"\r\n          ></circle>\r\n          <path\r\n            className=\"opacity-75\"\r\n            fill=\"currentColor\"\r\n            d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\r\n          ></path>\r\n        </svg>\r\n      )}\r\n    </button>\r\n  );\r\n};\r\n\r\nexport default Button;\r\n"], "names": [], "mappings": ";;;;;AAYA,MAAM,SAAS,CAAC,EACd,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,SAAS,EACT,KAAK,EACO;IACZ,qBACE,6LAAC;QACC,SAAS;QACT,OAAO;QACP,WAAW,CAAC,4GAA4G,EAAE,WAAW;QACrI,UAAU;;YAET;YACA,yBACC,6LAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;;;;;;;AAMd;KAxCM;uCA0CS"}}, {"offset": {"line": 2832, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2838, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/candidates/DiscardModal.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport Button from \"../Button\";\r\nimport Modal from \"../Modal\";\r\n\r\ninterface DiscardModalProps {\r\n  isOpenDiscardModal: boolean;\r\n  cancelTabChange: () => void;\r\n  confirmTabChange: () => void;\r\n  loadingPost?: boolean;\r\n  saveChanges?: any;\r\n  message?: string;\r\n  title?: string;\r\n  confirmButtonText?: string;\r\n}\r\n\r\nconst DiscardModal = (props: DiscardModalProps) => {\r\n  const {\r\n    isOpenDiscardModal,\r\n    loadingPost,\r\n    cancelTabChange,\r\n    confirmTabChange,\r\n    saveChanges,\r\n    message,\r\n    title,\r\n    confirmButtonText,\r\n  } = props;\r\n  return (\r\n    <Modal\r\n      isOpen={isOpenDiscardModal}\r\n      onClose={() => cancelTabChange()}\r\n      title={title ? title : \"Changes not saved!\"}\r\n    >\r\n      <p className=\"mb-5 h-20\">\r\n        {message\r\n          ? message\r\n          : \"Your changes haven’t been saved yet. Choose ‘Discard Changes‘ to revert or ‘Cancel‘ to go back.\"}\r\n      </p>\r\n      <div className=\"flex justify-end\">\r\n        {saveChanges ? (\r\n          <Button\r\n            onClick={() => {\r\n              saveChanges();\r\n            }}\r\n            className=\"rounded-md\"\r\n            style={{\r\n              color: \"green\",\r\n              border: \"1px solid green\",\r\n              background: \"#fff\",\r\n            }}\r\n          >\r\n            Save\r\n          </Button>\r\n        ) : null}\r\n        <Button\r\n          onClick={() => {\r\n            confirmTabChange();\r\n          }}\r\n          className=\"\"\r\n          style={{\r\n            color: confirmButtonText ? \"#16a34a \" : \"#e95151\",\r\n            border: `1px solid ${confirmButtonText ? \"#16a34a \" : \"#e95151\"}`,\r\n            background: \"#fff\",\r\n          }}\r\n        >\r\n          {confirmButtonText ?? \"Discard Changes\"}\r\n        </Button>\r\n        <Button\r\n          onClick={() => {\r\n            cancelTabChange();\r\n          }}\r\n          style={{ background: \"#222\" }}\r\n          className=\"bg-gray-900 hover:bg-gray-950\"\r\n          loading={loadingPost}\r\n        >\r\n          Cancel\r\n        </Button>\r\n      </div>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport default DiscardModal;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAaA,MAAM,eAAe,CAAC;IACpB,MAAM,EACJ,kBAAkB,EAClB,WAAW,EACX,eAAe,EACf,gBAAgB,EAChB,WAAW,EACX,OAAO,EACP,KAAK,EACL,iBAAiB,EAClB,GAAG;IACJ,qBACE,6LAAC,uHAAA,CAAA,UAAK;QACJ,QAAQ;QACR,SAAS,IAAM;QACf,OAAO,QAAQ,QAAQ;;0BAEvB,6LAAC;gBAAE,WAAU;0BACV,UACG,UACA;;;;;;0BAEN,6LAAC;gBAAI,WAAU;;oBACZ,4BACC,6LAAC,wHAAA,CAAA,UAAM;wBACL,SAAS;4BACP;wBACF;wBACA,WAAU;wBACV,OAAO;4BACL,OAAO;4BACP,QAAQ;4BACR,YAAY;wBACd;kCACD;;;;;+BAGC;kCACJ,6LAAC,wHAAA,CAAA,UAAM;wBACL,SAAS;4BACP;wBACF;wBACA,WAAU;wBACV,OAAO;4BACL,OAAO,oBAAoB,aAAa;4BACxC,QAAQ,CAAC,UAAU,EAAE,oBAAoB,aAAa,WAAW;4BACjE,YAAY;wBACd;kCAEC,qBAAqB;;;;;;kCAExB,6LAAC,wHAAA,CAAA,UAAM;wBACL,SAAS;4BACP;wBACF;wBACA,OAAO;4BAAE,YAAY;wBAAO;wBAC5B,WAAU;wBACV,SAAS;kCACV;;;;;;;;;;;;;;;;;;AAMT;KAhEM;uCAkES"}}, {"offset": {"line": 2932, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2938, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/utils/utils.ts"], "sourcesContent": ["import { EntitlementMap } from \"@/context/EntitlementContext\";\r\nimport moment from \"moment-timezone\";\r\nimport { isADLogin } from \"@/api/config\"; // Adjust the import path as necessary\r\n\r\nexport const unFormattedDateWithBrowserTimezone = (\r\n  val: string,\r\n  tz: string = \"America/Los_Angeles\" // Default to PST\r\n) => {\r\n  const utcDate = moment.utc(val); // Convert input to UTC\r\n  const formattedPSTDateTime = utcDate.tz(tz).format(\"YYYY-MM-DD (hh:mm A)\"); // Convert to PST and format with AM/PM\r\n\r\n  return formattedPSTDateTime;\r\n};\r\n\r\nexport const unFormattedDateWithBrowserTimezoneInDDMMYY = (\r\n  val: string,\r\n  tz: string = \"America/Los_Angeles\" // Default to PST\r\n): string => {\r\n  if (!moment(val).isValid()) {\r\n    return \"Invalid Date\";\r\n  }\r\n\r\n  const utcDate = moment.utc(val); // Convert input to UTC\r\n  const formattedPSTDateTime = utcDate.tz(tz).format(\"MM-DD-YYYY\"); // Convert to PST and format\r\n\r\n  return formattedPSTDateTime;\r\n};\r\nexport const getInitials = (name: string) => {\r\n  if (!name) return \"\";\r\n  return name\r\n    .split(\" \") // Split the name into parts by spaces\r\n    .map((part) => part.charAt(0).toUpperCase()) // Take the first letter of each part and capitalize it\r\n    .join(\"\"); // Join the initials together\r\n};\r\n\r\n// utils/entitlementUtils.ts (or inline)\r\nexport const getEntitlementKeyFromTitle = (\r\n  title: string\r\n): string | undefined => {\r\n  switch (title) {\r\n    case \"Vacancy\":\r\n      return \"Vacancy\";\r\n    case \"Workforce Readiness Index\":\r\n      return \"Work_force_Index\";\r\n    case \"Sub-Category Library\":\r\n      return \"Sub_Catregory\";\r\n    default:\r\n      return undefined;\r\n  }\r\n};\r\n\r\nexport const isUserEntitled = (\r\n  entitlementKey: string | undefined,\r\n  entitlement: EntitlementMap\r\n): boolean => {\r\n  const isADLoginValue: boolean = isADLogin();\r\n  if (!isADLoginValue || !entitlementKey) {\r\n    return true; // Default to true if not AD login or entitlement key is undefined\r\n  }\r\n  return !!entitlement[entitlementKey];\r\n};\r\n\r\nexport function getEnvType(envCode: string): \"sandbox\" | \"prod\" | \"uat\" {\r\n  const code = envCode.toLowerCase();\r\n  if ([\"qa\", \"dv\", \"sb\"].includes(code)) {\r\n    return \"sandbox\";\r\n  }\r\n  if (code === \"ua\") {\r\n    return \"uat\";\r\n  }\r\n  if (code === \"prod\") {\r\n    return \"prod\";\r\n  }\r\n  // Optionally, handle unknown codes\r\n  return \"sandbox\";\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AACA;AACA,kMAA0C,sCAAsC;;;AAEzE,MAAM,qCAAqC,CAChD,KACA,KAAa,sBAAsB,iBAAiB;AAAlB;IAElC,MAAM,UAAU,8IAAA,CAAA,UAAM,CAAC,GAAG,CAAC,MAAM,uBAAuB;IACxD,MAAM,uBAAuB,QAAQ,EAAE,CAAC,IAAI,MAAM,CAAC,yBAAyB,uCAAuC;IAEnH,OAAO;AACT;AAEO,MAAM,6CAA6C,CACxD,KACA,KAAa,sBAAsB,iBAAiB;AAAlB;IAElC,IAAI,CAAC,CAAA,GAAA,8IAAA,CAAA,UAAM,AAAD,EAAE,KAAK,OAAO,IAAI;QAC1B,OAAO;IACT;IAEA,MAAM,UAAU,8IAAA,CAAA,UAAM,CAAC,GAAG,CAAC,MAAM,uBAAuB;IACxD,MAAM,uBAAuB,QAAQ,EAAE,CAAC,IAAI,MAAM,CAAC,eAAe,4BAA4B;IAE9F,OAAO;AACT;AACO,MAAM,cAAc,CAAC;IAC1B,IAAI,CAAC,MAAM,OAAO;IAClB,OAAO,KACJ,KAAK,CAAC,KAAK,sCAAsC;KACjD,GAAG,CAAC,CAAC,OAAS,KAAK,MAAM,CAAC,GAAG,WAAW,IAAI,uDAAuD;KACnG,IAAI,CAAC,KAAK,6BAA6B;AAC5C;AAGO,MAAM,6BAA6B,CACxC;IAEA,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,MAAM,iBAAiB,CAC5B,gBACA;IAEA,MAAM,iBAA0B,CAAA,GAAA,gHAAA,CAAA,YAAS,AAAD;IACxC,IAAI,CAAC,kBAAkB,CAAC,gBAAgB;QACtC,OAAO,MAAM,kEAAkE;IACjF;IACA,OAAO,CAAC,CAAC,WAAW,CAAC,eAAe;AACtC;AAEO,SAAS,WAAW,OAAe;IACxC,MAAM,OAAO,QAAQ,WAAW;IAChC,IAAI;QAAC;QAAM;QAAM;KAAK,CAAC,QAAQ,CAAC,OAAO;QACrC,OAAO;IACT;IACA,IAAI,SAAS,MAAM;QACjB,OAAO;IACT;IACA,IAAI,SAAS,QAAQ;QACnB,OAAO;IACT;IACA,mCAAmC;IACnC,OAAO;AACT"}}, {"offset": {"line": 3011, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3017, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/candidates/ThumbAction.tsx"], "sourcesContent": ["import { ThumbsDown, Thum<PERSON>Up, CircleX, Eye } from \"lucide-react\";\r\nimport React, { useState, useEffect, useRef } from \"react\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { But<PERSON> } from \"../ui/button\";\r\nimport { Candidate, RecruiterReview, Vacancy } from \"@/app/candidates/helper\";\r\nimport { useNotification } from \"@/hooks/useNotification\";\r\nimport AppToolTip from \"../AppToolTip\";\r\nimport DiscardModal from \"./DiscardModal\";\r\nimport { unFormattedDateWithBrowserTimezoneInDDMMYY } from \"@/utils/utils\";\r\n\r\ntype ReviewState = \"like\" | \"dislike\" | \"maybe\" | null;\r\n\r\nconst ThumbAction = ({\r\n  candidate,\r\n  candidates,\r\n  setCandidates,\r\n  vacancyId,\r\n  vacancyCandidates,\r\n  setVacancyCandidates,\r\n  vacancyRefNo,\r\n  selectedVacancy,\r\n  IS_LOCK_FEATURE_DISABLED,\r\n  mercuryPortal,\r\n}: {\r\n  candidate: Candidate;\r\n  candidates: Candidate[];\r\n  setCandidates: React.Dispatch<React.SetStateAction<Candidate[]>>;\r\n  vacancyId: string;\r\n  vacancyCandidates: {\r\n    [key: string]: Candidate[];\r\n  };\r\n  setVacancyCandidates: React.Dispatch<\r\n    React.SetStateAction<{ [key: string]: Candidate[] } | null>\r\n  >;\r\n  vacancyRefNo: string;\r\n  selectedVacancy: Vacancy;\r\n  IS_LOCK_FEATURE_DISABLED: string;\r\n  mercuryPortal: boolean;\r\n}) => {\r\n  const [reviewState, setReviewState] = useState<ReviewState>(null);\r\n  const [showCommentBox, setShowCommentBox] = useState(false);\r\n  const [viewState, setViewState] = useState(false);\r\n  const [originalComment, setOriginalComment] = useState(\"\");\r\n  const [comment, setComment] = useState(\"\");\r\n  const commentBoxRef = useRef<HTMLDivElement | null>(null);\r\n  const buttonRef = useRef<HTMLDivElement | null>(null);\r\n  const [openAbove, setOpenAbove] = useState(false);\r\n  const [isOpenDiscardModal, setIsOpenDiscardModal] = useState(false);\r\n  const [loading, setLoading] = useState(false);\r\n  const [feedbackDate, setFeedbackDate] = useState(\"\");\r\n\r\n  const { showNotification } = useNotification();\r\n\r\n  useEffect(() => {\r\n    const decision = candidate?.candidate_data?.recruiter_review_decision;\r\n    if (decision && decision !== null) {\r\n      setReviewState(decision.vote as ReviewState);\r\n      setComment(decision.comment || \"\");\r\n      setOriginalComment(decision.comment || \"\");\r\n    }\r\n    let lastFeedbackDate =\r\n      candidate?.candidate_data?.recruiter_review_decision?.feedback_timestamp;\r\n    if (lastFeedbackDate) {\r\n      lastFeedbackDate =\r\n        unFormattedDateWithBrowserTimezoneInDDMMYY(lastFeedbackDate);\r\n      setFeedbackDate(lastFeedbackDate);\r\n    }\r\n  }, [candidate]);\r\n\r\n  useEffect(() => {\r\n    const handleClickOutside = (event: MouseEvent) => {\r\n      if (\r\n        commentBoxRef.current &&\r\n        !commentBoxRef.current.contains(event.target as Node)\r\n      ) {\r\n        closeCommentBox();\r\n      }\r\n    };\r\n    if (showCommentBox) {\r\n      document.addEventListener(\"mousedown\", handleClickOutside);\r\n    }\r\n    return () => {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    };\r\n  }, [showCommentBox, comment, candidate]);\r\n\r\n  useEffect(() => {\r\n    if (showCommentBox && buttonRef.current) {\r\n      const buttonRect = buttonRef.current.getBoundingClientRect();\r\n      const windowHeight = window.innerHeight;\r\n      setOpenAbove(buttonRect.bottom + 220 > windowHeight);\r\n    }\r\n  }, [showCommentBox]);\r\n\r\n  const handleReviewClick = (state: ReviewState, isLike: boolean) => {\r\n    setViewState(false);\r\n    setReviewState((prev) => (prev === state ? null : state));\r\n    handleSend(reviewState === state ? null : state);\r\n    if (!isLike) {\r\n      setShowCommentBox(true);\r\n    } else {\r\n      setShowCommentBox(false);\r\n    }\r\n  };\r\n\r\n  const sendToastMessage = (\r\n    comment: string,\r\n    state: string | null,\r\n    type: \"success\" | \"error\"\r\n  ) => {\r\n    let message = \"\";\r\n    if (type === \"error\") {\r\n      message = \"Error submitting review. Please try again.\";\r\n    } else if (type === \"success\") {\r\n      if (comment !== originalComment) {\r\n        message = \"Review submitted successfully.\";\r\n      } else if (state === \"like\") {\r\n        message = \"You've marked this candidate as a good fit.\";\r\n      } else if (state === \"dislike\") {\r\n        message = \"You've marked this candidate as not a good fit.\";\r\n      } else if (state === null) {\r\n        message = \"Your review action removed successfully.\";\r\n      }\r\n    }\r\n    showNotification(message, type);\r\n  };\r\n\r\n  const handleSend = async (state: ReviewState) => {\r\n    setShowCommentBox(false);\r\n    setIsOpenDiscardModal(false);\r\n    const uName =\r\n      localStorage.getItem(\"userName\") || localStorage.getItem(\"emailId\");\r\n    const requestObj: RecruiterReview = {\r\n      candidate_contact_id: candidate.candidate_contactid,\r\n      vacancy_refno: vacancyRefNo,\r\n      vote: state,\r\n      comment: state === null ? \"\" : comment,\r\n      reviewer_email: uName || selectedVacancy?.locked_by,\r\n      portal_name: mercuryPortal ? \"Mercury\" : \"Recruiter\",\r\n    };\r\n    try {\r\n      setLoading(true);\r\n      const response = await fetch(\"/api/vacancies\", {\r\n        method: \"POST\",\r\n        headers: {\r\n          Accept: \"application/json\",\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n        body: JSON.stringify(requestObj),\r\n      });\r\n      if (response.ok) {\r\n        const updatedCandidates = candidates.map((can: Candidate) => {\r\n          if (can.candidate_contactid === candidate.candidate_contactid) {\r\n            return {\r\n              ...can,\r\n              candidate_data: {\r\n                ...can.candidate_data,\r\n                recruiter_review_decision: requestObj,\r\n              },\r\n            };\r\n          }\r\n          return can;\r\n        });\r\n        setCandidates(updatedCandidates);\r\n        setVacancyCandidates({\r\n          ...vacancyCandidates,\r\n          [vacancyId]: updatedCandidates,\r\n        });\r\n        sendToastMessage(\r\n          requestObj.comment,\r\n          reviewState === state ? null : state,\r\n          \"success\"\r\n        );\r\n        setOriginalComment(requestObj.comment);\r\n      } else {\r\n        sendToastMessage(\r\n          requestObj.comment,\r\n          reviewState === state ? null : state,\r\n          \"error\"\r\n        );\r\n      }\r\n    } catch (error) {\r\n      console.error(error);\r\n      showNotification(\"Error submitting review. Please try again.\", \"error\");\r\n    } finally {\r\n      setLoading(false);\r\n      setComment(\"\");\r\n    }\r\n  };\r\n\r\n  const handleDiscardChange = () => {\r\n    setComment(\r\n      candidate?.candidate_data?.recruiter_review_decision?.comment || \"\"\r\n    );\r\n    setIsOpenDiscardModal(false);\r\n    setShowCommentBox(false);\r\n  };\r\n\r\n  const handleCancelChange = () => {\r\n    setIsOpenDiscardModal(false);\r\n  };\r\n\r\n  const closeCommentBox = () => {\r\n    const originalComment =\r\n      candidate?.candidate_data?.recruiter_review_decision?.comment || \"\";\r\n    if (comment.trim() !== originalComment.trim()) {\r\n      setIsOpenDiscardModal(true);\r\n    } else {\r\n      setShowCommentBox(false);\r\n    }\r\n  };\r\n\r\n  let isEditable =\r\n    IS_LOCK_FEATURE_DISABLED === \"true\"\r\n      ? !(IS_LOCK_FEATURE_DISABLED === \"true\")\r\n      : !selectedVacancy?.is_locked && selectedVacancy?.locked_by;\r\n\r\n  if (mercuryPortal) {\r\n    isEditable = mercuryPortal;\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <div className=\"relative flex\" ref={buttonRef}>\r\n        <div className=\"flex items-center space-x-2\">\r\n          <motion.div\r\n            onClick={() =>\r\n              isEditable\r\n                ? handleReviewClick(\"like\", reviewState === \"like\")\r\n                : null\r\n            }\r\n            animate={{ scale: reviewState === \"like\" ? 1.3 : 1 }}\r\n            transition={{ type: \"spring\", stiffness: 300 }}\r\n            whileHover={{\r\n              scale: reviewState === \"like\" ? 1.3 : !isEditable ? 1 : 1.1,\r\n            }}\r\n          >\r\n            <ThumbsUp\r\n              size={18}\r\n              className={`cursor-pointer ${\r\n                !isEditable\r\n                  ? \"text-green-950\"\r\n                  : reviewState === \"like\"\r\n                  ? \"text-green-700\"\r\n                  : \"text-green-600\"\r\n              }`}\r\n              fill={\r\n                !isEditable\r\n                  ? reviewState === \"like\"\r\n                    ? \"#14532d\"\r\n                    : \"white\"\r\n                  : reviewState === \"like\"\r\n                  ? \"#43bc6e\"\r\n                  : \"white\"\r\n              }\r\n            />\r\n          </motion.div>\r\n          <motion.div\r\n            onClick={() =>\r\n              isEditable\r\n                ? handleReviewClick(\"dislike\", reviewState === \"dislike\")\r\n                : null\r\n            }\r\n            animate={{ scale: reviewState === \"dislike\" ? 1.3 : 1 }}\r\n            transition={{ type: \"spring\", stiffness: 300 }}\r\n            whileHover={{\r\n              scale: reviewState === \"dislike\" ? 1.3 : !isEditable ? 1 : 1.1,\r\n            }}\r\n          >\r\n            <ThumbsDown\r\n              size={18}\r\n              className={`cursor-pointer ${\r\n                !isEditable\r\n                  ? \"text-red-900\"\r\n                  : reviewState === \"dislike\"\r\n                  ? \"text-red-700\"\r\n                  : \"text-red-600\"\r\n              }`}\r\n              fill={\r\n                !isEditable\r\n                  ? reviewState === \"dislike\"\r\n                    ? \"#7f1d1d\"\r\n                    : \"white\"\r\n                  : reviewState === \"dislike\"\r\n                  ? \"#f46161\"\r\n                  : \"white\"\r\n              }\r\n            />\r\n          </motion.div>\r\n          {(reviewState === \"like\" || reviewState === \"dislike\") &&\r\n            candidate?.candidate_data?.recruiter_review_decision?.comment && (\r\n              <motion.div\r\n                transition={{ type: \"spring\", stiffness: 300 }}\r\n                whileHover={{ scale: !showCommentBox ? 1.3 : 1.1 }}\r\n              >\r\n                <AppToolTip\r\n                  text=\"View Comments\"\r\n                  header={\r\n                    <Eye\r\n                      onClick={() => {\r\n                        setViewState(true);\r\n                        setShowCommentBox(true);\r\n                      }}\r\n                      size={20}\r\n                      className={`cursor-pointer text-blue-900`}\r\n                    />\r\n                  }\r\n                />\r\n              </motion.div>\r\n            )}\r\n        </div>\r\n        <AnimatePresence>\r\n          {showCommentBox && (\r\n            <motion.div\r\n              ref={commentBoxRef}\r\n              initial={{ opacity: 0, y: openAbove ? -10 : 10, x: 10 }}\r\n              animate={{ opacity: 1, y: 0, x: -180 }}\r\n              exit={{ opacity: 0, y: openAbove ? -10 : 10 }}\r\n              transition={{ duration: 0.3 }}\r\n              className={`absolute z-[1] ${\r\n                openAbove ? \"bottom-10\" : \"top-10\"\r\n              } w-[300px] h-[200px] bg-white shadow-lg rounded-lg p-4 border`}\r\n            >\r\n              <div className=\"flex justify-between items-center mb-1\">\r\n                <p className=\"text-sm font-semibold\">\r\n                  {reviewState === \"like\"\r\n                    ? \"Why did you like this?\"\r\n                    : reviewState === \"dislike\"\r\n                    ? \"Why did you dislike this?\"\r\n                    : \"What are you unsure about?\"}\r\n                </p>\r\n                <CircleX\r\n                  size={14}\r\n                  className=\"cursor-pointer\"\r\n                  onClick={closeCommentBox}\r\n                />\r\n              </div>\r\n              <textarea\r\n                className={`w-full ${\r\n                  viewState ? \"h-[140px]\" : \"h-[100px]\"\r\n                } border p-2 rounded resize-none focus:outline-none focus:ring-2 focus:ring-blue-500`}\r\n                placeholder=\"Share your thoughts...\"\r\n                value={comment}\r\n                onChange={(e) => setComment(e.target.value)}\r\n                disabled={viewState || loading}\r\n              />\r\n              {viewState ? null : (\r\n                <div className=\"flex gap-2\">\r\n                  <Button\r\n                    onClick={() => {\r\n                      setComment(\r\n                        candidate?.candidate_data?.recruiter_review_decision\r\n                          ?.comment || \"\"\r\n                      );\r\n                      setShowCommentBox(false);\r\n                    }}\r\n                    disabled={comment.trim() === \"\" || loading}\r\n                    className=\"w-full bg-gray-800 text-white py-1 rounded hover:bg-gray-900 transition px-0 ml-0\"\r\n                    size=\"icon\"\r\n                  >\r\n                    Discard\r\n                  </Button>\r\n\r\n                  <Button\r\n                    onClick={() => handleSend(reviewState)}\r\n                    disabled={comment.trim() === \"\" || loading}\r\n                    size=\"icon\"\r\n                    className=\"w-full bg-gray-800 text-white py-1 rounded hover:bg-gray-900 transition px-0 ml-0\"\r\n                  >\r\n                    Save Comment\r\n                  </Button>\r\n                </div>\r\n              )}\r\n            </motion.div>\r\n          )}\r\n        </AnimatePresence>\r\n        <DiscardModal\r\n          saveChanges={() => handleSend(reviewState)}\r\n          isOpenDiscardModal={isOpenDiscardModal}\r\n          cancelTabChange={handleCancelChange}\r\n          confirmTabChange={handleDiscardChange}\r\n        />\r\n      </div>\r\n      <div className=\"!ml-0 text-[12px] mt-1\">{feedbackDate}</div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ThumbAction;\r\n"], "names": [], "mappings": ";;;;AACA;AAEA;AAEA;AACA;AACA;AACA;AANA;AAFA;AAAA;AAAA;AAEA;AAFA;;;;;;;;;;;AAYA,MAAM,cAAc,CAAC,EACnB,SAAS,EACT,UAAU,EACV,aAAa,EACb,SAAS,EACT,iBAAiB,EACjB,oBAAoB,EACpB,YAAY,EACZ,eAAe,EACf,wBAAwB,EACxB,aAAa,EAgBd;;IACC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC5D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IACpD,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IAChD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,kBAAe,AAAD;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,WAAW,WAAW,gBAAgB;YAC5C,IAAI,YAAY,aAAa,MAAM;gBACjC,eAAe,SAAS,IAAI;gBAC5B,WAAW,SAAS,OAAO,IAAI;gBAC/B,mBAAmB,SAAS,OAAO,IAAI;YACzC;YACA,IAAI,mBACF,WAAW,gBAAgB,2BAA2B;YACxD,IAAI,kBAAkB;gBACpB,mBACE,CAAA,GAAA,iHAAA,CAAA,6CAA0C,AAAD,EAAE;gBAC7C,gBAAgB;YAClB;QACF;gCAAG;QAAC;KAAU;IAEd,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM;4DAAqB,CAAC;oBAC1B,IACE,cAAc,OAAO,IACrB,CAAC,cAAc,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAC5C;wBACA;oBACF;gBACF;;YACA,IAAI,gBAAgB;gBAClB,SAAS,gBAAgB,CAAC,aAAa;YACzC;YACA;yCAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;gCAAG;QAAC;QAAgB;QAAS;KAAU;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,kBAAkB,UAAU,OAAO,EAAE;gBACvC,MAAM,aAAa,UAAU,OAAO,CAAC,qBAAqB;gBAC1D,MAAM,eAAe,OAAO,WAAW;gBACvC,aAAa,WAAW,MAAM,GAAG,MAAM;YACzC;QACF;gCAAG;QAAC;KAAe;IAEnB,MAAM,oBAAoB,CAAC,OAAoB;QAC7C,aAAa;QACb,eAAe,CAAC,OAAU,SAAS,QAAQ,OAAO;QAClD,WAAW,gBAAgB,QAAQ,OAAO;QAC1C,IAAI,CAAC,QAAQ;YACX,kBAAkB;QACpB,OAAO;YACL,kBAAkB;QACpB;IACF;IAEA,MAAM,mBAAmB,CACvB,SACA,OACA;QAEA,IAAI,UAAU;QACd,IAAI,SAAS,SAAS;YACpB,UAAU;QACZ,OAAO,IAAI,SAAS,WAAW;YAC7B,IAAI,YAAY,iBAAiB;gBAC/B,UAAU;YACZ,OAAO,IAAI,UAAU,QAAQ;gBAC3B,UAAU;YACZ,OAAO,IAAI,UAAU,WAAW;gBAC9B,UAAU;YACZ,OAAO,IAAI,UAAU,MAAM;gBACzB,UAAU;YACZ;QACF;QACA,iBAAiB,SAAS;IAC5B;IAEA,MAAM,aAAa,OAAO;QACxB,kBAAkB;QAClB,sBAAsB;QACtB,MAAM,QACJ,aAAa,OAAO,CAAC,eAAe,aAAa,OAAO,CAAC;QAC3D,MAAM,aAA8B;YAClC,sBAAsB,UAAU,mBAAmB;YACnD,eAAe;YACf,MAAM;YACN,SAAS,UAAU,OAAO,KAAK;YAC/B,gBAAgB,SAAS,iBAAiB;YAC1C,aAAa,gBAAgB,YAAY;QAC3C;QACA,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM,kBAAkB;gBAC7C,QAAQ;gBACR,SAAS;oBACP,QAAQ;oBACR,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YACA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,oBAAoB,WAAW,GAAG,CAAC,CAAC;oBACxC,IAAI,IAAI,mBAAmB,KAAK,UAAU,mBAAmB,EAAE;wBAC7D,OAAO;4BACL,GAAG,GAAG;4BACN,gBAAgB;gCACd,GAAG,IAAI,cAAc;gCACrB,2BAA2B;4BAC7B;wBACF;oBACF;oBACA,OAAO;gBACT;gBACA,cAAc;gBACd,qBAAqB;oBACnB,GAAG,iBAAiB;oBACpB,CAAC,UAAU,EAAE;gBACf;gBACA,iBACE,WAAW,OAAO,EAClB,gBAAgB,QAAQ,OAAO,OAC/B;gBAEF,mBAAmB,WAAW,OAAO;YACvC,OAAO;gBACL,iBACE,WAAW,OAAO,EAClB,gBAAgB,QAAQ,OAAO,OAC/B;YAEJ;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC;YACd,iBAAiB,8CAA8C;QACjE,SAAU;YACR,WAAW;YACX,WAAW;QACb;IACF;IAEA,MAAM,sBAAsB;QAC1B,WACE,WAAW,gBAAgB,2BAA2B,WAAW;QAEnE,sBAAsB;QACtB,kBAAkB;IACpB;IAEA,MAAM,qBAAqB;QACzB,sBAAsB;IACxB;IAEA,MAAM,kBAAkB;QACtB,MAAM,kBACJ,WAAW,gBAAgB,2BAA2B,WAAW;QACnE,IAAI,QAAQ,IAAI,OAAO,gBAAgB,IAAI,IAAI;YAC7C,sBAAsB;QACxB,OAAO;YACL,kBAAkB;QACpB;IACF;IAEA,IAAI,aACF,6BAA6B,SACzB,CAAC,CAAC,6BAA6B,MAAM,IACrC,CAAC,iBAAiB,aAAa,iBAAiB;IAEtD,IAAI,eAAe;QACjB,aAAa;IACf;IAEA,qBACE;;0BACE,6LAAC;gBAAI,WAAU;gBAAgB,KAAK;;kCAClC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS,IACP,aACI,kBAAkB,QAAQ,gBAAgB,UAC1C;gCAEN,SAAS;oCAAE,OAAO,gBAAgB,SAAS,MAAM;gCAAE;gCACnD,YAAY;oCAAE,MAAM;oCAAU,WAAW;gCAAI;gCAC7C,YAAY;oCACV,OAAO,gBAAgB,SAAS,MAAM,CAAC,aAAa,IAAI;gCAC1D;0CAEA,cAAA,6LAAC,iNAAA,CAAA,WAAQ;oCACP,MAAM;oCACN,WAAW,CAAC,eAAe,EACzB,CAAC,aACG,mBACA,gBAAgB,SAChB,mBACA,kBACJ;oCACF,MACE,CAAC,aACG,gBAAgB,SACd,YACA,UACF,gBAAgB,SAChB,YACA;;;;;;;;;;;0CAIV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS,IACP,aACI,kBAAkB,WAAW,gBAAgB,aAC7C;gCAEN,SAAS;oCAAE,OAAO,gBAAgB,YAAY,MAAM;gCAAE;gCACtD,YAAY;oCAAE,MAAM;oCAAU,WAAW;gCAAI;gCAC7C,YAAY;oCACV,OAAO,gBAAgB,YAAY,MAAM,CAAC,aAAa,IAAI;gCAC7D;0CAEA,cAAA,6LAAC,qNAAA,CAAA,aAAU;oCACT,MAAM;oCACN,WAAW,CAAC,eAAe,EACzB,CAAC,aACG,iBACA,gBAAgB,YAChB,iBACA,gBACJ;oCACF,MACE,CAAC,aACG,gBAAgB,YACd,YACA,UACF,gBAAgB,YAChB,YACA;;;;;;;;;;;4BAIT,CAAC,gBAAgB,UAAU,gBAAgB,SAAS,KACnD,WAAW,gBAAgB,2BAA2B,yBACpD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,YAAY;oCAAE,MAAM;oCAAU,WAAW;gCAAI;gCAC7C,YAAY;oCAAE,OAAO,CAAC,iBAAiB,MAAM;gCAAI;0CAEjD,cAAA,6LAAC,4HAAA,CAAA,UAAU;oCACT,MAAK;oCACL,sBACE,6LAAC,mMAAA,CAAA,MAAG;wCACF,SAAS;4CACP,aAAa;4CACb,kBAAkB;wCACpB;wCACA,MAAM;wCACN,WAAW,CAAC,4BAA4B,CAAC;;;;;;;;;;;;;;;;;;;;;;kCAOvD,6LAAC,4LAAA,CAAA,kBAAe;kCACb,gCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,KAAK;4BACL,SAAS;gCAAE,SAAS;gCAAG,GAAG,YAAY,CAAC,KAAK;gCAAI,GAAG;4BAAG;4BACtD,SAAS;gCAAE,SAAS;gCAAG,GAAG;gCAAG,GAAG,CAAC;4BAAI;4BACrC,MAAM;gCAAE,SAAS;gCAAG,GAAG,YAAY,CAAC,KAAK;4BAAG;4BAC5C,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAW,CAAC,eAAe,EACzB,YAAY,cAAc,SAC3B,6DAA6D,CAAC;;8CAE/D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDACV,gBAAgB,SACb,2BACA,gBAAgB,YAChB,8BACA;;;;;;sDAEN,6LAAC,+MAAA,CAAA,UAAO;4CACN,MAAM;4CACN,WAAU;4CACV,SAAS;;;;;;;;;;;;8CAGb,6LAAC;oCACC,WAAW,CAAC,OAAO,EACjB,YAAY,cAAc,YAC3B,mFAAmF,CAAC;oCACrF,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;oCAC1C,UAAU,aAAa;;;;;;gCAExB,YAAY,qBACX,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,8HAAA,CAAA,SAAM;4CACL,SAAS;gDACP,WACE,WAAW,gBAAgB,2BACvB,WAAW;gDAEjB,kBAAkB;4CACpB;4CACA,UAAU,QAAQ,IAAI,OAAO,MAAM;4CACnC,WAAU;4CACV,MAAK;sDACN;;;;;;sDAID,6LAAC,8HAAA,CAAA,SAAM;4CACL,SAAS,IAAM,WAAW;4CAC1B,UAAU,QAAQ,IAAI,OAAO,MAAM;4CACnC,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;kCAQX,6LAAC,4IAAA,CAAA,UAAY;wBACX,aAAa,IAAM,WAAW;wBAC9B,oBAAoB;wBACpB,iBAAiB;wBACjB,kBAAkB;;;;;;;;;;;;0BAGtB,6LAAC;gBAAI,WAAU;0BAA0B;;;;;;;;AAG/C;GAtXM;;QAuCyB,4HAAA,CAAA,kBAAe;;;KAvCxC;uCAwXS"}}, {"offset": {"line": 3450, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3456, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/CandidateTable/CandidateTable.tsx"], "sourcesContent": ["import {\r\n  TableHeader,\r\n  TableBody,\r\n  TableRow,\r\n  TableHead,\r\n  TableCell,\r\n} from \"@/components/ui/table\";\r\nimport { ArrowUpDown, FileText } from \"lucide-react\";\r\nimport Loading from \"@/components/Loading\";\r\nimport WhyFitAction from \"../candidates/WhyFitAction\";\r\nimport AppToolTip from \"../AppToolTip\";\r\nimport ThumbAction from \"../candidates/ThumbAction\";\r\nimport { Candidate, Vacancy } from \"@/app/candidates/helper\";\r\nimport { unFormattedDateWithBrowserTimezoneInDDMMYY } from \"@/utils/utils\";\r\nimport { useState } from \"react\";\r\nimport Modal from \"../Modal\";\r\nimport { useNotification } from \"@/hooks/useNotification\";\r\nimport { useSession } from \"next-auth/react\";\r\nimport { trackedFetch } from \"@/library/trackApi\";\r\nimport { getAppInsights } from \"@/library/appInsights\";\r\nimport { Button } from \"../ui/button\";\r\nimport DiscardModal from \"../candidates/DiscardModal\";\r\nimport { useEntitlement } from \"@/context/EntitlementContext\";\r\n\r\ninterface TableProps {\r\n  handleCandidateSort: (name: string) => void;\r\n  isResumeModalOpen: boolean;\r\n  loading?: boolean;\r\n  paginatedCandidates: Candidate[];\r\n  selectedVacancy: Vacancy;\r\n  candidates: Candidate[];\r\n  setCandidates: React.Dispatch<React.SetStateAction<Candidate[]>>;\r\n  vacancyCandidates: {\r\n    [key: string]: Candidate[];\r\n  } | null;\r\n  setVacancyCandidates: React.Dispatch<\r\n    React.SetStateAction<{\r\n      [key: string]: Candidate[];\r\n    } | null>\r\n  >;\r\n  fetchResume: (name: Candidate) => void;\r\n  showSelectBox?: boolean;\r\n  handleCheckboxClick?: (id: number) => void;\r\n  selectedRows?: number[];\r\n  mercuryPortal: boolean;\r\n  IS_LOCK_FEATURE_DISABLED: string;\r\n  fetchCandidatesById: (vacamcyId: string) => void;\r\n}\r\n\r\nconst CandidateTable = ({\r\n  handleCandidateSort,\r\n  isResumeModalOpen,\r\n  loading,\r\n  paginatedCandidates,\r\n  selectedVacancy,\r\n  candidates,\r\n  setCandidates,\r\n  vacancyCandidates,\r\n  setVacancyCandidates,\r\n  fetchResume,\r\n  showSelectBox,\r\n  handleCheckboxClick,\r\n  selectedRows,\r\n  mercuryPortal,\r\n  IS_LOCK_FEATURE_DISABLED,\r\n  fetchCandidatesById,\r\n}: TableProps) => {\r\n  const [existingEdit, setExistingEdit] = useState(false);\r\n  const [candidateEditId, setCandidateEditId] = useState<string>(\"\");\r\n  const [candidateEditId2, setCandidateEditId2] = useState<string>(\"\");\r\n  const [expandPopupOpen, setExpandPopupOpen] = useState(false);\r\n  const [hideDislikedCandidates, setHideDislikedCandidates] = useState(false);\r\n  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);\r\n  const [mercuryURL, setMercuryURL] = useState<string>(\"\");\r\n  const handleHideDislikedCandidates = () => {\r\n    setHideDislikedCandidates(!hideDislikedCandidates);\r\n  };\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const { showNotification } = useNotification();\r\n  // Add at the top inside CandidateTable component\r\n  const [confirmShortlist, setConfirmShortlist] = useState<{\r\n    open: boolean;\r\n    contactId: string | null;\r\n  }>({ open: false, contactId: null });\r\n  const { entitlements } = useEntitlement();\r\n  const showShortlist = entitlements.Shorting_Listing;\r\n\r\n  // Filter candidates based on hide disliked state\r\n  const filteredCandidates = hideDislikedCandidates\r\n    ? paginatedCandidates.filter(\r\n        (candidate) =>\r\n          candidate?.candidate_data?.recruiter_review_decision?.vote !==\r\n          \"dislike\"\r\n      )\r\n    : paginatedCandidates;\r\n  const { data: session } = useSession();\r\n\r\n  const isShortlistDisabled = IS_LOCK_FEATURE_DISABLED === \"true\";\r\n\r\n  // const handleActionGuard = (action: () => void) => {\r\n  //   if (existingEdit && candidateEditId) {\r\n  //     setCandidateEditId2(\"targetCandidateId\");\r\n  //   } else {\r\n  //     action(); // Safe to proceed\r\n  //   }\r\n  // };\r\n  function getEnvType(envCode: string): \"sandbox\" | \"prod\" | \"uat\" {\r\n    const code = envCode.toLowerCase();\r\n    if ([\"qa\", \"dv\", \"sb\"].includes(code)) {\r\n      return \"sandbox\";\r\n    }\r\n    if (code === \"ua\") {\r\n      return \"uat\";\r\n    }\r\n    if (code === \"prod\") {\r\n      return \"prod\";\r\n    }\r\n    return \"sandbox\";\r\n  }\r\n\r\n  const getDomainClient = () => {\r\n    const url = window.location.href;\r\n    const match = url.match(\r\n      /^https:\\/\\/recruiter(?:\\.([^.]+))?\\.tandymgroup\\.com/\r\n    );\r\n    const result = match && match[1] ? match[1] : \"prod\";\r\n\r\n    const envType = getEnvType(result);\r\n    const crmDomain =\r\n      envType === \"prod\"\r\n        ? \"tandymgroup.crm.dynamics.com\"\r\n        : `tandymgroup-${envType}.crm.dynamics.com`;\r\n\r\n    return crmDomain;\r\n  };\r\n\r\n  const handleOpenPopup = async (contactId: string, mercuryPortal: boolean) => {\r\n    const crmDomain = getDomainClient();\r\n    let tandymURL = \"\";\r\n    try {\r\n      tandymURL = window.top?.location?.href || \"\";\r\n    } catch (err) {\r\n      console.warn(\r\n        \"Unable to access top window location due to cross-origin:\",\r\n        err\r\n      );\r\n      tandymURL = window.location.href;\r\n    }\r\n    const url = `https://${crmDomain}/main.aspx?appid=0ec72dfd-7eb1-ee11-a569-00224822704f&forceUCI=1&pagetype=entityrecord&etn=contact&id=${contactId}`;\r\n    url && window.open(url, \"_blank\");\r\n  };\r\n\r\n  const handleShortlist = async (contactId: string) => {\r\n    if (!contactId || !selectedVacancy?.vacancy_id) {\r\n      showNotification(\"Candidate Contact ID is missing\", \"error\");\r\n      return;\r\n    }\r\n\r\n    const payload = {\r\n      vacancy_id: selectedVacancy?.vacancy_id,\r\n      candidate_id: contactId,\r\n      reviewer_email: session?.user?.email,\r\n      portal_name: mercuryPortal ? \"Mercury\" : \"Recruiter\",\r\n    };\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      const response = await trackedFetch(\r\n        `/api/vacancies/shortlisted`,\r\n        {\r\n          method: \"POST\",\r\n          body: JSON.stringify(payload),\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n          },\r\n        },\r\n        { context: \"Post Shortlisted Vacancies\" }\r\n      );\r\n      const data = await response.json();\r\n\r\n      if (data?.data?.status_code === 200) {\r\n        // Success\r\n        showNotification(\r\n          data?.data?.message\r\n            ? `${data.data.message} (Code : 200)`\r\n            : data?.message\r\n            ? `${data.message} (Code : 200)`\r\n            : \"Shortlisted the candidate successfully for this vacancy:(Code : 200)\",\r\n          \"success\"\r\n        );\r\n        getAppInsights()?.trackEvent({\r\n          name: \"FE_PostVacanciesShortlisted\",\r\n          properties: {\r\n            reviewerEmail: session?.user?.email,\r\n            candidateId: contactId,\r\n            context: \"PostVacanciesShortlisted\",\r\n          },\r\n        });\r\n        // Remove the shortlisted candidate from the table\r\n        setCandidates((prev) =>\r\n          prev.filter(\r\n            (candidate) => candidate.candidate_data.contactid !== contactId\r\n          )\r\n        );\r\n      } else if (\r\n        data?.status_code === 409 ||\r\n        (typeof data?.error === \"string\" &&\r\n          data.error.toLowerCase().includes(\"already shortlisted\"))\r\n      ) {\r\n        // Already shortlisted\r\n        showNotification(\r\n          `${data.error} (Code : 409)` ||\r\n            `${data.detail?.message} (Code : 409)` ||\r\n            \"Failed to shortlist the candidate for this vacancy- (Code : 409)\",\r\n          \"warning\"\r\n        );\r\n      } else {\r\n        showNotification(\r\n          `Failed to shortlist the candidate for this vacancy - (Code : ${data.status_code})`,\r\n          \"error\"\r\n        );\r\n        // All other errors\r\n        throw new Error(\r\n          data?.error ||\r\n            data?.detail?.message ||\r\n            \"Failed to shortlist candidate\"\r\n        );\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error while shortlisting candidate:\", error);\r\n      getAppInsights()?.trackException({\r\n        error: new Error(\r\n          \"Post Vacancies Shortlisted api with error is \" + error\r\n        ),\r\n        severityLevel: 3,\r\n      });\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className={`max-h-[80vh] rounded-lg ${\r\n        expandPopupOpen ? \"overflow-hidden\" : \"overflow-auto\"\r\n      }`}\r\n    >\r\n      <table className=\"w-full caption-bottom text-sm\">\r\n        <TableHeader className=\"rounded-lg sticky top-0 z-[10]\">\r\n          <TableRow className=\"bg-gray-900 rounded-lg hover:bg-gray-900 text-white text-[12px]\">\r\n            {showSelectBox && (\r\n              <TableHead className=\"p-2 text-white w-[100px] rounded-tl-lg\">\r\n                Select\r\n              </TableHead>\r\n            )}\r\n            <TableHead\r\n              className={`p-2 text-white w-[250px] cursor-pointer ${\r\n                !showSelectBox ? \"rounded-tl-lg\" : \"\"\r\n              }`}\r\n            >\r\n              Name\r\n            </TableHead>\r\n            <TableHead\r\n              onClick={() => handleCandidateSort(\"city\")}\r\n              className=\"p-2 text-white w-[90px] cursor-pointer\"\r\n            >\r\n              Location\r\n              <ArrowUpDown className=\"inline w-3 h-3 ml-1 cursor-pointer\" />\r\n            </TableHead>\r\n            <TableHead\r\n              onClick={() => handleCandidateSort(\"availability\")}\r\n              className=\"p-2 text-white w-[90px] cursor-pointer\"\r\n            >\r\n              Availability Date\r\n              <ArrowUpDown className=\"inline w-3 h-3 ml-1 cursor-pointer\" />\r\n            </TableHead>\r\n            <TableHead className=\"p-2 text-white w-[90px] cursor-pointer\">\r\n              AI Agent Status\r\n            </TableHead>\r\n            <TableHead\r\n              onClick={() => handleCandidateSort(\"freshness_index\")}\r\n              className=\"p-2 text-white w-[90px] cursor-pointer\"\r\n            >\r\n              Freshness Index\r\n              <ArrowUpDown className=\"inline w-3 h-3 ml-1 cursor-pointer\" />\r\n            </TableHead>\r\n            <TableHead\r\n              className=\"p-2 text-white w-[70px] cursor-pointer\"\r\n              onClick={() => handleCandidateSort(\"overallscore\")}\r\n            >\r\n              Total Score\r\n              <ArrowUpDown className=\"inline w-3 h-3 ml-1 cursor-pointer\" />\r\n            </TableHead>\r\n            <TableHead className=\"p-2 text-white w-[80px]\">\r\n              Parsed Resume\r\n            </TableHead>\r\n            <TableHead className=\"p-2 text-white w-[85px]\">\r\n              <div className=\"flex flex-col items-center\">\r\n                <span className=\"text-xs mb-1\">Rating</span>\r\n                <button\r\n                  onClick={handleHideDislikedCandidates}\r\n                  className={`flex items-center gap-1 px-2 py-1 text-xs border border-white rounded transition-colors ${\r\n                    hideDislikedCandidates\r\n                      ? \"bg-white text-gray-900\"\r\n                      : \"bg-transparent hover:bg-white hover:text-gray-900\"\r\n                  }`}\r\n                >\r\n                  <span>{hideDislikedCandidates ? \"View\" : \"Hide\"}</span>\r\n                  <svg className=\"w-3 h-3 fill-red-500\" viewBox=\"0 0 24 24\">\r\n                    <path d=\"M15 3H6c-.83 0-1.54.5-1.84 1.22l-3.02 7.05c-.09.23-.14.47-.14.73v2c0 1.1.9 2 2 2h6.31l-.95 4.57-.03.32c0 .41.17.79.44 1.06L9.83 23l6.59-6.59c.36-.36.58-.86.58-1.41V5c0-1.1-.9-2-2-2zm4 0v12h4V3h-4z\" />\r\n                  </svg>\r\n                </button>\r\n              </div>\r\n            </TableHead>\r\n\r\n            <TableHead className=\"p-2 text-white w-[280px] text-left\">\r\n              Why Fit\r\n            </TableHead>\r\n            {(showShortlist || mercuryPortal) && (\r\n              <TableHead className=\"p-2 text-white w-[80px] text-left\">\r\n                Shortlist\r\n              </TableHead>\r\n            )}\r\n          </TableRow>\r\n        </TableHeader>\r\n        <TableBody>\r\n          {loading && !isResumeModalOpen ? (\r\n            <TableRow>\r\n              <TableCell colSpan={9} className=\"p-4 text-center\">\r\n                <Loading height=\"h-[50vh]\" />\r\n              </TableCell>\r\n            </TableRow>\r\n          ) : filteredCandidates.length > 0 ? (\r\n            filteredCandidates?.map((candidate, ind) => {\r\n              return (\r\n                <TableRow\r\n                  key={candidate.candidate_data.contactid}\r\n                  className={ind % 2 === 1 ? \"bg-gray-100\" : \"bg-white\"}\r\n                >\r\n                  {showSelectBox && selectedRows && handleCheckboxClick && (\r\n                    <TableCell className=\"p-2 text-[14px] w-[100px]\">\r\n                      <input\r\n                        type=\"checkbox\"\r\n                        checked={selectedRows.includes(candidate.id)}\r\n                        onChange={() => handleCheckboxClick(candidate.id)}\r\n                        className=\"accent-gray-800 cursor-pointer\"\r\n                      />\r\n                    </TableCell>\r\n                  )}\r\n                  <TableCell\r\n                    className=\"p-2 text-[14px] w-[250px]\"\r\n                    onClick={() =>\r\n                      handleOpenPopup(\r\n                        candidate.candidate_contactid,\r\n                        mercuryPortal\r\n                      )\r\n                    }\r\n                  >\r\n                    <AppToolTip\r\n                      text={candidate.candidate_data.name}\r\n                      header={\r\n                        <p className=\"truncate md:max-w-[250px]\">\r\n                          {candidate.candidate_data.name}\r\n                        </p>\r\n                      }\r\n                      direction=\"top\"\r\n                    />\r\n                    {/* <AppToolTip\r\n                      text={candidate.candidate_data.email}\r\n                      header={\r\n                        <p className=\"truncate md:max-w-[300px] 2xl:max-w-full\">\r\n                          ({candidate.candidate_data.email})\r\n                        </p>\r\n                      }\r\n                      direction=\"top\"\r\n                    /> */}\r\n                  </TableCell>\r\n                  <TableCell className=\"p-2 text-[14px] w-[90px]\">\r\n                    {(() => {\r\n                      const { city, state } = candidate.candidate_data;\r\n                      const isMissing = (val: string | null | undefined) =>\r\n                        !val || val === \"Missing\";\r\n\r\n                      if (isMissing(city) && isMissing(state)) return \"\";\r\n                      if (!isMissing(city) && !isMissing(state))\r\n                        return `${city}, ${state}`;\r\n                      return !isMissing(city) ? city : state;\r\n                    })()}\r\n                  </TableCell>\r\n                  <TableCell className=\"p-2 text-[14px] w-[90px]\">\r\n                    {(() => {\r\n                      const availability =\r\n                        candidate.candidate_data.availability;\r\n                      if (!availability || availability === \"Missing\")\r\n                        return \"\";\r\n                      return unFormattedDateWithBrowserTimezoneInDDMMYY(\r\n                        availability\r\n                      );\r\n                    })()}\r\n                  </TableCell>\r\n                  <TableCell className=\"p-2 text-[14px] w-[70px]\">\r\n                    {candidate.candidate_data[\"info_bot_response\"]}\r\n                    <br />\r\n                    {candidate.candidate_data[\"info_bot_response_date\"] != null\r\n                      ? unFormattedDateWithBrowserTimezoneInDDMMYY(\r\n                          candidate.candidate_data[\"info_bot_response_date\"]\r\n                        )\r\n                      : null}\r\n                  </TableCell>\r\n                  <TableCell className=\"p-2 text-[13px] w-[70px]\">\r\n                    {candidate.candidate_data[\"freshness_index\"]}\r\n                  </TableCell>\r\n                  <TableCell className=\"p-2 text-[14px] w-[70px]\">\r\n                    {candidate.candidate_data[\r\n                      \"classification score\"\r\n                    ]?.overallscore.toFixed(2)}\r\n                  </TableCell>\r\n                  <TableCell className=\"p-2 space-x-2 h-14 w-[50px] sticky right-[0px]\">\r\n                    <AppToolTip\r\n                      header={\r\n                        <FileText\r\n                          size={18}\r\n                          className=\"text-gray-700\"\r\n                          onClick={() => fetchResume(candidate)}\r\n                          aria-disabled={!candidate.candidate_contactid}\r\n                        />\r\n                      }\r\n                      text=\"View Resume\"\r\n                    />\r\n                  </TableCell>\r\n                  <TableCell className=\"p-2 space-x-2 h-14 w-[85px]\">\r\n                    <ThumbAction\r\n                      candidate={candidate}\r\n                      setCandidates={setCandidates}\r\n                      candidates={candidates}\r\n                      vacancyId={selectedVacancy?.vacancy_id}\r\n                      vacancyRefNo={selectedVacancy?.refno}\r\n                      vacancyCandidates={vacancyCandidates || {}}\r\n                      setVacancyCandidates={setVacancyCandidates}\r\n                      selectedVacancy={selectedVacancy}\r\n                      IS_LOCK_FEATURE_DISABLED={IS_LOCK_FEATURE_DISABLED}\r\n                      mercuryPortal={mercuryPortal}\r\n                    />\r\n                  </TableCell>\r\n\r\n                  <TableCell className=\"p-2 pl-0 space-x-2 h-14 w-[280px] 2xl:w-[550px]\">\r\n                    <div className={`flex gap-[5px]`}>\r\n                      <WhyFitAction\r\n                        candidate={candidate}\r\n                        setCandidates={setCandidates}\r\n                        selectedVacancy={selectedVacancy}\r\n                        existingEdit={existingEdit}\r\n                        setExistingEdit={setExistingEdit}\r\n                        candidateEditId={candidateEditId}\r\n                        candidateEditId2={candidateEditId2}\r\n                        setCandidateEditId={setCandidateEditId}\r\n                        setCandidateEditId2={setCandidateEditId2}\r\n                        setExpandPopupOpen={setExpandPopupOpen}\r\n                      />\r\n                    </div>\r\n                  </TableCell>\r\n                  {(showShortlist || mercuryPortal) && (\r\n                    <TableCell className=\"p-2 space-x-2 h-14 w-[80px]\">\r\n                      <Button\r\n                        className=\"rounded w-24\"\r\n                        onClick={() =>\r\n                          setConfirmShortlist({\r\n                            open: true,\r\n                            contactId: candidate.candidate_data.contactid,\r\n                          })\r\n                        }\r\n                        disabled={\r\n                          isShortlistDisabled\r\n                            ? true\r\n                            : candidate.candidate_data?.shortlisted_details\r\n                                .status === \"success\"\r\n                        }\r\n                      >\r\n                        {candidate.candidate_data?.shortlisted_details\r\n                          .status !== \"success\"\r\n                          ? \"Shortlist\"\r\n                          : \"Shortlisted\"}\r\n                      </Button>\r\n                    </TableCell>\r\n                  )}\r\n                </TableRow>\r\n              );\r\n            })\r\n          ) : (\r\n            <TableRow>\r\n              <TableCell colSpan={9} className=\"p-4 text-center\">\r\n                No candidates found\r\n              </TableCell>\r\n            </TableRow>\r\n          )}\r\n        </TableBody>\r\n      </table>\r\n      {isModalOpen && mercuryPortal && mercuryURL && (\r\n        <Modal\r\n          isOpen={isModalOpen}\r\n          width=\"max-w-6xl\"\r\n          height=\"h-[90vh]\"\r\n          children={\r\n            <div className=\"relative w-full h-[80vh]\">\r\n              <iframe\r\n                src={mercuryURL}\r\n                width=\"100%\"\r\n                height=\"100%\"\r\n                allowFullScreen\r\n              />\r\n            </div>\r\n          }\r\n          onClose={() => setIsModalOpen(false)}\r\n        />\r\n      )}\r\n      {isLoading && (\r\n        <div className=\"fixed inset-0 flex items-center justify-center bg-gray-900 bg-opacity-50 z-50\">\r\n          <Loading height=\"h-[50px]\" />\r\n        </div>\r\n      )}\r\n      {confirmShortlist.open && (\r\n        <DiscardModal\r\n          isOpenDiscardModal={confirmShortlist.open}\r\n          title=\"Confirm Shortlist\"\r\n          cancelTabChange={() =>\r\n            setConfirmShortlist({ open: false, contactId: null })\r\n          }\r\n          message=\"This action will shortlist the candidate for the selected vacancy. Do you want to continue?\"\r\n          confirmButtonText=\"Confirm\"\r\n          confirmTabChange={async () => {\r\n            setConfirmShortlist({ open: false, contactId: null });\r\n            if (confirmShortlist.contactId) {\r\n              await handleShortlist(confirmShortlist.contactId);\r\n            }\r\n          }}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CandidateTable;\r\n"], "names": [], "mappings": ";;;;AAAA;AAQA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA;AAAA;;;;;;;;;;;;;;;;;;;AA0CA,MAAM,iBAAiB,CAAC,EACtB,mBAAmB,EACnB,iBAAiB,EACjB,OAAO,EACP,mBAAmB,EACnB,eAAe,EACf,UAAU,EACV,aAAa,EACb,iBAAiB,EACjB,oBAAoB,EACpB,WAAW,EACX,aAAa,EACb,mBAAmB,EACnB,YAAY,EACZ,aAAa,EACb,wBAAwB,EACxB,mBAAmB,EACR;;IACX,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC/D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACxD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACrD,MAAM,+BAA+B;QACnC,0BAA0B,CAAC;IAC7B;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,kBAAe,AAAD;IAC3C,iDAAiD;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAGpD;QAAE,MAAM;QAAO,WAAW;IAAK;IAClC,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IACtC,MAAM,gBAAgB,aAAa,gBAAgB;IAEnD,iDAAiD;IACjD,MAAM,qBAAqB,yBACvB,oBAAoB,MAAM,CACxB,CAAC,YACC,WAAW,gBAAgB,2BAA2B,SACtD,aAEJ;IACJ,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAEnC,MAAM,sBAAsB,6BAA6B;IAEzD,sDAAsD;IACtD,2CAA2C;IAC3C,gDAAgD;IAChD,aAAa;IACb,mCAAmC;IACnC,MAAM;IACN,KAAK;IACL,SAAS,WAAW,OAAe;QACjC,MAAM,OAAO,QAAQ,WAAW;QAChC,IAAI;YAAC;YAAM;YAAM;SAAK,CAAC,QAAQ,CAAC,OAAO;YACrC,OAAO;QACT;QACA,IAAI,SAAS,MAAM;YACjB,OAAO;QACT;QACA,IAAI,SAAS,QAAQ;YACnB,OAAO;QACT;QACA,OAAO;IACT;IAEA,MAAM,kBAAkB;QACtB,MAAM,MAAM,OAAO,QAAQ,CAAC,IAAI;QAChC,MAAM,QAAQ,IAAI,KAAK,CACrB;QAEF,MAAM,SAAS,SAAS,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,GAAG;QAE9C,MAAM,UAAU,WAAW;QAC3B,MAAM,YACJ,YAAY,SACR,iCACA,CAAC,YAAY,EAAE,QAAQ,iBAAiB,CAAC;QAE/C,OAAO;IACT;IAEA,MAAM,kBAAkB,OAAO,WAAmB;QAChD,MAAM,YAAY;QAClB,IAAI,YAAY;QAChB,IAAI;YACF,YAAY,OAAO,GAAG,EAAE,UAAU,QAAQ;QAC5C,EAAE,OAAO,KAAK;YACZ,QAAQ,IAAI,CACV,6DACA;YAEF,YAAY,OAAO,QAAQ,CAAC,IAAI;QAClC;QACA,MAAM,MAAM,CAAC,QAAQ,EAAE,UAAU,sGAAsG,EAAE,WAAW;QACpJ,OAAO,OAAO,IAAI,CAAC,KAAK;IAC1B;IAEA,MAAM,kBAAkB,OAAO;QAC7B,IAAI,CAAC,aAAa,CAAC,iBAAiB,YAAY;YAC9C,iBAAiB,mCAAmC;YACpD;QACF;QAEA,MAAM,UAAU;YACd,YAAY,iBAAiB;YAC7B,cAAc;YACd,gBAAgB,SAAS,MAAM;YAC/B,aAAa,gBAAgB,YAAY;QAC3C;QACA,aAAa;QAEb,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAChC,CAAC,0BAA0B,CAAC,EAC5B;gBACE,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;gBACrB,SAAS;oBACP,gBAAgB;gBAClB;YACF,GACA;gBAAE,SAAS;YAA6B;YAE1C,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,MAAM,MAAM,gBAAgB,KAAK;gBACnC,UAAU;gBACV,iBACE,MAAM,MAAM,UACR,GAAG,KAAK,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,GACnC,MAAM,UACN,GAAG,KAAK,OAAO,CAAC,aAAa,CAAC,GAC9B,wEACJ;gBAEF,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,WAAW;oBAC3B,MAAM;oBACN,YAAY;wBACV,eAAe,SAAS,MAAM;wBAC9B,aAAa;wBACb,SAAS;oBACX;gBACF;gBACA,kDAAkD;gBAClD,cAAc,CAAC,OACb,KAAK,MAAM,CACT,CAAC,YAAc,UAAU,cAAc,CAAC,SAAS,KAAK;YAG5D,OAAO,IACL,MAAM,gBAAgB,OACrB,OAAO,MAAM,UAAU,YACtB,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,wBACpC;gBACA,sBAAsB;gBACtB,iBACE,GAAG,KAAK,KAAK,CAAC,aAAa,CAAC,IAC1B,GAAG,KAAK,MAAM,EAAE,QAAQ,aAAa,CAAC,IACtC,oEACF;YAEJ,OAAO;gBACL,iBACE,CAAC,6DAA6D,EAAE,KAAK,WAAW,CAAC,CAAC,CAAC,EACnF;gBAEF,mBAAmB;gBACnB,MAAM,IAAI,MACR,MAAM,SACJ,MAAM,QAAQ,WACd;YAEN;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,eAAe;gBAC/B,OAAO,IAAI,MACT,kDAAkD;gBAEpD,eAAe;YACjB;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,6LAAC;QACC,WAAW,CAAC,wBAAwB,EAClC,kBAAkB,oBAAoB,iBACtC;;0BAEF,6LAAC;gBAAM,WAAU;;kCACf,6LAAC,6HAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,6LAAC,6HAAA,CAAA,WAAQ;4BAAC,WAAU;;gCACjB,+BACC,6LAAC,6HAAA,CAAA,YAAS;oCAAC,WAAU;8CAAyC;;;;;;8CAIhE,6LAAC,6HAAA,CAAA,YAAS;oCACR,WAAW,CAAC,wCAAwC,EAClD,CAAC,gBAAgB,kBAAkB,IACnC;8CACH;;;;;;8CAGD,6LAAC,6HAAA,CAAA,YAAS;oCACR,SAAS,IAAM,oBAAoB;oCACnC,WAAU;;wCACX;sDAEC,6LAAC,2NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;8CAEzB,6LAAC,6HAAA,CAAA,YAAS;oCACR,SAAS,IAAM,oBAAoB;oCACnC,WAAU;;wCACX;sDAEC,6LAAC,2NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;8CAEzB,6LAAC,6HAAA,CAAA,YAAS;oCAAC,WAAU;8CAAyC;;;;;;8CAG9D,6LAAC,6HAAA,CAAA,YAAS;oCACR,SAAS,IAAM,oBAAoB;oCACnC,WAAU;;wCACX;sDAEC,6LAAC,2NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;8CAEzB,6LAAC,6HAAA,CAAA,YAAS;oCACR,WAAU;oCACV,SAAS,IAAM,oBAAoB;;wCACpC;sDAEC,6LAAC,2NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;8CAEzB,6LAAC,6HAAA,CAAA,YAAS;oCAAC,WAAU;8CAA0B;;;;;;8CAG/C,6LAAC,6HAAA,CAAA,YAAS;oCAAC,WAAU;8CACnB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAe;;;;;;0DAC/B,6LAAC;gDACC,SAAS;gDACT,WAAW,CAAC,wFAAwF,EAClG,yBACI,2BACA,qDACJ;;kEAEF,6LAAC;kEAAM,yBAAyB,SAAS;;;;;;kEACzC,6LAAC;wDAAI,WAAU;wDAAuB,SAAQ;kEAC5C,cAAA,6LAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAMhB,6LAAC,6HAAA,CAAA,YAAS;oCAAC,WAAU;8CAAqC;;;;;;gCAGzD,CAAC,iBAAiB,aAAa,mBAC9B,6LAAC,6HAAA,CAAA,YAAS;oCAAC,WAAU;8CAAoC;;;;;;;;;;;;;;;;;kCAM/D,6LAAC,6HAAA,CAAA,YAAS;kCACP,WAAW,CAAC,kCACX,6LAAC,6HAAA,CAAA,WAAQ;sCACP,cAAA,6LAAC,6HAAA,CAAA,YAAS;gCAAC,SAAS;gCAAG,WAAU;0CAC/B,cAAA,6LAAC,yHAAA,CAAA,UAAO;oCAAC,QAAO;;;;;;;;;;;;;;;mCAGlB,mBAAmB,MAAM,GAAG,IAC9B,oBAAoB,IAAI,CAAC,WAAW;4BAClC,qBACE,6LAAC,6HAAA,CAAA,WAAQ;gCAEP,WAAW,MAAM,MAAM,IAAI,gBAAgB;;oCAE1C,iBAAiB,gBAAgB,qCAChC,6LAAC,6HAAA,CAAA,YAAS;wCAAC,WAAU;kDACnB,cAAA,6LAAC;4CACC,MAAK;4CACL,SAAS,aAAa,QAAQ,CAAC,UAAU,EAAE;4CAC3C,UAAU,IAAM,oBAAoB,UAAU,EAAE;4CAChD,WAAU;;;;;;;;;;;kDAIhB,6LAAC,6HAAA,CAAA,YAAS;wCACR,WAAU;wCACV,SAAS,IACP,gBACE,UAAU,mBAAmB,EAC7B;kDAIJ,cAAA,6LAAC,4HAAA,CAAA,UAAU;4CACT,MAAM,UAAU,cAAc,CAAC,IAAI;4CACnC,sBACE,6LAAC;gDAAE,WAAU;0DACV,UAAU,cAAc,CAAC,IAAI;;;;;;4CAGlC,WAAU;;;;;;;;;;;kDAYd,6LAAC,6HAAA,CAAA,YAAS;wCAAC,WAAU;kDAClB,CAAC;4CACA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,UAAU,cAAc;4CAChD,MAAM,YAAY,CAAC,MACjB,CAAC,OAAO,QAAQ;4CAElB,IAAI,UAAU,SAAS,UAAU,QAAQ,OAAO;4CAChD,IAAI,CAAC,UAAU,SAAS,CAAC,UAAU,QACjC,OAAO,GAAG,KAAK,EAAE,EAAE,OAAO;4CAC5B,OAAO,CAAC,UAAU,QAAQ,OAAO;wCACnC,CAAC;;;;;;kDAEH,6LAAC,6HAAA,CAAA,YAAS;wCAAC,WAAU;kDAClB,CAAC;4CACA,MAAM,eACJ,UAAU,cAAc,CAAC,YAAY;4CACvC,IAAI,CAAC,gBAAgB,iBAAiB,WACpC,OAAO;4CACT,OAAO,CAAA,GAAA,iHAAA,CAAA,6CAA0C,AAAD,EAC9C;wCAEJ,CAAC;;;;;;kDAEH,6LAAC,6HAAA,CAAA,YAAS;wCAAC,WAAU;;4CAClB,UAAU,cAAc,CAAC,oBAAoB;0DAC9C,6LAAC;;;;;4CACA,UAAU,cAAc,CAAC,yBAAyB,IAAI,OACnD,CAAA,GAAA,iHAAA,CAAA,6CAA0C,AAAD,EACvC,UAAU,cAAc,CAAC,yBAAyB,IAEpD;;;;;;;kDAEN,6LAAC,6HAAA,CAAA,YAAS;wCAAC,WAAU;kDAClB,UAAU,cAAc,CAAC,kBAAkB;;;;;;kDAE9C,6LAAC,6HAAA,CAAA,YAAS;wCAAC,WAAU;kDAClB,UAAU,cAAc,CACvB,uBACD,EAAE,aAAa,QAAQ;;;;;;kDAE1B,6LAAC,6HAAA,CAAA,YAAS;wCAAC,WAAU;kDACnB,cAAA,6LAAC,4HAAA,CAAA,UAAU;4CACT,sBACE,6LAAC,iNAAA,CAAA,WAAQ;gDACP,MAAM;gDACN,WAAU;gDACV,SAAS,IAAM,YAAY;gDAC3B,iBAAe,CAAC,UAAU,mBAAmB;;;;;;4CAGjD,MAAK;;;;;;;;;;;kDAGT,6LAAC,6HAAA,CAAA,YAAS;wCAAC,WAAU;kDACnB,cAAA,6LAAC,2IAAA,CAAA,UAAW;4CACV,WAAW;4CACX,eAAe;4CACf,YAAY;4CACZ,WAAW,iBAAiB;4CAC5B,cAAc,iBAAiB;4CAC/B,mBAAmB,qBAAqB,CAAC;4CACzC,sBAAsB;4CACtB,iBAAiB;4CACjB,0BAA0B;4CAC1B,eAAe;;;;;;;;;;;kDAInB,6LAAC,6HAAA,CAAA,YAAS;wCAAC,WAAU;kDACnB,cAAA,6LAAC;4CAAI,WAAW,CAAC,cAAc,CAAC;sDAC9B,cAAA,6LAAC,4IAAA,CAAA,UAAY;gDACX,WAAW;gDACX,eAAe;gDACf,iBAAiB;gDACjB,cAAc;gDACd,iBAAiB;gDACjB,iBAAiB;gDACjB,kBAAkB;gDAClB,oBAAoB;gDACpB,qBAAqB;gDACrB,oBAAoB;;;;;;;;;;;;;;;;oCAIzB,CAAC,iBAAiB,aAAa,mBAC9B,6LAAC,6HAAA,CAAA,YAAS;wCAAC,WAAU;kDACnB,cAAA,6LAAC,8HAAA,CAAA,SAAM;4CACL,WAAU;4CACV,SAAS,IACP,oBAAoB;oDAClB,MAAM;oDACN,WAAW,UAAU,cAAc,CAAC,SAAS;gDAC/C;4CAEF,UACE,sBACI,OACA,UAAU,cAAc,EAAE,oBACvB,WAAW;sDAGnB,UAAU,cAAc,EAAE,oBACxB,WAAW,YACV,cACA;;;;;;;;;;;;+BAjJL,UAAU,cAAc,CAAC,SAAS;;;;;wBAuJ7C,mBAEA,6LAAC,6HAAA,CAAA,WAAQ;sCACP,cAAA,6LAAC,6HAAA,CAAA,YAAS;gCAAC,SAAS;gCAAG,WAAU;0CAAkB;;;;;;;;;;;;;;;;;;;;;;YAO1D,eAAe,iBAAiB,4BAC/B,6LAAC,uHAAA,CAAA,UAAK;gBACJ,QAAQ;gBACR,OAAM;gBACN,QAAO;gBACP,wBACE,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBACC,KAAK;wBACL,OAAM;wBACN,QAAO;wBACP,eAAe;;;;;;;;;;;gBAIrB,SAAS,IAAM,eAAe;;;;;;YAGjC,2BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,yHAAA,CAAA,UAAO;oBAAC,QAAO;;;;;;;;;;;YAGnB,iBAAiB,IAAI,kBACpB,6LAAC,4IAAA,CAAA,UAAY;gBACX,oBAAoB,iBAAiB,IAAI;gBACzC,OAAM;gBACN,iBAAiB,IACf,oBAAoB;wBAAE,MAAM;wBAAO,WAAW;oBAAK;gBAErD,SAAQ;gBACR,mBAAkB;gBAClB,kBAAkB;oBAChB,oBAAoB;wBAAE,MAAM;wBAAO,WAAW;oBAAK;oBACnD,IAAI,iBAAiB,SAAS,EAAE;wBAC9B,MAAM,gBAAgB,iBAAiB,SAAS;oBAClD;gBACF;;;;;;;;;;;;AAKV;GA1eM;;QA6ByB,4HAAA,CAAA,kBAAe;QAMnB,iIAAA,CAAA,iBAAc;QAWb,iJAAA,CAAA,aAAU;;;KA9ChC;uCA4eS"}}, {"offset": {"line": 4154, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4160, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/ui/select.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\";\r\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\";\r\nimport { cn } from \"@/library/utils\";\r\n\r\nconst Select = SelectPrimitive.Root;\r\n\r\nconst SelectGroup = SelectPrimitive.Group;\r\n\r\nconst SelectValue = SelectPrimitive.Value;\r\n\r\nconst SelectTrigger = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <SelectPrimitive.Icon asChild>\r\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\r\n    </SelectPrimitive.Icon>\r\n  </SelectPrimitive.Trigger>\r\n));\r\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName;\r\n\r\nconst SelectScrollUpButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollUpButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronUp className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollUpButton>\r\n));\r\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName;\r\n\r\nconst SelectScrollDownButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollDownButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronDown className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollDownButton>\r\n));\r\nSelectScrollDownButton.displayName =\r\n  SelectPrimitive.ScrollDownButton.displayName;\r\n\r\nconst SelectContent = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\r\n>(({ className, children, position = \"popper\", ...props }, ref) => (\r\n  <SelectPrimitive.Portal>\r\n    <SelectPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        position === \"popper\" &&\r\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n        className\r\n      )}\r\n      position={position}\r\n      {...props}\r\n    >\r\n      <SelectScrollUpButton />\r\n      <SelectPrimitive.Viewport\r\n        className={cn(\r\n          \"p-1\",\r\n          position === \"popper\" &&\r\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\r\n        )}\r\n      >\r\n        {children}\r\n      </SelectPrimitive.Viewport>\r\n      <SelectScrollDownButton />\r\n    </SelectPrimitive.Content>\r\n  </SelectPrimitive.Portal>\r\n));\r\nSelectContent.displayName = SelectPrimitive.Content.displayName;\r\n\r\nconst SelectLabel = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\"px-2 py-1.5 text-sm font-semibold\", className)}\r\n    {...props}\r\n  />\r\n));\r\nSelectLabel.displayName = SelectPrimitive.Label.displayName;\r\n\r\nconst SelectItem = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute right-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <SelectPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </SelectPrimitive.ItemIndicator>\r\n    </span>\r\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n  </SelectPrimitive.Item>\r\n));\r\nSelectItem.displayName = SelectPrimitive.Item.displayName;\r\n\r\nconst SelectSeparator = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props}\r\n  />\r\n));\r\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName;\r\n\r\nexport {\r\n  Select,\r\n  SelectGroup,\r\n  SelectValue,\r\n  SelectTrigger,\r\n  SelectContent,\r\n  SelectLabel,\r\n  SelectItem,\r\n  SelectSeparator,\r\n  SelectScrollUpButton,\r\n  SelectScrollDownButton,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AAGA;AAFA;AACA;AAAA;AAAA;AAJA;;;;;;AAOA,MAAM,SAAS,sKAAgB,IAAI;AAEnC,MAAM,cAAc,sKAAgB,KAAK;AAEzC,MAAM,cAAc,sKAAgB,KAAK;AAEzC,MAAM,8BAAgB,8JAAM,UAAU,MAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,sKAAgB,OAAO;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kUACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,sKAAgB,IAAI;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,sKAAgB,OAAO,CAAC,WAAW;AAE/D,MAAM,qCAAuB,8JAAM,UAAU,CAG3C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,cAAc;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,mNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;MAZnB;AAeN,qBAAqB,WAAW,GAAG,sKAAgB,cAAc,CAAC,WAAW;AAE7E,MAAM,uCAAyB,8JAAM,UAAU,CAG7C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,gBAAgB;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;MAZrB;AAeN,uBAAuB,WAAW,GAChC,sKAAgB,gBAAgB,CAAC,WAAW;AAE9C,MAAM,8BAAgB,8JAAM,UAAU,OAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,6LAAC,sKAAgB,MAAM;kBACrB,cAAA,6LAAC,sKAAgB,OAAO;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,sKAAgB,QAAQ;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,sKAAgB,OAAO,CAAC,WAAW;AAE/D,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,KAAK;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,sKAAgB,KAAK,CAAC,WAAW;AAE3D,MAAM,2BAAa,8JAAM,UAAU,OAGjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,sKAAgB,IAAI;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,sKAAgB,aAAa;8BAC5B,cAAA,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGrB,6LAAC,sKAAgB,QAAQ;0BAAE;;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,sKAAgB,IAAI,CAAC,WAAW;AAEzD,MAAM,gCAAkB,8JAAM,UAAU,QAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,SAAS;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG,sKAAgB,SAAS,CAAC,WAAW"}}, {"offset": {"line": 4369, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4375, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/candidates/Candidates.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useEffect, useState } from \"react\";\r\nimport {\r\n  ArrowUpDown,\r\n  ChevronLeft,\r\n  ChevronRight,\r\n  LockKeyhole,\r\n} from \"lucide-react\";\r\nimport noCandidatesImage from \"@/public/assets/noCandidates.svg\";\r\nimport {\r\n  Candidate,\r\n  ResumeData,\r\n  ReviewerConfig,\r\n  Vacancy,\r\n} from \"../../app/candidates/helper\";\r\nimport Loading from \"@/components/Loading\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport Image from \"next/image\";\r\nimport VacancyItem from \"@/components/candidates/VacancyItem\";\r\nimport CandidateResume from \"@/components/candidates/CandidateResume\";\r\nimport { useNotification } from \"@/hooks/useNotification\";\r\nimport CandidateTable from \"../CandidateTable/CandidateTable\";\r\nimport { useSession } from \"next-auth/react\";\r\nimport { usePathname } from \"next/navigation\";\r\nimport { Button } from \"../ui/button\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport { trackedFetch } from \"@/library/trackApi\";\r\nimport { getAppInsights } from \"@/library/appInsights\";\r\nimport VacancyDetails from \"./VacancyDetails\";\r\nimport { Badge } from \"../ui/badge\";\r\nimport { unFormattedDateWithBrowserTimezone } from \"@/utils/utils\";\r\n\r\nconst isVacancyFetchFromFiles = false\r\n  // process.env.NEXT_PUBLIC_IS_VACANCY_FETCH_FROM_FILES === \"true\";\r\n\r\nconst DEFAULT_PER_PAGE = 150;\r\nconst SHOW_SEARCH_BY_EMAIL = false;\r\n\r\nexport type CatalystMatchStatus = {\r\n  status: \"queued\" | \"inprocess\" | \"completed\" | \"error\" | null;\r\n  initiated_at: string | null;\r\n  completed_at: string | null;\r\n  initiated_by: string | null;\r\n};\r\n\r\nexport type VacancyUpdateTimestamps = {\r\n  match_results_generated_at: string | null;\r\n  data_last_updated_at: string | null;\r\n  archived: boolean;\r\n};\r\n\r\nexport type CatalystMatchStatusResponse = {\r\n  catalyst_match_status: CatalystMatchStatus;\r\n  update_timestamps: VacancyUpdateTimestamps;\r\n};\r\n\r\nconst Candidates = ({\r\n  vacancyid,\r\n  mercuryPortal,\r\n  emailId,\r\n  IS_LOCK_FEATURE_DISABLED,\r\n}: {\r\n  vacancyid?: string;\r\n  mercuryPortal?: boolean;\r\n  emailId?: string;\r\n  IS_LOCK_FEATURE_DISABLED: string;\r\n}) => {\r\n  const pathname = usePathname();\r\n  const pageNotFound =\r\n    vacancyid === undefined &&\r\n    !pathname.includes(`/CandidateTuning/For_Mercury_Portal?vacancyid=`);\r\n  const [vacancies, setVacancies] = useState<Vacancy[]>([]);\r\n  const [vacancyCandidates, setVacancyCandidates] = useState<Record<\r\n    string,\r\n    Candidate[]\r\n  > | null>({});\r\n  const [selectedResume, setSelectedResume] = useState<ResumeData | null>(null);\r\n  const [isResumeModalOpen, setIsResumeModalOpen] = useState(false);\r\n  const [selectedVacancy, setSelectedVacancy] = useState<Vacancy | null>(null);\r\n  const [candidates, setCandidates] = useState<Candidate[]>([]);\r\n  const [loading, setLoading] = useState<boolean>(false);\r\n  const [page, setPage] = useState<number>(1);\r\n  const [limit, setLimit] = useState<number>(DEFAULT_PER_PAGE);\r\n  const [search, setSearch] = useState<string>(\"\");\r\n  const [vacancySearch, setVacancySearch] = useState<string>(\"\");\r\n  const [activeVacancy, setActiveVacancy] = useState<Vacancy | null>(null);\r\n  const [startRating, setStartRating] = useState(false);\r\n  const [reviewLoading, setReviewLoading] = useState(false);\r\n  const [completeRating, setCompleteRating] = useState(false);\r\n  const [userName, setUserName] = useState<string>(\"\");\r\n  const [isParsedVacancyPopupOpen, setIsParsedVacancyPopupOpen] =\r\n    useState<boolean>(false);\r\n  const [sortConfig, setSortConfig] = useState<{\r\n    key: string | null;\r\n    direction: \"asc\" | \"desc\" | null;\r\n  }>({\r\n    key: null,\r\n    direction: null,\r\n  });\r\n  const session = useSession();\r\n  const name = session?.data?.user?.name;\r\n  const [regenerationData, setRegenerationData] = useState<CatalystMatchStatus>();\r\n  const [isRegenerating, setIsRegenerating] = useState(false);\r\n  const [pollingInterval, setPollingInterval] = useState<NodeJS.Timeout | null>(null);\r\n  const [isRegenerationReadOnly, setIsRegenerationReadOnly] = useState(false);\r\n  const [regenerationMessage, setRegenerationMessage] = useState<string>(\"\");\r\n  const [canRegenerate, setCanRegenerate] = useState(false);\r\n\r\n  // Utility function to check if 2 hours have passed since last regeneration\r\n  const canRegenerateAfterCooldown = (initiatedAt: string | null): boolean => {\r\n    if (!initiatedAt) return true;\r\n\r\n    const lastRegeneration = new Date(initiatedAt);\r\n    const now = new Date();\r\n    const twoHoursInMs = 2 * 60 * 60 * 1000; // 2 hours in milliseconds\r\n\r\n    return (now.getTime() - lastRegeneration.getTime()) >= twoHoursInMs;\r\n  };\r\n\r\n  // Function to update regeneration state based on status\r\n  const updateRegenerationState = (catalystMatchStatus: CatalystMatchStatus) => {\r\n    const status = catalystMatchStatus.status;\r\n    const initiatedAt = catalystMatchStatus.initiated_at;\r\n    const initiatedBy = catalystMatchStatus.initiated_by;\r\n    const currentUser = emailId || name;\r\n\r\n    if (status === \"queued\" || status === \"inprocess\") {\r\n      // Regeneration is ongoing\r\n      setIsRegenerating(true);\r\n      setIsRegenerationReadOnly(true);\r\n      setCanRegenerate(false);\r\n      setRegenerationMessage(\"Regeneration in progress (Catalyst match is read-only until complete)\");\r\n\r\n      if (initiatedBy && initiatedBy !== currentUser) {\r\n        // Show popup for other user's regeneration\r\n        alert(`Regeneration is already initiated by ${initiatedBy}. Please wait until it is complete.`);\r\n      }\r\n    } else if (status === \"completed\") {\r\n      // Regeneration completed\r\n      setIsRegenerating(false);\r\n\r\n      const canRegenerateNow = canRegenerateAfterCooldown(initiatedAt);\r\n      setCanRegenerate(canRegenerateNow);\r\n      setIsRegenerationReadOnly(!canRegenerateNow);\r\n\r\n      if (canRegenerateNow) {\r\n        setRegenerationMessage(\"\");\r\n      } else {\r\n        setRegenerationMessage(\"Regeneration Complete. To regenerate again, wait for 2 hours from the last one\");\r\n      }\r\n    } else if (status === \"error\") {\r\n      // Regeneration failed\r\n      setIsRegenerating(false);\r\n      setIsRegenerationReadOnly(false);\r\n      setCanRegenerate(true);\r\n      setRegenerationMessage(\"\");\r\n    } else {\r\n      // No regeneration data or null status\r\n      setIsRegenerating(false);\r\n      setIsRegenerationReadOnly(false);\r\n      setCanRegenerate(true);\r\n      setRegenerationMessage(\"\");\r\n    }\r\n  };\r\n\r\n  // Function to check regeneration status\r\n  const checkRegenerationStatus = async (shouldRefreshCandidates: boolean = false) => {\r\n    if (!vacancyid) return;\r\n\r\n    try {\r\n      const response = await fetch(`/api/vacancies/${vacancyid}/catalystmatchstatus`);\r\n\r\n      if (response.ok) {\r\n        const data: CatalystMatchStatusResponse = await response.json();\r\n        const catalystMatchStatus = data.catalyst_match_status;\r\n\r\n        setRegenerationData(catalystMatchStatus);\r\n        updateRegenerationState(catalystMatchStatus);\r\n\r\n        const status = catalystMatchStatus.status;\r\n\r\n        if (status === \"completed\") {\r\n          // Regeneration completed, stop polling and refresh candidates if needed\r\n          if (pollingInterval) {\r\n            clearInterval(pollingInterval);\r\n            setPollingInterval(null);\r\n          }\r\n\r\n          if (shouldRefreshCandidates && mercuryPortal) {\r\n            await fetchCandidatesById(vacancyid);\r\n          }\r\n          console.log(\"Catalyst match regeneration completed successfully\");\r\n        } else if (status === \"error\") {\r\n          // Regeneration failed, stop polling\r\n          if (pollingInterval) {\r\n            clearInterval(pollingInterval);\r\n            setPollingInterval(null);\r\n          }\r\n          console.error(\"Catalyst match regeneration failed\");\r\n        }\r\n        // If status is \"queued\" or \"inprocess\", continue polling\r\n      } else {\r\n        console.error(\"Failed to check regeneration status:\", response.statusText);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error checking regeneration status:\", error);\r\n    }\r\n  };\r\n\r\n  // Function to start polling for regeneration status\r\n  const startPolling = () => {\r\n    if (pollingInterval) {\r\n      clearInterval(pollingInterval);\r\n    }\r\n\r\n    const interval = setInterval(() => checkRegenerationStatus(true), 30000); // Poll every 30 seconds\r\n    setPollingInterval(interval);\r\n  };\r\n\r\n  const regenerateCatalystMatch = async () => {\r\n      if (!vacancyid || !canRegenerate) return;\r\n\r\n      try {\r\n        const response = await fetch(`/api/vacancies/${vacancyid}/regenerate-catalyst-match`, {\r\n          method: 'POST',\r\n          headers: {\r\n            'Content-Type': 'application/json',\r\n          },\r\n          body: JSON.stringify({\r\n            reviewer_email: emailId\r\n          })\r\n        });\r\n\r\n        if (response.ok) {\r\n          const data: CatalystMatchStatus = await response.json();\r\n          setRegenerationData(data);\r\n          updateRegenerationState(data);\r\n          console.log(\"Catalyst match regeneration initiated:\", data);\r\n\r\n          // Start polling for status updates\r\n          startPolling();\r\n        } else {\r\n          // Check if regeneration is already initiated by another user\r\n          if (response.status === 409) { // Assuming 409 Conflict for already initiated\r\n            await checkRegenerationStatus(); // Refresh status to get current state\r\n          } else {\r\n            console.error(\"Failed to regenerate catalyst match:\", response.statusText);\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error regenerating catalyst match:\", error);\r\n      }\r\n    };\r\n\r\n  useEffect(() => {\r\n    if (emailId) {\r\n      localStorage.setItem(\"emailId\", emailId);\r\n      setUserName(emailId);\r\n    }\r\n  }, [emailId]);\r\n\r\n  // Cleanup polling interval when component unmounts or vacancyid changes\r\n  useEffect(() => {\r\n    return () => {\r\n      if (pollingInterval) {\r\n        clearInterval(pollingInterval);\r\n        setPollingInterval(null);\r\n      }\r\n    };\r\n  }, [pollingInterval]);\r\n\r\n  // Reset regeneration state when vacancy changes and check catalyst match status\r\n  useEffect(() => {\r\n    if (pollingInterval) {\r\n      clearInterval(pollingInterval);\r\n      setPollingInterval(null);\r\n    }\r\n    setIsRegenerating(false);\r\n    setRegenerationData(undefined);\r\n\r\n    // Check catalyst match status when vacancy changes\r\n    if (vacancyid) {\r\n      checkRegenerationStatus();\r\n    }\r\n  }, [vacancyid]);\r\n\r\n  const [sortConfigCan, setSortConfigCan] = useState<{\r\n    key: string | null;\r\n    direction: \"asc\" | \"desc\" | null;\r\n  }>({\r\n    key: null,\r\n    direction: null,\r\n  });\r\n\r\n  const { showNotification } = useNotification();\r\n\r\n  const openParsedVacancyPopup = () => {\r\n    setActiveVacancy(selectedVacancy);\r\n    setIsParsedVacancyPopupOpen(true);\r\n  };\r\n\r\n  const fetchVacancies = async () => {\r\n    try {\r\n      setLoading(true);\r\n      const response = isVacancyFetchFromFiles\r\n        ? await trackedFetch(\r\n            \"/api/vacancies/files\",\r\n            {},\r\n            { context: \"GetVacancies\" }\r\n          )\r\n        : await trackedFetch(\"/api/vacancies\", {}, { context: \"GetVacancies\" });\r\n      if (response.ok) {\r\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n        const data: { vacancies: Vacancy[] } = await response.json();\r\n        if (data.vacancies) {\r\n          if (!isVacancyFetchFromFiles) {\r\n            setVacancies(data.vacancies);\r\n          }\r\n        }\r\n        getAppInsights()?.trackEvent({\r\n          name: \"FE_VacanciesFetched\",\r\n          properties: {\r\n            count: data.vacancies?.length ?? 0,\r\n            context: \"GetVacancies\",\r\n          },\r\n        });\r\n        return data.vacancies;\r\n      } else {\r\n        console.error(\"Failed to fetch attributes:\", response.statusText);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error fetching skills:\", error);\r\n      getAppInsights()?.trackException({\r\n        error: new Error(\"vacancies api with error is \" + error),\r\n        severityLevel: 3,\r\n      });\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const fetchCandidatesById = async (vacancyid: string) => {\r\n    try {\r\n      setLoading(true);\r\n      if (isVacancyFetchFromFiles) {\r\n        setCandidates(vacancyCandidates?.[vacancyid] || []);\r\n        return;\r\n      }\r\n      const response = await trackedFetch(\r\n        `/api/vacancies/${vacancyid.split(\"/\").join(\"-\")}`,\r\n        {},\r\n        { context: \"GetCandidatesById\" }\r\n      );\r\n      if (response.ok) {\r\n        const data: { candidates: Candidate[] } = await response.json();\r\n        setCandidates(data.candidates);\r\n        getAppInsights()?.trackEvent({\r\n          name: \"FE_CandidatesFetchedById\",\r\n          properties: {\r\n            vacancyid,\r\n            context: \"GetCandidatesByVacancyId\",\r\n          },\r\n        });\r\n      } else {\r\n        console.error(\"Failed to fetch attributes:\", response.statusText);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error fetching skills:\", error);\r\n      getAppInsights()?.trackException({\r\n        error: new Error(\"CandidatesById api with error is \" + error),\r\n        severityLevel: 3,\r\n      });\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchVacancies().then((vacancies) => {\r\n      if (vacancies && vacancies.length > 0) {\r\n        vacancies?.map((vacancy) => {\r\n          if (vacancy?.vacancy_id?.toLowerCase() === vacancyid?.toLowerCase()) {\r\n            setSelectedVacancy(vacancy);\r\n          }\r\n        });\r\n      }\r\n    });\r\n    if (vacancyid && mercuryPortal) {\r\n      fetchCandidatesById(vacancyid);\r\n    }\r\n  }, [vacancyid, mercuryPortal]);\r\n\r\n  const handleSort = (key: string = \"refno\") => {\r\n    let direction: \"asc\" | \"desc\" | null = \"asc\";\r\n    if (sortConfig.key === key && sortConfig.direction === \"asc\") {\r\n      direction = \"desc\";\r\n    }\r\n\r\n    const [externalSession, setExternalSession] = useState<{\r\n      user: { name: string; email: string };\r\n      token?: string;\r\n    } | null>(null);\r\n\r\n    const sortedData = [...vacancies].sort((a, b) => {\r\n      if (a.refno < b.refno) return direction === \"asc\" ? -1 : 1;\r\n      if (a.refno > b.refno) return direction === \"asc\" ? 1 : -1;\r\n      return 0;\r\n    });\r\n\r\n    setSortConfig({ key, direction });\r\n    setVacancies(sortedData);\r\n  };\r\n\r\n  const handleCandidateSort = (key: string) => {\r\n    let direction: \"asc\" | \"desc\" = \"asc\";\r\n    if (sortConfigCan.key === key && sortConfigCan.direction === \"asc\") {\r\n      direction = \"desc\";\r\n    }\r\n\r\n    const getValue = (candidate: Candidate) => {\r\n      const data = candidate.candidate_data;\r\n\r\n      switch (key) {\r\n        case \"name\":\r\n          return data.name?.toLowerCase() || \"\";\r\n        case \"overallscore\":\r\n          return data[\"classification score\"]?.overallscore ?? 0;\r\n        case \"jobtitlescore\":\r\n          return data[\"classification score\"]?.jobtitlescore ?? 0;\r\n        case \"softskillsscore\":\r\n          return data[\"classification score\"]?.softskillsscore ?? 0;\r\n        case \"technicalskills\":\r\n          return data[\"classification score\"]?.[\"technical skills\"] ?? 0;\r\n        case \"toolsplatformsscore\":\r\n          return data[\"classification score\"]?.toolsplatformsscore ?? 0;\r\n        case \"degreesandcertifications\":\r\n          return (\r\n            data[\"classification score\"]?.[\"degrees and certifications\"] ?? 0\r\n          );\r\n        case \"industryexperiencescore\":\r\n          return data[\"classification score\"]?.industryexperiencescore ?? 0;\r\n        case \"relevantexperiencescore\":\r\n          return data[\"classification score\"]?.relevantexperiencescore ?? 0;\r\n        case \"jobtitle_recency_score\":\r\n          return data[\"classification score\"]?.jobtitle_recency_score ?? 0;\r\n        case \"availability\": {\r\n          const availability = data.availability;\r\n          if (!availability || availability === \"Missing\") return new Date(0);\r\n          return new Date(availability);\r\n        }\r\n        case \"city\":\r\n          const { city, state } = data;\r\n          const isValid = (v: string | null | undefined) =>\r\n            v && v !== \"Missing\";\r\n          if (isValid(city) && isValid(state))\r\n            return `${city}, ${state}`.toLowerCase();\r\n          if (isValid(city)) return city.toLowerCase();\r\n          if (isValid(state)) return state.toLowerCase();\r\n          return null;\r\n        case \"freshness\":\r\n          return data[\"classification score\"]?.jobtitle_recency_score ?? 0;\r\n        case \"freshness_index\":\r\n          return data[\"freshness_index\"];\r\n        default:\r\n          return \"\";\r\n      }\r\n    };\r\n\r\n    const sortedCandidates = [...candidates].sort((a, b) => {\r\n      const aValue = getValue(a);\r\n      const bValue = getValue(b);\r\n\r\n      if (aValue == null && bValue == null) return 0;\r\n      if (aValue == null) return 1;\r\n      if (bValue == null) return -1;\r\n\r\n      if (typeof aValue === \"string\" && typeof bValue === \"string\") {\r\n        return direction === \"asc\"\r\n          ? aValue.localeCompare(bValue)\r\n          : bValue.localeCompare(aValue);\r\n      }\r\n\r\n      if (aValue < bValue) return direction === \"asc\" ? -1 : 1;\r\n      if (aValue > bValue) return direction === \"asc\" ? 1 : -1;\r\n      return 0;\r\n    });\r\n\r\n    setSortConfigCan({ key, direction });\r\n    setCandidates(sortedCandidates);\r\n  };\r\n\r\n  const handleVacancyClick = (vacancy: Vacancy) => {\r\n    setSelectedVacancy(vacancy);\r\n    setPage(1);\r\n    setSortConfigCan({\r\n      key: null,\r\n      direction: null,\r\n    });\r\n    fetchCandidatesById(vacancy.vacancy_id);\r\n  };\r\n\r\n  const filteredCandidates = candidates?.filter((candidate) =>\r\n    candidate.candidate_data.email?.toLowerCase().includes(search.toLowerCase())\r\n  );\r\n\r\n  const paginatedCandidates = filteredCandidates?.slice(\r\n    (page - 1) * limit,\r\n    page * limit\r\n  );\r\n\r\n  const filteredVacancy = (vacancies ?? []).filter((vacancy) =>\r\n    vacancy.refno.toLowerCase().includes(vacancySearch.toLowerCase())\r\n  );\r\n\r\n  const fetchResume = async (candidate: Candidate) => {\r\n    try {\r\n      setLoading(true);\r\n      setIsResumeModalOpen(true);\r\n      const response = await trackedFetch(\r\n        isVacancyFetchFromFiles\r\n          ? `/api/vacancies/files/${candidate.candidate_contactid}`\r\n          : `/api/vacancies/resume/${candidate.candidate_contactid}`,\r\n        {},\r\n        { context: \"GetResume\" }\r\n      );\r\n      if (response.ok) {\r\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n        const data: any = await response.json();\r\n        setSelectedResume(data);\r\n        getAppInsights()?.trackEvent({\r\n          name: \"FE_ResumeFetched\",\r\n          properties: {\r\n            candidateId: candidate.candidate_contactid,\r\n          },\r\n        });\r\n      } else {\r\n        console.error(\"Failed to fetch resume:\", response.statusText);\r\n      }\r\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n    } catch (error: any) {\r\n      console.error(\"Error fetching resume:\", error);\r\n      getAppInsights()?.trackException({\r\n        error: new Error(\"GetResume api with error is \" + error),\r\n        severityLevel: 3,\r\n      });\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const startReview = async () => {\r\n    if (!userName) {\r\n      showNotification(\"Please enter your email address\", \"error\");\r\n      return;\r\n    } else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(userName)) {\r\n      showNotification(\"Please enter a valid email address\", \"error\");\r\n      return;\r\n    }\r\n    localStorage.setItem(\"userName\", userName);\r\n    try {\r\n      setReviewLoading(true);\r\n      const requestObj = {\r\n        reviewer: userName,\r\n      };\r\n      const response = await trackedFetch(\r\n        `/api/vacancies/${selectedVacancy?.vacancy_id}/start`,\r\n        {\r\n          method: \"POST\",\r\n          headers: {\r\n            Accept: \"application/json\",\r\n            \"Content-Type\": \"application/json\",\r\n          },\r\n          body: JSON.stringify(requestObj),\r\n        },\r\n        { context: \"StartReview\" }\r\n      );\r\n      const data: { locked_by: string; reviewer_config: ReviewerConfig } = (\r\n        await response.json()\r\n      )?.data;\r\n      if (selectedVacancy) {\r\n        const newVacancy: Vacancy = {\r\n          ...selectedVacancy,\r\n          locked_by: data.locked_by as string,\r\n          reviewer_config: data.reviewer_config as ReviewerConfig,\r\n        };\r\n\r\n        const newVacancyList = vacancies.map((item) =>\r\n          item.vacancy_id === newVacancy?.vacancy_id ? newVacancy : item\r\n        );\r\n        setSelectedVacancy(newVacancy);\r\n        setVacancies(newVacancyList);\r\n      }\r\n      if (data) {\r\n        setReviewLoading(false);\r\n        setStartRating(false);\r\n        showNotification(`Review process started successfully`, \"success\");\r\n        getAppInsights()?.trackEvent({\r\n          name: \"FE_ReviewStarted\",\r\n          properties: {\r\n            vacancyId: selectedVacancy?.vacancy_id,\r\n            reviewer: userName,\r\n            context: \"StartReview\",\r\n          },\r\n        });\r\n      } else {\r\n        throw \"Something went wrong\";\r\n      }\r\n    } catch (error: any) {\r\n      showNotification(`Failed to start review, please try again`, \"error\");\r\n      setReviewLoading(false);\r\n      setStartRating(false);\r\n      console.error(\"Error: \", error);\r\n      getAppInsights()?.trackException({\r\n        error: new Error(\"StartReview api with error is \" + error),\r\n        severityLevel: 3,\r\n      });\r\n    }\r\n  };\r\n\r\n  const completeReview = async () => {\r\n    try {\r\n      setReviewLoading(true);\r\n      const requestObj = {\r\n        reviewer: userName,\r\n      };\r\n      const response = await trackedFetch(\r\n        `/api/vacancies/${selectedVacancy?.vacancy_id}/complete`,\r\n        {\r\n          method: \"POST\",\r\n          headers: {\r\n            Accept: \"application/json\",\r\n            \"Content-Type\": \"application/json\",\r\n          },\r\n          body: JSON.stringify(requestObj),\r\n        },\r\n        { context: \"CompleteReview\" }\r\n      );\r\n      const data: {\r\n        locked_by: string;\r\n        reviewer_config: ReviewerConfig;\r\n        is_locked: boolean;\r\n      } = (await response.json())?.data;\r\n      if (selectedVacancy) {\r\n        const newVacancy: Vacancy = {\r\n          ...selectedVacancy,\r\n          locked_by: data.locked_by as string,\r\n          reviewer_config: data.reviewer_config as ReviewerConfig,\r\n          is_locked: data.is_locked,\r\n        };\r\n\r\n        const newVacancyList = vacancies.map((item) =>\r\n          item.vacancy_id === newVacancy?.vacancy_id ? newVacancy : item\r\n        );\r\n        setSelectedVacancy(newVacancy);\r\n        setVacancies(newVacancyList);\r\n      }\r\n\r\n      if (data) {\r\n        setCompleteRating(false);\r\n        setReviewLoading(false);\r\n        showNotification(`Review locked successfully`, \"success\");\r\n        getAppInsights()?.trackEvent({\r\n          name: \"FE_ReviewCompleted\",\r\n          properties: {\r\n            vacancyId: selectedVacancy?.vacancy_id,\r\n            reviewer: userName,\r\n            context: \"CompleteReview\",\r\n          },\r\n        });\r\n      } else {\r\n        throw \"Something went wrong\";\r\n      }\r\n    } catch (error) {\r\n      showNotification(`Failed to submit review, please try again`, \"error\");\r\n      setReviewLoading(false);\r\n      console.error(\"Error: \", error);\r\n      getAppInsights()?.trackException({\r\n        error: new Error(\"CompleteReview api with error is \" + error),\r\n        severityLevel: 3,\r\n      });\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"p-4\">\r\n      {isParsedVacancyPopupOpen && (\r\n        <VacancyDetails\r\n          vacancy={selectedVacancy}\r\n          setActiveVacancy={() => {\r\n            setActiveVacancy(null);\r\n            setIsParsedVacancyPopupOpen(false);\r\n          }}\r\n          mercuryPortal={mercuryPortal ?? false}\r\n        />\r\n      )}\r\n      {(vacancyid || mercuryPortal) && (\r\n        <h2 className=\"text-2xl font-bold text-right py-2\">{name}</h2>\r\n      )}\r\n      {!vacancyid && !mercuryPortal ? (\r\n        <h2 className=\"text-2xl font-bold text-center py-2\">\r\n          Vacancies Portal\r\n        </h2>\r\n      ) : (\r\n        selectedVacancy && (\r\n          <>\r\n            <div className=\"flex justify-center items-baseline\">\r\n              <h2 className=\"text-2xl font-bold text-center\">\r\n                Catalyst Match for the Vacancy\r\n              </h2>\r\n              <span className=\"ml-2\">\r\n                <a\r\n                  href=\"#\"\r\n                  onClick={(e) => {\r\n                    e.preventDefault();\r\n                    openParsedVacancyPopup();\r\n                  }}\r\n                  className=\"text-blue-600 underline hover:text-blue-800 cursor-pointer\"\r\n                >\r\n                  [Parsed Vacancy]\r\n                </a>\r\n              </span>\r\n            </div>\r\n          </>\r\n        )\r\n      )}\r\n\r\n      <div className=\"flex\">\r\n        {/* Vacancy Panel */}\r\n        {!vacancyid && !mercuryPortal && (\r\n          <div className=\"w-[350px] p-2 border-r z-30\">\r\n            <h3\r\n              className=\"text-lg font-semibold mb-2 cursor-pointer\"\r\n              onClick={() => handleSort()}\r\n            >\r\n              Vacancy List{\" \"}\r\n              <ArrowUpDown className=\"text-black inline ml-1 h-4 w-4\" />\r\n            </h3>\r\n            <p>\r\n              <Input\r\n                type=\"search\"\r\n                placeholder=\"Search by vacancy code\"\r\n                value={vacancySearch}\r\n                onChange={(e) => setVacancySearch(e.target.value)}\r\n                className=\"w-full border border-gray-300 rounded-lg p-2 my-2\"\r\n              />\r\n            </p>\r\n            {loading && !filteredVacancy.length ? (\r\n              <Loading height=\"h-[50vh]\" />\r\n            ) : (\r\n              <ul className=\"max-h-[66vh] overflow-y-auto\">\r\n                {filteredVacancy.map((vacancy) => (\r\n                  <VacancyItem\r\n                    vacancy={vacancy}\r\n                    handleVacancyClick={handleVacancyClick}\r\n                    selectedVacancy={selectedVacancy}\r\n                    setSearch={setSearch}\r\n                    key={vacancy.refno}\r\n                    activeVacancy={activeVacancy}\r\n                    setActiveVacancy={setActiveVacancy}\r\n                    startRating={startRating}\r\n                    setStartRating={setStartRating}\r\n                    completeRating={completeRating}\r\n                    setCompleteRating={setCompleteRating}\r\n                    reviewLoading={reviewLoading}\r\n                    userName={userName}\r\n                    setUserName={setUserName}\r\n                    startReview={startReview}\r\n                    completeReview={completeReview}\r\n                  />\r\n                ))}\r\n              </ul>\r\n            )}\r\n          </div>\r\n        )}\r\n        {/* Candidates Table */}\r\n        <div className=\"flex-1\">\r\n          {loading && !selectedVacancy ? (\r\n            <div className=\"flex items-center justify-center h-[50vh]\">\r\n              <Loading height=\"h-[50vh]\" />\r\n            </div>\r\n          ) : selectedVacancy && !vacancyid && candidates ? (\r\n            <>\r\n              <div className=\"flex justify-between items-center\">\r\n                {selectedVacancy && (\r\n                  <h3 className=\"text-lg font-semibold mb-2\">\r\n                    &quot;Candidates for {selectedVacancy.refno}&quot;\r\n                  </h3>\r\n                )}\r\n                {((!mercuryPortal && IS_LOCK_FEATURE_DISABLED === \"true\") || isRegenerationReadOnly) && (\r\n                  <div className=\"bg-blue-200 px-2 py-1 mb-1 rounded-md flex items-center gap-2 mr-2\">\r\n                    <LockKeyhole size={15} />\r\n                    <h6 className=\"text-sm\">\r\n                    {isRegenerationReadOnly\r\n                      ? \"The below table is read-only (Regeneration in progress)\"\r\n                      : \"The below table is read-only\"\r\n                    }\r\n                  </h6>\r\n                  </div>\r\n                )}\r\n              </div>\r\n              <div className=\"border border-gray-800 mt-2 rounded-lg\">\r\n                <CandidateTable\r\n                  handleCandidateSort={handleCandidateSort}\r\n                  isResumeModalOpen={isResumeModalOpen}\r\n                  loading={loading}\r\n                  paginatedCandidates={paginatedCandidates}\r\n                  selectedVacancy={selectedVacancy}\r\n                  candidates={candidates}\r\n                  setCandidates={setCandidates}\r\n                  vacancyCandidates={vacancyCandidates}\r\n                  setVacancyCandidates={setVacancyCandidates}\r\n                  fetchResume={fetchResume}\r\n                  mercuryPortal={(mercuryPortal ?? false) || isRegenerationReadOnly}\r\n                  IS_LOCK_FEATURE_DISABLED={IS_LOCK_FEATURE_DISABLED}\r\n                  fetchCandidatesById={fetchCandidatesById}\r\n                />\r\n              </div>\r\n              {/* Pagination */}\r\n              <div className=\"flex justify-between items-center mt-4\">\r\n                <Select\r\n                  value={limit as unknown as string}\r\n                  onValueChange={(val: string) => {\r\n                    setLimit(Number(val));\r\n                    setPage(1);\r\n                  }}\r\n                >\r\n                  <SelectTrigger className=\"w-32\">\r\n                    <SelectValue placeholder=\"Select per page-\" />\r\n                  </SelectTrigger>\r\n                  <SelectContent>\r\n                    <SelectGroup>\r\n                      {[50, 100, 150, 200, 500].map((size) => (\r\n                        <SelectItem\r\n                          key={size}\r\n                          value={size as unknown as string}\r\n                        >\r\n                          {size} per page\r\n                        </SelectItem>\r\n                      ))}\r\n                    </SelectGroup>\r\n                  </SelectContent>\r\n                </Select>\r\n                <div>\r\n                  <Button\r\n                    variant={\"outline\"}\r\n                    onClick={() => setPage((p) => Math.max(p - 1, 1))}\r\n                    disabled={page === 1}\r\n                    className=\"px-2 py-0.5 mr-1\"\r\n                  >\r\n                    <ChevronLeft />\r\n                  </Button>\r\n                  <Button\r\n                    variant={\"outline\"}\r\n                    onClick={() =>\r\n                      setPage((p) =>\r\n                        p * limit < filteredCandidates.length ? p + 1 : p\r\n                      )\r\n                    }\r\n                    className=\"px-2 py-0.5\"\r\n                    disabled={\r\n                      Math.ceil(filteredCandidates.length / limit) === page\r\n                    }\r\n                  >\r\n                    <ChevronRight />\r\n                  </Button>\r\n                </div>\r\n              </div>\r\n            </>\r\n          ) : selectedVacancy && vacancyid ? (\r\n            <div className=\"\">\r\n              {SHOW_SEARCH_BY_EMAIL && (\r\n                <Input\r\n                  type=\"search\"\r\n                  placeholder=\"Search by email\"\r\n                  value={search}\r\n                  onChange={(e) => setSearch(e.target.value)}\r\n                  className=\"w-1/3 border border-gray-300 rounded-lg p-2 my-2\"\r\n                />\r\n              )}\r\n              <div className=\"flex gap-2 mb-2\">\r\n                {/* <Button\r\n                  variant=\"default\"\r\n                  onClick={saveShortlistedCandidates}\r\n                  disabled={!selectedRows.length}\r\n                  className=\"mb-2\"\r\n                >\r\n                  Shortlist\r\n                </Button> */}\r\n              </div>\r\n              {mercuryPortal && IS_LOCK_FEATURE_DISABLED === \"true\" && (\r\n                <div className=\"bg-blue-200 px-2 py-1 mb-1 rounded-md flex justify-between items-center\">\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <LockKeyhole size={15} />\r\n                    <h6 className=\"text-sm\">The below table is read-only</h6>\r\n                  </div>\r\n                  <Badge variant=\"outline\" className=\"bg-blue-200\">\r\n                    Read only\r\n                  </Badge>\r\n                </div>\r\n              )}\r\n              {/* regenerate */}\r\n                {!loading && selectedVacancy && (\r\n                  <div className=\"flex flex-col gap-2 mt-4 mb-2\">\r\n                    <div className=\"flex gap-4 items-center justify-end\">\r\n                      {regenerationData && regenerationData.initiated_at && (\r\n                        <span className=\"text-sm text-gray-600\">\r\n                          Last Regeneration : {unFormattedDateWithBrowserTimezone(regenerationData.initiated_at)}\r\n                        </span>\r\n                      )}\r\n                      <Button\r\n                        onClick={regenerateCatalystMatch}\r\n                        disabled={!canRegenerate}\r\n                        className={!canRegenerate ? \"bg-gray-400 hover:bg-gray-400 cursor-not-allowed\" : \"\"}\r\n                      >\r\n                        Regenerate\r\n                      </Button>\r\n                    </div>\r\n\r\n                    {/* Regeneration message */}\r\n                    {regenerationMessage && (\r\n                      <div className=\"text-sm text-gray-700 text-right\">\r\n                        {regenerationMessage}\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                )}\r\n              <div className=\"border border-gray-800 rounded-lg\">\r\n                <CandidateTable\r\n                  handleCandidateSort={handleCandidateSort}\r\n                  isResumeModalOpen={isResumeModalOpen}\r\n                  loading={loading}\r\n                  paginatedCandidates={paginatedCandidates}\r\n                  selectedVacancy={selectedVacancy}\r\n                  candidates={candidates}\r\n                  setCandidates={setCandidates}\r\n                  vacancyCandidates={vacancyCandidates}\r\n                  setVacancyCandidates={setVacancyCandidates}\r\n                  fetchResume={fetchResume}\r\n                  mercuryPortal={(mercuryPortal ?? false) || isRegenerationReadOnly}\r\n                  IS_LOCK_FEATURE_DISABLED={IS_LOCK_FEATURE_DISABLED}\r\n                  fetchCandidatesById={fetchCandidatesById}\r\n                />\r\n              </div>\r\n              {/* Pagination */}\r\n              <div className=\"flex justify-between items-center mt-4\">\r\n                <Select\r\n                  value={limit as unknown as string}\r\n                  onValueChange={(val: string) => {\r\n                    setLimit(Number(val));\r\n                    setPage(1);\r\n                  }}\r\n                >\r\n                  <SelectTrigger className=\"w-32\">\r\n                    <SelectValue placeholder=\"Select per page-\" />\r\n                  </SelectTrigger>\r\n                  <SelectContent>\r\n                    <SelectGroup>\r\n                      {[50, 100, 150, 200, 500].map((size) => (\r\n                        <SelectItem\r\n                          key={size}\r\n                          value={size as unknown as string}\r\n                        >\r\n                          {size} per page\r\n                        </SelectItem>\r\n                      ))}\r\n                    </SelectGroup>\r\n                  </SelectContent>\r\n                </Select>\r\n                <div>\r\n                  <Button\r\n                    variant={\"outline\"}\r\n                    onClick={() => setPage((p) => Math.max(p - 1, 1))}\r\n                    disabled={page === 1}\r\n                    className=\"px-2 py-0.5 mr-1\"\r\n                  >\r\n                    <ChevronLeft />\r\n                  </Button>\r\n                  <Button\r\n                    variant={\"outline\"}\r\n                    onClick={() =>\r\n                      setPage((p) =>\r\n                        p * limit < filteredCandidates.length ? p + 1 : p\r\n                      )\r\n                    }\r\n                    className=\"px-2 py-0.5\"\r\n                    disabled={\r\n                      Math.ceil(filteredCandidates.length / limit) === page\r\n                    }\r\n                  >\r\n                    <ChevronRight />\r\n                  </Button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          ) : candidates === undefined && mercuryPortal ? (\r\n            <div className=\"m-auto w-full max-w-md p-4 bg-red-100 border border-red-400 rounded-lg\">\r\n              <p className=\"text-red-500 text-center\">\r\n                Vacancy Id parameter not passed in by Mercury. Contact support\r\n                if this persists.\r\n              </p>\r\n            </div>\r\n          ) : (\r\n            candidates?.length === 0 && (\r\n              <div className=\"relative flex items-center justify-end h-[50vh] flex-col\">\r\n                <p className=\"text-3xl text-gray-700 mb-4\">\r\n                  No candidates found\r\n                </p>\r\n                <Image src={noCandidatesImage} alt=\"no-candidates-image\" />\r\n                <p className=\"text-gray-500 text-sm absolute bottom-2 italic\">\r\n                  (No Matching Vacancy Available parameter passed. Contact support if\r\n                  this persists.)\r\n                </p>\r\n              </div>\r\n            )\r\n          )}\r\n        </div>\r\n      </div>\r\n      <CandidateResume\r\n        vacancy={selectedVacancy}\r\n        selectedResume={selectedResume}\r\n        setSelectedResume={setSelectedResume}\r\n        isResumeModalOpen={isResumeModalOpen}\r\n        setIsResumeModalOpen={setIsResumeModalOpen}\r\n        isLoading={loading}\r\n      />\r\n      {vacancyid === undefined && !loading && pageNotFound && mercuryPortal && (\r\n        <div className=\"bg-white border-t p-4 h-screen flex items-center justify-center\">\r\n          <p>\r\n            <span className=\"text-lg mr-1\">404</span>Page not found\r\n          </p>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Candidates;\r\n"], "names": [], "mappings": ";;;;AACA;AAOA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAQA;AACA;AACA;AACA;AACA;AAnCA;AAAA;AAAA;AAAA;;;AAFA;;;;;;;;;;;;;;;;;;;;AAuCA,MAAM,0BAA0B;AAC9B,kEAAkE;AAEpE,MAAM,mBAAmB;AACzB,MAAM,uBAAuB;AAoB7B,MAAM,aAAa,CAAC,EAClB,SAAS,EACT,aAAa,EACb,OAAO,EACP,wBAAwB,EAMzB;;;IACC,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,eACJ,cAAc,aACd,CAAC,SAAS,QAAQ,CAAC,CAAC,8CAA8C,CAAC;IACrE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACxD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAG/C,CAAC;IACX,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IACxE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAC5D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAChD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACnE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjD,MAAM,CAAC,0BAA0B,4BAA4B,GAC3D,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACpB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAGxC;QACD,KAAK;QACL,WAAW;IACb;IACA,MAAM,UAAU,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACzB,MAAM,OAAO,SAAS,MAAM,MAAM;IAClC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IACvD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB;IAC9E,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACvE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,2EAA2E;IAC3E,MAAM,6BAA6B,CAAC;QAClC,IAAI,CAAC,aAAa,OAAO;QAEzB,MAAM,mBAAmB,IAAI,KAAK;QAClC,MAAM,MAAM,IAAI;QAChB,MAAM,eAAe,IAAI,KAAK,KAAK,MAAM,0BAA0B;QAEnE,OAAO,AAAC,IAAI,OAAO,KAAK,iBAAiB,OAAO,MAAO;IACzD;IAEA,wDAAwD;IACxD,MAAM,0BAA0B,CAAC;QAC/B,MAAM,SAAS,oBAAoB,MAAM;QACzC,MAAM,cAAc,oBAAoB,YAAY;QACpD,MAAM,cAAc,oBAAoB,YAAY;QACpD,MAAM,cAAc,WAAW;QAE/B,IAAI,WAAW,YAAY,WAAW,aAAa;YACjD,0BAA0B;YAC1B,kBAAkB;YAClB,0BAA0B;YAC1B,iBAAiB;YACjB,uBAAuB;YAEvB,IAAI,eAAe,gBAAgB,aAAa;gBAC9C,2CAA2C;gBAC3C,MAAM,CAAC,qCAAqC,EAAE,YAAY,mCAAmC,CAAC;YAChG;QACF,OAAO,IAAI,WAAW,aAAa;YACjC,yBAAyB;YACzB,kBAAkB;YAElB,MAAM,mBAAmB,2BAA2B;YACpD,iBAAiB;YACjB,0BAA0B,CAAC;YAE3B,IAAI,kBAAkB;gBACpB,uBAAuB;YACzB,OAAO;gBACL,uBAAuB;YACzB;QACF,OAAO,IAAI,WAAW,SAAS;YAC7B,sBAAsB;YACtB,kBAAkB;YAClB,0BAA0B;YAC1B,iBAAiB;YACjB,uBAAuB;QACzB,OAAO;YACL,sCAAsC;YACtC,kBAAkB;YAClB,0BAA0B;YAC1B,iBAAiB;YACjB,uBAAuB;QACzB;IACF;IAEA,wCAAwC;IACxC,MAAM,0BAA0B,OAAO,0BAAmC,KAAK;QAC7E,IAAI,CAAC,WAAW;QAEhB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,eAAe,EAAE,UAAU,oBAAoB,CAAC;YAE9E,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAoC,MAAM,SAAS,IAAI;gBAC7D,MAAM,sBAAsB,KAAK,qBAAqB;gBAEtD,oBAAoB;gBACpB,wBAAwB;gBAExB,MAAM,SAAS,oBAAoB,MAAM;gBAEzC,IAAI,WAAW,aAAa;oBAC1B,wEAAwE;oBACxE,IAAI,iBAAiB;wBACnB,cAAc;wBACd,mBAAmB;oBACrB;oBAEA,IAAI,2BAA2B,eAAe;wBAC5C,MAAM,oBAAoB;oBAC5B;oBACA,QAAQ,GAAG,CAAC;gBACd,OAAO,IAAI,WAAW,SAAS;oBAC7B,oCAAoC;oBACpC,IAAI,iBAAiB;wBACnB,cAAc;wBACd,mBAAmB;oBACrB;oBACA,QAAQ,KAAK,CAAC;gBAChB;YACA,yDAAyD;YAC3D,OAAO;gBACL,QAAQ,KAAK,CAAC,wCAAwC,SAAS,UAAU;YAC3E;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;QACvD;IACF;IAEA,oDAAoD;IACpD,MAAM,eAAe;QACnB,IAAI,iBAAiB;YACnB,cAAc;QAChB;QAEA,MAAM,WAAW,YAAY,IAAM,wBAAwB,OAAO,QAAQ,wBAAwB;QAClG,mBAAmB;IACrB;IAEA,MAAM,0BAA0B;QAC5B,IAAI,CAAC,aAAa,CAAC,eAAe;QAElC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,eAAe,EAAE,UAAU,0BAA0B,CAAC,EAAE;gBACpF,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,gBAAgB;gBAClB;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAA4B,MAAM,SAAS,IAAI;gBACrD,oBAAoB;gBACpB,wBAAwB;gBACxB,QAAQ,GAAG,CAAC,0CAA0C;gBAEtD,mCAAmC;gBACnC;YACF,OAAO;gBACL,6DAA6D;gBAC7D,IAAI,SAAS,MAAM,KAAK,KAAK;oBAC3B,MAAM,2BAA2B,sCAAsC;gBACzE,OAAO;oBACL,QAAQ,KAAK,CAAC,wCAAwC,SAAS,UAAU;gBAC3E;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;QACtD;IACF;IAEF,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,SAAS;gBACX,aAAa,OAAO,CAAC,WAAW;gBAChC,YAAY;YACd;QACF;+BAAG;QAAC;KAAQ;IAEZ,wEAAwE;IACxE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR;wCAAO;oBACL,IAAI,iBAAiB;wBACnB,cAAc;wBACd,mBAAmB;oBACrB;gBACF;;QACF;+BAAG;QAAC;KAAgB;IAEpB,gFAAgF;IAChF,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,iBAAiB;gBACnB,cAAc;gBACd,mBAAmB;YACrB;YACA,kBAAkB;YAClB,oBAAoB;YAEpB,mDAAmD;YACnD,IAAI,WAAW;gBACb;YACF;QACF;+BAAG;QAAC;KAAU;IAEd,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAG9C;QACD,KAAK;QACL,WAAW;IACb;IAEA,MAAM,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,kBAAe,AAAD;IAE3C,MAAM,yBAAyB;QAC7B,iBAAiB;QACjB,4BAA4B;IAC9B;IAEA,MAAM,iBAAiB;QACrB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,6EAMb,MAAM,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,kBAAkB,CAAC,GAAG;gBAAE,SAAS;YAAe;YACvE,IAAI,SAAS,EAAE,EAAE;gBACf,8DAA8D;gBAC9D,MAAM,OAAiC,MAAM,SAAS,IAAI;gBAC1D,IAAI,KAAK,SAAS,EAAE;oBAClB,wCAA8B;wBAC5B,aAAa,KAAK,SAAS;oBAC7B;gBACF;gBACA,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,WAAW;oBAC3B,MAAM;oBACN,YAAY;wBACV,OAAO,KAAK,SAAS,EAAE,UAAU;wBACjC,SAAS;oBACX;gBACF;gBACA,OAAO,KAAK,SAAS;YACvB,OAAO;gBACL,QAAQ,KAAK,CAAC,+BAA+B,SAAS,UAAU;YAClE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,eAAe;gBAC/B,OAAO,IAAI,MAAM,iCAAiC;gBAClD,eAAe;YACjB;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,sBAAsB,OAAO;QACjC,IAAI;YACF,WAAW;YACX,uCAA6B;;YAG7B;YACA,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAChC,CAAC,eAAe,EAAE,UAAU,KAAK,CAAC,KAAK,IAAI,CAAC,MAAM,EAClD,CAAC,GACD;gBAAE,SAAS;YAAoB;YAEjC,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAoC,MAAM,SAAS,IAAI;gBAC7D,cAAc,KAAK,UAAU;gBAC7B,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,WAAW;oBAC3B,MAAM;oBACN,YAAY;wBACV;wBACA,SAAS;oBACX;gBACF;YACF,OAAO;gBACL,QAAQ,KAAK,CAAC,+BAA+B,SAAS,UAAU;YAClE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,eAAe;gBAC/B,OAAO,IAAI,MAAM,sCAAsC;gBACvD,eAAe;YACjB;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,iBAAiB,IAAI;wCAAC,CAAC;oBACrB,IAAI,aAAa,UAAU,MAAM,GAAG,GAAG;wBACrC,WAAW;oDAAI,CAAC;gCACd,IAAI,SAAS,YAAY,kBAAkB,WAAW,eAAe;oCACnE,mBAAmB;gCACrB;4BACF;;oBACF;gBACF;;YACA,IAAI,aAAa,eAAe;gBAC9B,oBAAoB;YACtB;QACF;+BAAG;QAAC;QAAW;KAAc;IAE7B,MAAM,aAAa,CAAC,MAAc,OAAO;;QACvC,IAAI,YAAmC;QACvC,IAAI,WAAW,GAAG,KAAK,OAAO,WAAW,SAAS,KAAK,OAAO;YAC5D,YAAY;QACd;QAEA,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAG3C;QAEV,MAAM,aAAa;eAAI;SAAU,CAAC,IAAI,CAAC,CAAC,GAAG;YACzC,IAAI,EAAE,KAAK,GAAG,EAAE,KAAK,EAAE,OAAO,cAAc,QAAQ,CAAC,IAAI;YACzD,IAAI,EAAE,KAAK,GAAG,EAAE,KAAK,EAAE,OAAO,cAAc,QAAQ,IAAI,CAAC;YACzD,OAAO;QACT;QAEA,cAAc;YAAE;YAAK;QAAU;QAC/B,aAAa;IACf;QAnBM;IAqBN,MAAM,sBAAsB,CAAC;QAC3B,IAAI,YAA4B;QAChC,IAAI,cAAc,GAAG,KAAK,OAAO,cAAc,SAAS,KAAK,OAAO;YAClE,YAAY;QACd;QAEA,MAAM,WAAW,CAAC;YAChB,MAAM,OAAO,UAAU,cAAc;YAErC,OAAQ;gBACN,KAAK;oBACH,OAAO,KAAK,IAAI,EAAE,iBAAiB;gBACrC,KAAK;oBACH,OAAO,IAAI,CAAC,uBAAuB,EAAE,gBAAgB;gBACvD,KAAK;oBACH,OAAO,IAAI,CAAC,uBAAuB,EAAE,iBAAiB;gBACxD,KAAK;oBACH,OAAO,IAAI,CAAC,uBAAuB,EAAE,mBAAmB;gBAC1D,KAAK;oBACH,OAAO,IAAI,CAAC,uBAAuB,EAAE,CAAC,mBAAmB,IAAI;gBAC/D,KAAK;oBACH,OAAO,IAAI,CAAC,uBAAuB,EAAE,uBAAuB;gBAC9D,KAAK;oBACH,OACE,IAAI,CAAC,uBAAuB,EAAE,CAAC,6BAA6B,IAAI;gBAEpE,KAAK;oBACH,OAAO,IAAI,CAAC,uBAAuB,EAAE,2BAA2B;gBAClE,KAAK;oBACH,OAAO,IAAI,CAAC,uBAAuB,EAAE,2BAA2B;gBAClE,KAAK;oBACH,OAAO,IAAI,CAAC,uBAAuB,EAAE,0BAA0B;gBACjE,KAAK;oBAAgB;wBACnB,MAAM,eAAe,KAAK,YAAY;wBACtC,IAAI,CAAC,gBAAgB,iBAAiB,WAAW,OAAO,IAAI,KAAK;wBACjE,OAAO,IAAI,KAAK;oBAClB;gBACA,KAAK;oBACH,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG;oBACxB,MAAM,UAAU,CAAC,IACf,KAAK,MAAM;oBACb,IAAI,QAAQ,SAAS,QAAQ,QAC3B,OAAO,GAAG,KAAK,EAAE,EAAE,OAAO,CAAC,WAAW;oBACxC,IAAI,QAAQ,OAAO,OAAO,KAAK,WAAW;oBAC1C,IAAI,QAAQ,QAAQ,OAAO,MAAM,WAAW;oBAC5C,OAAO;gBACT,KAAK;oBACH,OAAO,IAAI,CAAC,uBAAuB,EAAE,0BAA0B;gBACjE,KAAK;oBACH,OAAO,IAAI,CAAC,kBAAkB;gBAChC;oBACE,OAAO;YACX;QACF;QAEA,MAAM,mBAAmB;eAAI;SAAW,CAAC,IAAI,CAAC,CAAC,GAAG;YAChD,MAAM,SAAS,SAAS;YACxB,MAAM,SAAS,SAAS;YAExB,IAAI,UAAU,QAAQ,UAAU,MAAM,OAAO;YAC7C,IAAI,UAAU,MAAM,OAAO;YAC3B,IAAI,UAAU,MAAM,OAAO,CAAC;YAE5B,IAAI,OAAO,WAAW,YAAY,OAAO,WAAW,UAAU;gBAC5D,OAAO,cAAc,QACjB,OAAO,aAAa,CAAC,UACrB,OAAO,aAAa,CAAC;YAC3B;YAEA,IAAI,SAAS,QAAQ,OAAO,cAAc,QAAQ,CAAC,IAAI;YACvD,IAAI,SAAS,QAAQ,OAAO,cAAc,QAAQ,IAAI,CAAC;YACvD,OAAO;QACT;QAEA,iBAAiB;YAAE;YAAK;QAAU;QAClC,cAAc;IAChB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,mBAAmB;QACnB,QAAQ;QACR,iBAAiB;YACf,KAAK;YACL,WAAW;QACb;QACA,oBAAoB,QAAQ,UAAU;IACxC;IAEA,MAAM,qBAAqB,YAAY,OAAO,CAAC,YAC7C,UAAU,cAAc,CAAC,KAAK,EAAE,cAAc,SAAS,OAAO,WAAW;IAG3E,MAAM,sBAAsB,oBAAoB,MAC9C,CAAC,OAAO,CAAC,IAAI,OACb,OAAO;IAGT,MAAM,kBAAkB,CAAC,aAAa,EAAE,EAAE,MAAM,CAAC,CAAC,UAChD,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,cAAc,WAAW;IAGhE,MAAM,cAAc,OAAO;QACzB,IAAI;YACF,WAAW;YACX,qBAAqB;YACrB,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAChC,6EAEI,CAAC,sBAAsB,EAAE,UAAU,mBAAmB,EAAE,EAC5D,CAAC,GACD;gBAAE,SAAS;YAAY;YAEzB,IAAI,SAAS,EAAE,EAAE;gBACf,8DAA8D;gBAC9D,MAAM,OAAY,MAAM,SAAS,IAAI;gBACrC,kBAAkB;gBAClB,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,WAAW;oBAC3B,MAAM;oBACN,YAAY;wBACV,aAAa,UAAU,mBAAmB;oBAC5C;gBACF;YACF,OAAO;gBACL,QAAQ,KAAK,CAAC,2BAA2B,SAAS,UAAU;YAC9D;QACA,8DAA8D;QAChE,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,0BAA0B;YACxC,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,eAAe;gBAC/B,OAAO,IAAI,MAAM,iCAAiC;gBAClD,eAAe;YACjB;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,UAAU;YACb,iBAAiB,mCAAmC;YACpD;QACF,OAAO,IAAI,CAAC,6BAA6B,IAAI,CAAC,WAAW;YACvD,iBAAiB,sCAAsC;YACvD;QACF;QACA,aAAa,OAAO,CAAC,YAAY;QACjC,IAAI;YACF,iBAAiB;YACjB,MAAM,aAAa;gBACjB,UAAU;YACZ;YACA,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAChC,CAAC,eAAe,EAAE,iBAAiB,WAAW,MAAM,CAAC,EACrD;gBACE,QAAQ;gBACR,SAAS;oBACP,QAAQ;oBACR,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB,GACA;gBAAE,SAAS;YAAc;YAE3B,MAAM,OAA+D,CACnE,MAAM,SAAS,IAAI,EACrB,GAAG;YACH,IAAI,iBAAiB;gBACnB,MAAM,aAAsB;oBAC1B,GAAG,eAAe;oBAClB,WAAW,KAAK,SAAS;oBACzB,iBAAiB,KAAK,eAAe;gBACvC;gBAEA,MAAM,iBAAiB,UAAU,GAAG,CAAC,CAAC,OACpC,KAAK,UAAU,KAAK,YAAY,aAAa,aAAa;gBAE5D,mBAAmB;gBACnB,aAAa;YACf;YACA,IAAI,MAAM;gBACR,iBAAiB;gBACjB,eAAe;gBACf,iBAAiB,CAAC,mCAAmC,CAAC,EAAE;gBACxD,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,WAAW;oBAC3B,MAAM;oBACN,YAAY;wBACV,WAAW,iBAAiB;wBAC5B,UAAU;wBACV,SAAS;oBACX;gBACF;YACF,OAAO;gBACL,MAAM;YACR;QACF,EAAE,OAAO,OAAY;YACnB,iBAAiB,CAAC,wCAAwC,CAAC,EAAE;YAC7D,iBAAiB;YACjB,eAAe;YACf,QAAQ,KAAK,CAAC,WAAW;YACzB,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,eAAe;gBAC/B,OAAO,IAAI,MAAM,mCAAmC;gBACpD,eAAe;YACjB;QACF;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI;YACF,iBAAiB;YACjB,MAAM,aAAa;gBACjB,UAAU;YACZ;YACA,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAChC,CAAC,eAAe,EAAE,iBAAiB,WAAW,SAAS,CAAC,EACxD;gBACE,QAAQ;gBACR,SAAS;oBACP,QAAQ;oBACR,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB,GACA;gBAAE,SAAS;YAAiB;YAE9B,MAAM,OAIF,CAAC,MAAM,SAAS,IAAI,EAAE,GAAG;YAC7B,IAAI,iBAAiB;gBACnB,MAAM,aAAsB;oBAC1B,GAAG,eAAe;oBAClB,WAAW,KAAK,SAAS;oBACzB,iBAAiB,KAAK,eAAe;oBACrC,WAAW,KAAK,SAAS;gBAC3B;gBAEA,MAAM,iBAAiB,UAAU,GAAG,CAAC,CAAC,OACpC,KAAK,UAAU,KAAK,YAAY,aAAa,aAAa;gBAE5D,mBAAmB;gBACnB,aAAa;YACf;YAEA,IAAI,MAAM;gBACR,kBAAkB;gBAClB,iBAAiB;gBACjB,iBAAiB,CAAC,0BAA0B,CAAC,EAAE;gBAC/C,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,WAAW;oBAC3B,MAAM;oBACN,YAAY;wBACV,WAAW,iBAAiB;wBAC5B,UAAU;wBACV,SAAS;oBACX;gBACF;YACF,OAAO;gBACL,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,iBAAiB,CAAC,yCAAyC,CAAC,EAAE;YAC9D,iBAAiB;YACjB,QAAQ,KAAK,CAAC,WAAW;YACzB,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,eAAe;gBAC/B,OAAO,IAAI,MAAM,sCAAsC;gBACvD,eAAe;YACjB;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;YACZ,0CACC,6LAAC,8IAAA,CAAA,UAAc;gBACb,SAAS;gBACT,kBAAkB;oBAChB,iBAAiB;oBACjB,4BAA4B;gBAC9B;gBACA,eAAe,iBAAiB;;;;;;YAGnC,CAAC,aAAa,aAAa,mBAC1B,6LAAC;gBAAG,WAAU;0BAAsC;;;;;;YAErD,CAAC,aAAa,CAAC,8BACd,6LAAC;gBAAG,WAAU;0BAAsC;;;;;uBAIpD,iCACE;0BACE,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAiC;;;;;;sCAG/C,6LAAC;4BAAK,WAAU;sCACd,cAAA,6LAAC;gCACC,MAAK;gCACL,SAAS,CAAC;oCACR,EAAE,cAAc;oCAChB;gCACF;gCACA,WAAU;0CACX;;;;;;;;;;;;;;;;;;0BASX,6LAAC;gBAAI,WAAU;;oBAEZ,CAAC,aAAa,CAAC,+BACd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,WAAU;gCACV,SAAS,IAAM;;oCAChB;oCACc;kDACb,6LAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;0CAEzB,6LAAC;0CACC,cAAA,6LAAC,6HAAA,CAAA,QAAK;oCACJ,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;oCAChD,WAAU;;;;;;;;;;;4BAGb,WAAW,CAAC,gBAAgB,MAAM,iBACjC,6LAAC,yHAAA,CAAA,UAAO;gCAAC,QAAO;;;;;qDAEhB,6LAAC;gCAAG,WAAU;0CACX,gBAAgB,GAAG,CAAC,CAAC,wBACpB,6LAAC,2IAAA,CAAA,UAAW;wCACV,SAAS;wCACT,oBAAoB;wCACpB,iBAAiB;wCACjB,WAAW;wCAEX,eAAe;wCACf,kBAAkB;wCAClB,aAAa;wCACb,gBAAgB;wCAChB,gBAAgB;wCAChB,mBAAmB;wCACnB,eAAe;wCACf,UAAU;wCACV,aAAa;wCACb,aAAa;wCACb,gBAAgB;uCAXX,QAAQ,KAAK;;;;;;;;;;;;;;;;kCAmB9B,6LAAC;wBAAI,WAAU;kCACZ,WAAW,CAAC,gCACX,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,yHAAA,CAAA,UAAO;gCAAC,QAAO;;;;;;;;;;mCAEhB,mBAAmB,CAAC,aAAa,2BACnC;;8CACE,6LAAC;oCAAI,WAAU;;wCACZ,iCACC,6LAAC;4CAAG,WAAU;;gDAA6B;gDACnB,gBAAgB,KAAK;gDAAC;;;;;;;wCAG/C,CAAC,AAAC,CAAC,iBAAiB,6BAA6B,UAAW,sBAAsB,mBACjF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uNAAA,CAAA,cAAW;oDAAC,MAAM;;;;;;8DACnB,6LAAC;oDAAG,WAAU;8DACb,yBACG,4DACA;;;;;;;;;;;;;;;;;;8CAMV,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,kJAAA,CAAA,UAAc;wCACb,qBAAqB;wCACrB,mBAAmB;wCACnB,SAAS;wCACT,qBAAqB;wCACrB,iBAAiB;wCACjB,YAAY;wCACZ,eAAe;wCACf,mBAAmB;wCACnB,sBAAsB;wCACtB,aAAa;wCACb,eAAe,CAAC,iBAAiB,KAAK,KAAK;wCAC3C,0BAA0B;wCAC1B,qBAAqB;;;;;;;;;;;8CAIzB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,8HAAA,CAAA,SAAM;4CACL,OAAO;4CACP,eAAe,CAAC;gDACd,SAAS,OAAO;gDAChB,QAAQ;4CACV;;8DAEA,6LAAC,8HAAA,CAAA,gBAAa;oDAAC,WAAU;8DACvB,cAAA,6LAAC,8HAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,6LAAC,8HAAA,CAAA,gBAAa;8DACZ,cAAA,6LAAC,8HAAA,CAAA,cAAW;kEACT;4DAAC;4DAAI;4DAAK;4DAAK;4DAAK;yDAAI,CAAC,GAAG,CAAC,CAAC,qBAC7B,6LAAC,8HAAA,CAAA,aAAU;gEAET,OAAO;;oEAEN;oEAAK;;+DAHD;;;;;;;;;;;;;;;;;;;;;sDASf,6LAAC;;8DACC,6LAAC,8HAAA,CAAA,SAAM;oDACL,SAAS;oDACT,SAAS,IAAM,QAAQ,CAAC,IAAM,KAAK,GAAG,CAAC,IAAI,GAAG;oDAC9C,UAAU,SAAS;oDACnB,WAAU;8DAEV,cAAA,6LAAC,uNAAA,CAAA,cAAW;;;;;;;;;;8DAEd,6LAAC,8HAAA,CAAA,SAAM;oDACL,SAAS;oDACT,SAAS,IACP,QAAQ,CAAC,IACP,IAAI,QAAQ,mBAAmB,MAAM,GAAG,IAAI,IAAI;oDAGpD,WAAU;oDACV,UACE,KAAK,IAAI,CAAC,mBAAmB,MAAM,GAAG,WAAW;8DAGnD,cAAA,6LAAC,yNAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;;;;;;;2CAKnB,mBAAmB,0BACrB,6LAAC;4BAAI,WAAU;;gCACZ,sCACC,6LAAC,6HAAA,CAAA,QAAK;oCACJ,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;oCACzC,WAAU;;;;;;8CAGd,6LAAC;oCAAI,WAAU;;;;;;gCAUd,iBAAiB,6BAA6B,wBAC7C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uNAAA,CAAA,cAAW;oDAAC,MAAM;;;;;;8DACnB,6LAAC;oDAAG,WAAU;8DAAU;;;;;;;;;;;;sDAE1B,6LAAC,6HAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;sDAAc;;;;;;;;;;;;gCAMlD,CAAC,WAAW,iCACX,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;gDACZ,oBAAoB,iBAAiB,YAAY,kBAChD,6LAAC;oDAAK,WAAU;;wDAAwB;wDACjB,CAAA,GAAA,iHAAA,CAAA,qCAAkC,AAAD,EAAE,iBAAiB,YAAY;;;;;;;8DAGzF,6LAAC,8HAAA,CAAA,SAAM;oDACL,SAAS;oDACT,UAAU,CAAC;oDACX,WAAW,CAAC,gBAAgB,qDAAqD;8DAClF;;;;;;;;;;;;wCAMF,qCACC,6LAAC;4CAAI,WAAU;sDACZ;;;;;;;;;;;;8CAKX,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,kJAAA,CAAA,UAAc;wCACb,qBAAqB;wCACrB,mBAAmB;wCACnB,SAAS;wCACT,qBAAqB;wCACrB,iBAAiB;wCACjB,YAAY;wCACZ,eAAe;wCACf,mBAAmB;wCACnB,sBAAsB;wCACtB,aAAa;wCACb,eAAe,CAAC,iBAAiB,KAAK,KAAK;wCAC3C,0BAA0B;wCAC1B,qBAAqB;;;;;;;;;;;8CAIzB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,8HAAA,CAAA,SAAM;4CACL,OAAO;4CACP,eAAe,CAAC;gDACd,SAAS,OAAO;gDAChB,QAAQ;4CACV;;8DAEA,6LAAC,8HAAA,CAAA,gBAAa;oDAAC,WAAU;8DACvB,cAAA,6LAAC,8HAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,6LAAC,8HAAA,CAAA,gBAAa;8DACZ,cAAA,6LAAC,8HAAA,CAAA,cAAW;kEACT;4DAAC;4DAAI;4DAAK;4DAAK;4DAAK;yDAAI,CAAC,GAAG,CAAC,CAAC,qBAC7B,6LAAC,8HAAA,CAAA,aAAU;gEAET,OAAO;;oEAEN;oEAAK;;+DAHD;;;;;;;;;;;;;;;;;;;;;sDASf,6LAAC;;8DACC,6LAAC,8HAAA,CAAA,SAAM;oDACL,SAAS;oDACT,SAAS,IAAM,QAAQ,CAAC,IAAM,KAAK,GAAG,CAAC,IAAI,GAAG;oDAC9C,UAAU,SAAS;oDACnB,WAAU;8DAEV,cAAA,6LAAC,uNAAA,CAAA,cAAW;;;;;;;;;;8DAEd,6LAAC,8HAAA,CAAA,SAAM;oDACL,SAAS;oDACT,SAAS,IACP,QAAQ,CAAC,IACP,IAAI,QAAQ,mBAAmB,MAAM,GAAG,IAAI,IAAI;oDAGpD,WAAU;oDACV,UACE,KAAK,IAAI,CAAC,mBAAmB,MAAM,GAAG,WAAW;8DAGnD,cAAA,6LAAC,yNAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;mCAKnB,eAAe,aAAa,8BAC9B,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAA2B;;;;;;;;;;mCAM1C,YAAY,WAAW,mBACrB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAA8B;;;;;;8CAG3C,6LAAC,gIAAA,CAAA,UAAK;oCAAC,KAAK,0SAAA,CAAA,UAAiB;oCAAE,KAAI;;;;;;8CACnC,6LAAC;oCAAE,WAAU;8CAAiD;;;;;;;;;;;;;;;;;;;;;;;0BASxE,6LAAC,+IAAA,CAAA,UAAe;gBACd,SAAS;gBACT,gBAAgB;gBAChB,mBAAmB;gBACnB,mBAAmB;gBACnB,sBAAsB;gBACtB,WAAW;;;;;;YAEZ,cAAc,aAAa,CAAC,WAAW,gBAAgB,+BACtD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;;sCACC,6LAAC;4BAAK,WAAU;sCAAe;;;;;;wBAAU;;;;;;;;;;;;;;;;;;AAMrD;GAl9BM;;QAWa,qIAAA,CAAA,cAAW;QAgCZ,iJAAA,CAAA,aAAU;QAmMG,4HAAA,CAAA,kBAAe;;;KA9OxC;uCAo9BS"}}, {"offset": {"line": 5677, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}