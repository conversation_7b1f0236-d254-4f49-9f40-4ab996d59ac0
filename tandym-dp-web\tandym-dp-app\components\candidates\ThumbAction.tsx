import { ThumbsDown, Thum<PERSON>Up, CircleX, Eye } from "lucide-react";
import React, { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { But<PERSON> } from "../ui/button";
import { Candidate, RecruiterReview, Vacancy } from "@/app/candidates/helper";
import { useNotification } from "@/hooks/useNotification";
import AppToolTip from "../AppToolTip";
import DiscardModal from "./DiscardModal";
import { unFormattedDateWithBrowserTimezoneInDDMMYY } from "@/utils/utils";

type ReviewState = "like" | "dislike" | "maybe" | null;

const ThumbAction = ({
  candidate,
  candidates,
  setCandidates,
  vacancyId,
  vacancyCandidates,
  setVacancyCandidates,
  vacancyRefNo,
  selectedVacancy,
  IS_LOCK_FEATURE_DISABLED,
  mercuryPortal,
}: {
  candidate: Candidate;
  candidates: Candidate[];
  setCandidates: React.Dispatch<React.SetStateAction<Candidate[]>>;
  vacancyId: string;
  vacancyCandidates: {
    [key: string]: Candidate[];
  };
  setVacancyCandidates: React.Dispatch<
    React.SetStateAction<{ [key: string]: Candidate[] } | null>
  >;
  vacancyRefNo: string;
  selectedVacancy: Vacancy;
  IS_LOCK_FEATURE_DISABLED: string;
  mercuryPortal: boolean;
}) => {
  const [reviewState, setReviewState] = useState<ReviewState>(null);
  const [showCommentBox, setShowCommentBox] = useState(false);
  const [viewState, setViewState] = useState(false);
  const [originalComment, setOriginalComment] = useState("");
  const [comment, setComment] = useState("");
  const commentBoxRef = useRef<HTMLDivElement | null>(null);
  const buttonRef = useRef<HTMLDivElement | null>(null);
  const [openAbove, setOpenAbove] = useState(false);
  const [isOpenDiscardModal, setIsOpenDiscardModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [feedbackDate, setFeedbackDate] = useState("");

  const { showNotification } = useNotification();

  useEffect(() => {
    const decision = candidate?.candidate_data?.recruiter_review_decision;
    if (decision && decision !== null) {
      setReviewState(decision.vote as ReviewState);
      setComment(decision.comment || "");
      setOriginalComment(decision.comment || "");
    }
    let lastFeedbackDate =
      candidate?.candidate_data?.recruiter_review_decision?.feedback_timestamp;
    if (lastFeedbackDate) {
      lastFeedbackDate =
        unFormattedDateWithBrowserTimezoneInDDMMYY(lastFeedbackDate);
      setFeedbackDate(lastFeedbackDate);
    }
  }, [candidate]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        commentBoxRef.current &&
        !commentBoxRef.current.contains(event.target as Node)
      ) {
        closeCommentBox();
      }
    };
    if (showCommentBox) {
      document.addEventListener("mousedown", handleClickOutside);
    }
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showCommentBox, comment, candidate]);

  useEffect(() => {
    if (showCommentBox && buttonRef.current) {
      const buttonRect = buttonRef.current.getBoundingClientRect();
      const windowHeight = window.innerHeight;
      setOpenAbove(buttonRect.bottom + 220 > windowHeight);
    }
  }, [showCommentBox]);

  const handleReviewClick = (state: ReviewState, isLike: boolean) => {
    setViewState(false);
    setReviewState((prev) => (prev === state ? null : state));
    handleSend(reviewState === state ? null : state);
    if (!isLike) {
      setShowCommentBox(true);
    } else {
      setShowCommentBox(false);
    }
  };

  const sendToastMessage = (
    comment: string,
    state: string | null,
    type: "success" | "error"
  ) => {
    let message = "";
    if (type === "error") {
      message = "Error submitting review. Please try again.";
    } else if (type === "success") {
      if (comment !== originalComment) {
        message = "Review submitted successfully.";
      } else if (state === "like") {
        message = "You've marked this candidate as a good fit.";
      } else if (state === "dislike") {
        message = "You've marked this candidate as not a good fit.";
      } else if (state === null) {
        message = "Your review action removed successfully.";
      }
    }
    showNotification(message, type);
  };

  const handleSend = async (state: ReviewState) => {
    setShowCommentBox(false);
    setIsOpenDiscardModal(false);
    const uName =
      localStorage.getItem("userName") || localStorage.getItem("emailId");
    const requestObj: RecruiterReview = {
      candidate_contact_id: candidate.candidate_contactid,
      vacancy_refno: vacancyRefNo,
      vote: state,
      comment: state === null ? "" : comment,
      reviewer_email: uName || selectedVacancy?.locked_by,
    };
    try {
      setLoading(true);
      const response = await fetch("/api/vacancies", {
        method: "POST",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestObj),
      });
      if (response.ok) {
        const updatedCandidates = candidates.map((can: Candidate) => {
          if (can.candidate_contactid === candidate.candidate_contactid) {
            return {
              ...can,
              candidate_data: {
                ...can.candidate_data,
                recruiter_review_decision: requestObj,
              },
            };
          }
          return can;
        });
        setCandidates(updatedCandidates);
        setVacancyCandidates({
          ...vacancyCandidates,
          [vacancyId]: updatedCandidates,
        });
        sendToastMessage(
          requestObj.comment,
          reviewState === state ? null : state,
          "success"
        );
        setOriginalComment(requestObj.comment);
      } else {
        sendToastMessage(
          requestObj.comment,
          reviewState === state ? null : state,
          "error"
        );
      }
    } catch (error) {
      console.error(error);
      showNotification("Error submitting review. Please try again.", "error");
    } finally {
      setLoading(false);
      setComment("");
    }
  };

  const handleDiscardChange = () => {
    setComment(
      candidate?.candidate_data?.recruiter_review_decision?.comment || ""
    );
    setIsOpenDiscardModal(false);
    setShowCommentBox(false);
  };

  const handleCancelChange = () => {
    setIsOpenDiscardModal(false);
  };

  const closeCommentBox = () => {
    const originalComment =
      candidate?.candidate_data?.recruiter_review_decision?.comment || "";
    if (comment.trim() !== originalComment.trim()) {
      setIsOpenDiscardModal(true);
    } else {
      setShowCommentBox(false);
    }
  };

  let isEditable =
    IS_LOCK_FEATURE_DISABLED === "true"
      ? !(IS_LOCK_FEATURE_DISABLED === "true")
      : !selectedVacancy?.is_locked && selectedVacancy?.locked_by;

  if (mercuryPortal) {
    isEditable = mercuryPortal;
  }

  return (
    <>
      <div className="relative flex" ref={buttonRef}>
        <div className="flex items-center space-x-2">
          <motion.div
            onClick={() =>
              isEditable
                ? handleReviewClick("like", reviewState === "like")
                : null
            }
            animate={{ scale: reviewState === "like" ? 1.3 : 1 }}
            transition={{ type: "spring", stiffness: 300 }}
            whileHover={{
              scale: reviewState === "like" ? 1.3 : !isEditable ? 1 : 1.1,
            }}
          >
            <ThumbsUp
              size={18}
              className={`cursor-pointer ${
                !isEditable
                  ? "text-green-950"
                  : reviewState === "like"
                  ? "text-green-700"
                  : "text-green-600"
              }`}
              fill={
                !isEditable
                  ? reviewState === "like"
                    ? "#14532d"
                    : "white"
                  : reviewState === "like"
                  ? "#43bc6e"
                  : "white"
              }
            />
          </motion.div>
          <motion.div
            onClick={() =>
              isEditable
                ? handleReviewClick("dislike", reviewState === "dislike")
                : null
            }
            animate={{ scale: reviewState === "dislike" ? 1.3 : 1 }}
            transition={{ type: "spring", stiffness: 300 }}
            whileHover={{
              scale: reviewState === "dislike" ? 1.3 : !isEditable ? 1 : 1.1,
            }}
          >
            <ThumbsDown
              size={18}
              className={`cursor-pointer ${
                !isEditable
                  ? "text-red-900"
                  : reviewState === "dislike"
                  ? "text-red-700"
                  : "text-red-600"
              }`}
              fill={
                !isEditable
                  ? reviewState === "dislike"
                    ? "#7f1d1d"
                    : "white"
                  : reviewState === "dislike"
                  ? "#f46161"
                  : "white"
              }
            />
          </motion.div>
          {(reviewState === "like" || reviewState === "dislike") &&
            candidate?.candidate_data?.recruiter_review_decision?.comment && (
              <motion.div
                transition={{ type: "spring", stiffness: 300 }}
                whileHover={{ scale: !showCommentBox ? 1.3 : 1.1 }}
              >
                <AppToolTip
                  text="View Comments"
                  header={
                    <Eye
                      onClick={() => {
                        setViewState(true);
                        setShowCommentBox(true);
                      }}
                      size={20}
                      className={`cursor-pointer text-blue-900`}
                    />
                  }
                />
              </motion.div>
            )}
        </div>
        <AnimatePresence>
          {showCommentBox && (
            <motion.div
              ref={commentBoxRef}
              initial={{ opacity: 0, y: openAbove ? -10 : 10, x: 10 }}
              animate={{ opacity: 1, y: 0, x: -180 }}
              exit={{ opacity: 0, y: openAbove ? -10 : 10 }}
              transition={{ duration: 0.3 }}
              className={`absolute z-[1] ${
                openAbove ? "bottom-10" : "top-10"
              } w-[300px] h-[200px] bg-white shadow-lg rounded-lg p-4 border`}
            >
              <div className="flex justify-between items-center mb-1">
                <p className="text-sm font-semibold">
                  {reviewState === "like"
                    ? "Why did you like this?"
                    : reviewState === "dislike"
                    ? "Why did you dislike this?"
                    : "What are you unsure about?"}
                </p>
                <CircleX
                  size={14}
                  className="cursor-pointer"
                  onClick={closeCommentBox}
                />
              </div>
              <textarea
                className={`w-full ${
                  viewState ? "h-[140px]" : "h-[100px]"
                } border p-2 rounded resize-none focus:outline-none focus:ring-2 focus:ring-blue-500`}
                placeholder="Share your thoughts..."
                value={comment}
                onChange={(e) => setComment(e.target.value)}
                disabled={viewState || loading}
              />
              {viewState ? null : (
                <div className="flex gap-2">
                  <Button
                    onClick={() => {
                      setComment(
                        candidate?.candidate_data?.recruiter_review_decision
                          ?.comment || ""
                      );
                      setShowCommentBox(false);
                    }}
                    disabled={comment.trim() === "" || loading}
                    className="w-full bg-gray-800 text-white py-1 rounded hover:bg-gray-900 transition px-0 ml-0"
                    size="icon"
                  >
                    Discard
                  </Button>

                  <Button
                    onClick={() => handleSend(reviewState)}
                    disabled={comment.trim() === "" || loading}
                    size="icon"
                    className="w-full bg-gray-800 text-white py-1 rounded hover:bg-gray-900 transition px-0 ml-0"
                  >
                    Save Comment
                  </Button>
                </div>
              )}
            </motion.div>
          )}
        </AnimatePresence>
        <DiscardModal
          saveChanges={() => handleSend(reviewState)}
          isOpenDiscardModal={isOpenDiscardModal}
          cancelTabChange={handleCancelChange}
          confirmTabChange={handleDiscardChange}
        />
      </div>
      <div className="!ml-0 text-[12px] mt-1">{feedbackDate}</div>
    </>
  );
};

export default ThumbAction;
