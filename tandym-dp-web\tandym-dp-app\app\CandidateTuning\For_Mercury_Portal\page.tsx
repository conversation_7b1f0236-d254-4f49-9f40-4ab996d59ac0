import VacancyCandidates from "@/components/candidates/Candidates";
import { emailInternalAddress } from "@/library/utils";
import { fetchEntitlements } from "@/api/serverActions";

const MercuryPortalComponent = async ({ searchParams }: any) => {
  const params = await searchParams;
  const vacancyId = params?.vacancyid;
  const emailId = params?.internalemailaddress ?? emailInternalAddress;
  const mercuryPortal = true;
  const IS_LOCK_FEATURE_DISABLED = "false";
 const entitlementResponse = await fetchEntitlements(emailId);

  const showCandidateTuningPage = true;
    // entitlementResponse &&
    // !entitlementResponse.error &&
    // entitlementResponse.entitlement?.candidate_tunning_page;


  if (!showCandidateTuningPage) {
    return (
      <div
      style={{
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        height: "50vh",
        padding: "2rem",
        margin: "1rem",
        textAlign: "center",
      }}
    >
      <p>You do not have access to this page.</p>
    </div>
    );
  }
  return (
    <div>
      <VacancyCandidates
        vacancyid={vacancyId}
        mercuryPortal={mercuryPortal}
        emailId={emailId}
        IS_LOCK_FEATURE_DISABLED={IS_LOCK_FEATURE_DISABLED}
      />
    </div>
  );
};

export default MercuryPortalComponent;
