import { ResumeData, WorkExperience } from "@/app/candidates/helper";
import React from "react";
import Loading from "../Loading";
import { DownloadIcon, ExternalLink } from "lucide-react";
import { isParsedResume } from "@/api/config";
import { Button } from "../ui/button";
import { Vacancy } from "../CandidateTable/helper";
import { getAppInsights } from "@/library/appInsights";

const showSoftSkills = process.env.NEXT_PUBLIC_IS_SHOW_SOFT_SKILLS_ENABLED === "true";

const SkillBadge = ({
  skill,
  styleClass,
}: {
  skill: string;
  styleClass?: string;
}) => {
  return (
    <span
      className={`px-3 py-1 rounded-full text-xs font-semibold text-gray-800 border border-gray-300 shadow-sm ${
        styleClass ?? ""
      }`}
    >
      {skill}
    </span>
  );
};

const CandidateResume = ({
  vacancy,
  selectedResume,
  setSelectedResume,
  isResumeModalOpen,
  setIsResumeModalOpen,
  isLoading,
}: {
  vacancy: Vacancy | null;
  selectedResume: ResumeData | null;
  setSelectedResume: React.Dispatch<React.SetStateAction<null | ResumeData>>;
  isResumeModalOpen: boolean;
  setIsResumeModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  isLoading: boolean;
}) => {
  const cv = selectedResume?.candidate?.resume_file;

  const legendSkillWeights = ["high", "medium", "normal"];

  const getSharePointURL = (url: string) => {
    url = url.toLocaleLowerCase();
    const baseURL = url.split("/contact")[0] || "";
    const idMatch = url.match(/\/sites.*$/);
    const encodedFileName = idMatch ? encodeURIComponent(idMatch[0]) : "";
    return `${baseURL}/_layouts/15/embed.aspx?Id=${encodedFileName}`;
  };

  const resultURL = cv ? getSharePointURL(cv) : "";

  const getFullWorkExperience = (selectedResume: ResumeData) => {
    return (
      <div className="mb-6 p-4 bg-gray-100 rounded-lg">
        <h3 className="text-xl font-bold text-gray-900 mb-2">
          Full Work Experience
        </h3>
        {selectedResume?.candidate?.["work experience"].length > 0 ? (
          <ul className="list-disc ml-5 space-y-3">
            {selectedResume?.candidate?.["work experience"].map(
              (exp: WorkExperience, index: number) => (
                <li key={index} className="p-3 bg-white rounded-md shadow">
                  <p className="font-semibold text-lg">
                    {exp.title} at {exp.company}
                  </p>
                  <p className="text-gray-600 text-sm">
                    {exp.start_date} - {exp.end_date || "Present"}
                  </p>
                  <p className="text-gray-700 max-h-[150px] overflow-y-auto">
                    {(() => {
                      const description = exp.description || "";
                      const softSkills =
                        vacancy?.vacancy_data?.["soft skills"] || [];
                      const technicalSkills =
                        vacancy?.vacancy_data?.["technical skills"] || [];
                      const toolsAndPlatforms =
                        vacancy?.vacancy_data?.["tools and platforms"] || [];
                      const allSkills = [
                        ...softSkills,
                        ...technicalSkills,
                        ...toolsAndPlatforms,
                      ];
                      const regex = new RegExp(
                        allSkills
                          .map(
                            (skill) =>
                              `\\b${skill.name.replace(
                                /[.*+?^${}()|[\]\\]/g,
                                "\\$&"
                              )}\\b`
                          )
                          .join("|"),
                        "gi"
                      );
                      const result: (string | React.ReactNode)[] = [];
                      let lastIndex = 0;
                      let match: RegExpExecArray | null;
                      while ((match = regex.exec(description)) !== null) {
                        if (match.index > lastIndex) {
                          result.push(
                            description.slice(lastIndex, match.index)
                          );
                        }
                        const matchedSkill = allSkills.find((skill) => {
                          const skillName =
                            typeof skill === "string" ? skill : skill.name;
                          return (
                            skillName &&
                            match &&
                            match[0] &&
                            skillName.toLowerCase() === match[0].toLowerCase()
                          );
                        });
                        const weight =
                          typeof matchedSkill === "object" &&
                          matchedSkill !== null
                            ? matchedSkill.weight
                            : undefined;
                        const colorClass = weight
                          ? getHighlightStyle(weight)
                          : "bg-gray-200 text-gray-700 border border-gray-300";
                        result.push(
                          <span
                            key={match.index + "-" + match[0]}
                            className={colorClass}
                          >
                            {match[0]}
                          </span>
                        );
                        lastIndex = regex.lastIndex;
                      }
                      if (lastIndex < description.length) {
                        result.push(description.slice(lastIndex));
                      }
                      return result;
                    })()}
                  </p>
                </li>
              )
            )}
          </ul>
        ) : (
          <p className="text-gray-500">No work experience available</p>
        )}
      </div>
    );
  };

  const getSkillBadgeHighlightClassName = (skill: string) => {
    if (!skill) return "";
    const skillLower = skill.toLowerCase();
    const softSkills = vacancy?.vacancy_data?.["soft skills"] || [];
    const technicalSkills = vacancy?.vacancy_data?.["technical skills"] || [];
    const toolsAndPlatforms =
      vacancy?.vacancy_data?.["tools and platforms"] || [];
    const allSkills = [...softSkills, ...technicalSkills, ...toolsAndPlatforms];
    const matchedSkill = allSkills.find(
      (skill) => skill.name.toLowerCase() === skillLower
    );
    return matchedSkill ? getHighlightStyle(matchedSkill.weight) : "";
  };

  const getParsedResume = () => {
    return (
      <>
        {cv ? (
          <>
            <div className="mb-6">
              <div className="flex justify-between my-4">
                <h3 className="text-xl font-bold text-gray-900">CV</h3>
                <a
                  href={cv}
                  target="_blank"
                  download
                  onClick={() => {
                    getAppInsights()?.trackEvent({
                      name: "FE_DownloadCVClicked",
                      properties: { resume: cv },
                    });
                  }}
                  className="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-4 py-2 rounded-lg transition-all flex items-center gap-2 z-10"
                >
                  Download CV <DownloadIcon />
                </a>
              </div>
              <iframe
                className="cv-renderer"
                src={resultURL}
                width="100%"
                height="600px"
              />
            </div>
          </>
        ) : (
          <div className="flex relative justify-center items-center text-3xl my-5">
            Resume is not available
          </div>
        )}
      </>
    );
  };

  // Add getHighlightStyle function for color based on weight
  const getHighlightStyle = (weight: string) => {
    switch (weight) {
      case "high":
        return "p-1 bg-green-200/50 text-green-800 border border-green-300 shadow-md";
      case "medium":
        return "p-1 bg-blue-200/50 text-blue-800 border border-blue-300 shadow-md";
      case "normal":
        return "p-1 bg-yellow-200/50 text-yellow-800 border border-yellow-300 shadow-md";
      case "low":
        return "p-1 bg-red-200/50 text-red-800 border border-red-300 shadow-md";
      default:
        return "p-1 bg-gray-200 text-gray-700 border border-gray-300";
    }
  };
  const handleResumeLinkClick = () => {
    getAppInsights()?.trackEvent({
      name: "FE_ResumeLinkClicked",
      properties: { resume: cv },
    });
  };

  return (
    <div>
      {" "}
      {/* Modal */}
      {isResumeModalOpen && (
        <div className="fixed z-50 inset-0 bg-gray-900 bg-opacity-60 flex items-center justify-center">
          <div className="bg-white w-[80vw] min-h-[60vh] px-8 rounded-lg max-h-[90vh] overflow-y-auto shadow-lg">
            {/* Header */}
            <div className="flex items-center justify-between text-gray-900 border-b pb-2 mb-4 sticky top-0 bg-white z-10 pt-8">
              <h2 className="text-2xl font-extrabold ">Candidate Profile</h2>
              <div className="flex">
                <Button
                  asChild
                  className="text-blue-500 text-lg"
                  variant="link"
                >
                  <a
                    href={cv}
                    target="_blank"
                    rel="noopener noreferrer"
                    onClick={handleResumeLinkClick}
                  >
                    Resume Link <ExternalLink />
                  </a>
                </Button>
                {/* Close Button */}
                <button
                  onClick={() => {
                    setSelectedResume(null);
                    setIsResumeModalOpen(false);
                  }}
                  className="bg-red-600 hover:bg-red-700 text-white font-semibold px-6 py-2 rounded-lg transition-all"
                >
                  Close
                </button>
              </div>
            </div>
            {selectedResume?.candidate &&
            Object.keys(selectedResume.candidate)?.length > 0 ? (
              <>
                {/* Personal Details */}
                <div className="mb-6">
                  <p className="text-lg font-semibold text-gray-700">
                    <span className="text-gray-900">👤 Name:</span>{" "}
                    {selectedResume?.candidate?.name}
                  </p>
                  <p className="text-lg font-semibold text-gray-700">
                    <span className="text-gray-900">✉️ Email:</span>{" "}
                    {selectedResume?.candidate?.email}
                  </p>
                  <p className="text-lg font-semibold text-gray-700">
                    <span className="text-gray-900">📞 Phone:</span>{" "}
                    {selectedResume?.candidate?.phone}
                  </p>
                  {selectedResume?.candidate?.city &&
                    selectedResume?.candidate?.state && (
                      <p className="text-lg font-semibold text-gray-700">
                        <span className="text-gray-900">📍 location:</span>{" "}
                        {`${selectedResume?.candidate?.city}, ${selectedResume?.candidate?.state}`}
                      </p>
                    )}
                </div>

                {/* Skills & Certifications */}
                <div className="mb-6">
                  <p className="flex justify-content items-start mb-5">
                    <strong className="w-[250px]"> Skills</strong>
                    <span className="flex-1 flex flex-wrap gap-2 capitalize">
                      {legendSkillWeights.map((weight, index) => (
                        <SkillBadge
                          key={index}
                          skill={weight}
                          styleClass={getHighlightStyle(weight)}
                        />
                      ))}
                    </span>
                  </p>
                  {/* Soft Skills */}
                  {showSoftSkills && (
                    <p className="flex items-start mb-5">
                      <strong className="w-[250px]">🗣 Soft Skills:</strong>
                      <span className="flex-1 flex flex-wrap gap-2 capitalize">
                        {selectedResume?.candidate?.["soft skills"]?.length > 0
                          ? selectedResume?.candidate?.["soft skills"].map(
                              (skill, index) => (
                                <SkillBadge
                                  key={index}
                                  skill={skill}
                                  styleClass={getSkillBadgeHighlightClassName(
                                    skill
                                  )}
                                />
                              )
                            )
                          : "N/A"}
                      </span>
                    </p>
                  )}

                  {/* Technical Skills */}
                  <p className="flex items-start mb-5">
                    <strong className="w-[250px]">💻 Technical Skills:</strong>
                    <span className="flex-1 flex flex-wrap gap-2 capitalize">
                      {selectedResume?.candidate?.["technical skills"]?.length >
                      0
                        ? selectedResume?.candidate?.["technical skills"]?.map(
                            (skill, index) => (
                              <SkillBadge
                                key={index}
                                skill={skill}
                                styleClass={getSkillBadgeHighlightClassName(
                                  skill
                                )}
                              />
                            )
                          )
                        : "N/A"}
                    </span>
                  </p>

                  {/* Tools & Platforms */}
                  <p className="flex items-start mb-5">
                    <strong className="w-[250px]">🛠 Tools & Platforms:</strong>
                    <span className="flex-1 flex flex-wrap gap-2 capitalize">
                      {selectedResume?.candidate?.["tools and platforms"]
                        ?.length > 0
                        ? selectedResume?.candidate?.[
                            "tools and platforms"
                          ]?.map((skill, index) => (
                            <SkillBadge
                              key={index}
                              skill={skill}
                              styleClass={getSkillBadgeHighlightClassName(
                                skill
                              )}
                            />
                          ))
                        : "N/A"}
                    </span>
                  </p>
                </div>

                {!isParsedResume
                  ? getParsedResume()
                  : getFullWorkExperience(selectedResume)}
              </>
            ) : selectedResume?.candidate &&
              Object.keys(selectedResume.candidate)?.length === 0 ? (
              <div className="flex relative top-28 pt-10 justify-center items-center text-3xl">
                Resume details not found
              </div>
            ) : isResumeModalOpen && isLoading ? (
              <Loading height="h-[80vh]" />
            ) : null}
          </div>
        </div>
      )}
    </div>
  );
};

export default CandidateResume;
