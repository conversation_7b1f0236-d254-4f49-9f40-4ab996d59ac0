{"db": {"env": "DEV", "_comment": "db env variables are DEV, QA, UAT or PROD"}, "sharepoint": {"env": "PROD", "_comment": "sharepoint env variables are SANDBOX, UAT or PROD"}, "dataverse": {"env": "SANDBOX", "_comment": "dataverse env variables are SANDBOX, UAT or PROD"}, "logging": {"name": "recruiter_d<PERSON><PERSON>r", "log_level": "DEBUG", "log_to_stdout": true, "use_syslog": false, "app_insights_log": true, "log_file": "recruiter_dpserver.log", "_comment": "log_file will be used if both use_syslog and log_to_stdout are false"}, "app": {"host": "0.0.0.0", "port": 8005}, "secrets": {"ext_api_keys_file": "/etc/ext_api_keys.env.gpg", "dev_secrets_file": "/etc/dev_secrets.env.gpg", "uat_secrets_file": "/etc/uat_secrets.env.gpg", "prod_secrets_file": "/etc/prod_secrets.env.gpg"}}