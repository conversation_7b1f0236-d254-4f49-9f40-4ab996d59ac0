variables:
  WebLBIP: ************
  ServerLBIP: ************
  PortalLBIP: ************
  ReactAppEnv: ua
  WebMinReplicas: 1
  WebMaxReplicas: 1
  WebMemThreshold: 80
  WebCpuThreshold: 80
  ServerMinReplicas: 1
  ServerMaxReplicas: 1
  ServerMemThreshold: 80
  ServerCpuThreshold: 80
  PortalMinReplicas: 1
  PortalMaxReplicas: 1
  PortalMemThreshold: 80
  PortalCpuThreshold: 80
  NEXTAUTH_URL: https://recruiter.ua.tandymgroup.com
  NEXT_PUBLIC_AD_LOGIN: true
  NEXT_PUBLIC_PARSE_RESUME_FROM_MERCURY: true
  NEXT_PUBLIC_APPINSIGHTS_CONNECTION_STRING: 017ce8e3-75aa-4b79-821a-a254f47527df;IngestionEndpoint=https://eastus-3.in.applicationinsights.azure.com/;LiveEndpoint=https://eastus.livediagnostics.monitor.azure.com/;ApplicationId=7f1efc32-bc37-4cd1-bd8e-c4af6169342b
  NEXT_PUBLIC_AUTH_URL: recruiter.ua.tandymgroup.com
  CRM_URL: https://tandymgroup-uat.crm.dynamics.com
