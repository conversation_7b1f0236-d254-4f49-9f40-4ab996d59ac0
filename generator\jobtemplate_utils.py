from dataverse_helper.token_manager import get_token_for_env, get_dataverse_credentials_for_env, Environment
from dataverse_helper.dv_common import read_fields_from_dataverse, update_row_in_dataverse
from common.appLogger import getGlobalAppLogger
from generator.job_template import job_template, job_template_input
import re
import sys
import html
from generator.vancacy_openai_utils import extract_job_template_json, create_adverttext3, validate_vacancy_json
import json

def strip_html_tags(text: str) -> str:
    """
    Remove HTML tags and unescape HTML entities from the given text.
    """
    # Remove tags
    clean = re.sub(r'<[^>]+>', '', text)
    # Unescape HTML entities
    return html.unescape(clean)

def extract_between_markers(text: str, extract: bool = True) -> tuple[bool, str]:
    """
    Extracts the substring from just after an HTML-wrapped separator of hyphens
    (three or more) and up through and including the 'Recency of Must-Have Skills (Required):'
    marker and any content on that line.
    Returns (True, extracted_text) if both markers are found in correct order;
    otherwise returns (False, "").
    If extract=False, returns the text with that entire segment removed.
    """
    # Pattern for hyphens inside any <p> tag
    #start_pattern = re.compile(r'<p[^>]*?>-{3,}</p>', re.IGNORECASE)
    # Pattern for Recency marker plus any content until end of line

    start_pattern = re.compile(
        r'(?:<div[^>]*?>\s*<p[^>]*?>\s*-{3,}\s*</p>)|(?:<p[^>]*?>\s*-{3,}\s*</p>)',
        re.IGNORECASE | re.DOTALL
    )
    end_pattern = re.compile(
        r'Recency of Must-Have Skills\s*\(Required\):[^\r\n]*',
        re.IGNORECASE
    )
    
    start_match = start_pattern.search(text)
    end_match = end_pattern.search(text)
    
    # If either marker is missing or out of order, return False
    if not start_match or not end_match or start_match.end() >= end_match.end():
        return False, ""

    if extract:
        # Return only the extracted segment between markers
        extracted = text[start_match.end():end_match.end()]
        return True, extracted.strip()
    else:
        # Remove the entire matched segment including start and end markers
        remaining = text[:start_match.start()] + text[end_match.end():]
        return True, remaining.strip()



def clean_text(text: str) -> str:
    """
    Clean the CatalystMatch HTML/text by:
    1. Converting <br> tags to newlines.
    2. Replacing &nbsp; with spaces.
    3. Removing all other HTML tags.
    4. Unescaping HTML entities (&amp;, etc.).
    5. Removing lines of hyphens and leading hyphens.
    6. Trimming and collapsing whitespace.
    """
    # 1. Convert <br> tags to newlines
    text = re.sub(r'<br\s*/?>', '\n', text, flags=re.IGNORECASE)
    
    # 2. Replace non-breaking spaces with normal spaces
    text = text.replace('&nbsp;', ' ')
    
    # 3. Remove all remaining HTML tags
    text = re.sub(r'<[^>]+>', '', text)
    
    # 4. Unescape HTML entities
    text = html.unescape(text)
    
    # 5. Remove lines that consist solely of hyphens
    text = re.sub(r'(?m)^[\s-]*\n', '', text)
    # Remove any leading hyphens at the very start
    text = re.sub(r'^-+', '', text)
    
    # 6. Split into lines, strip each, and drop empty ones
    lines = [line.strip() for line in text.splitlines() if line.strip()]
    
    # Rejoin with single newline
    return '\n'.join(lines)

def populate_job_template(token, dataverse_url, advert_text2, vacancy_id, logger):

    #remove all html tags   
    if advert_text2 is not None:
        success, advert_text_temp = extract_between_markers(advert_text2, True)
        if success:
            advert_text_not_p = clean_text(advert_text_temp)
        else:
           logger.info("Job template is not found")
           advert_text_not_p = ""
    else:
        advert_text_not_p = ""
    #check if advert_text_not_p is has To use Catalyst fill out the following (don't delete this line)  
    if "For CatalystMatch fill out the following" not in advert_text_not_p:
        logger.info("Job template is not populated")
        if advert_text2 is not None:
            advert_text2= advert_text2 + job_template_input
        else:
            advert_text2 = job_template_input
        update_row_in_dataverse(token, dataverse_url, "crimson_vacancy", vacancy_id, {'recruit_adverttext2': advert_text2})
        return "job template added", ""
    else:
        logger.info("Job template is populated")
        return "job template exists", advert_text_not_p
    
def extract_validate_job_template(advert_text2, client_name, category, logger):
    success, advert_text_temp = extract_between_markers(advert_text2, True)
    logger.info(f"success is {success}")
    if success:
        advert_text_not_p = clean_text(advert_text_temp)
        advert_text_not_p = advert_text_not_p + "\nClient name (Required): " + client_name + "\n"
        logger.info(advert_text_not_p)
        json_text = extract_job_template_json(advert_text_not_p, logger)

        error_code = validate_job_template(json_text, category, logger)
        return error_code, json_text
    else:
        return -1, ""
    

def validate_job_template(json_text, category, logger):
    #Need to validate city and state based on what chatGPt give us.
    #New grad - use new grad in technical musthave check for it.
    #test for remote in job location
    if json_text['job location'] == [] or json_text['job location'] == "" or json_text["years of experience"] == "":
        return 1
    if category == "Architecture Development & QA" or category == "Enterprise Applications (CC)" or category == "Infrastructure (MC)" or category == "Program & Project Management (CC)" or category == "Nursing & Therapy (CC)":
            if json_text["technical skills"] is []:
                return 2
    if category == "Advanced Practice & Physicians (CC)":
            if json_text["degrees and certifications"] is []:
                return 3
    if json_text['confidential'] == "" or json_text['industry'] == "" or json_text['recency of must have skills'] == "":
        logger.error("Confidential, industry or recency of must have data is missing - Chatgpt Error")
        return 4
    if json_text['job location'][0]['state'] == "Unknown":
        
        return 5
    return 0
        

def populate_job_template_for_all_vacancies(enum_env, vacancy_ref_no, env_and_more, logger):
    if env_and_more == "sandbox":
        env_num = Environment.SANDBOX
    else:
        env_num = Environment.PROD 
    token = get_token_for_env(env_num, logger=logger)
    credentials = get_dataverse_credentials_for_env(env_num, logger=logger)
    dataverse_url = credentials["RESOURCE_URL"]

    #whereClause = f"statecode eq 0 and recruit_mandatorytagcontrol2 ne null and recruit_numericuserfield5 eq null"
    if vacancy_ref_no != "":
        whereClause = f"statecode eq 0 and crimson_vacancyrefno eq '{vacancy_ref_no}'"
    else:
        whereClause = f"statecode eq 0"
    fields = ['crimson_vacancyrefno', 'recruit_adverttext2', 'crimson_vacancyid']
    rep = read_fields_from_dataverse(token, dataverse_url, "crimson_vacancy", fields, whereClause, logger=logger, expand=None, additional_headers=None)
    rows = rep['value']
    total_count = len(rows)

    # Print the total count
    logger.info(f" number of open vacancies - {total_count}")
    for row in rows:
        logger.info(row['crimson_vacancyrefno'])
        populate_job_template(token, dataverse_url, row['recruit_adverttext2'], row['crimson_vacancyid'], logger)
        
def test_extract_job_template_adverttext2(vacancy_ref_no, env, logger):
    if env == "sandbox":
        envnum = Environment.SANDBOX
    else:
        envnum = Environment.PROD
    token = get_token_for_env(envnum, logger=logger)
    credentials = get_dataverse_credentials_for_env(envnum, logger=logger)
    dataverse_url = credentials["RESOURCE_URL"]
    fields = ['recruit_adverttext2', 'recruit_mandatorytagcontrol0', 'crimson_jobtitle']
    whereClause = f"crimson_vacancyrefno eq '{vacancy_ref_no}'"
    expand_clause ="crimson_clientid($select=name)"
 
    rep = read_fields_from_dataverse(token, dataverse_url, "crimson_vacancy", fields, whereClause, logger=logger, expand=expand_clause, additional_headers=None)
    advert_text2 = rep['value'][0]['recruit_adverttext2']

    client_name = rep['value'][0]['crimson_clientid']['name']
    error_code, json_text = extract_validate_job_template(advert_text2, client_name, rep['value'][0]['recruit_mandatorytagcontrol0'], logger)
    print(json_text)
    if error_code == 0:
        logger.info("Job template is valid")
    elif error_code == 1:
        logger.error(f"for {vacancy_ref_no} and GTM {rep['value'][0]['recruit_mandatorytagcontrol0']} Job template is invalid - missing location or years of experience")
    elif error_code == 2:
        logger.error(f"for {vacancy_ref_no} and GTM {rep['value'][0]['recruit_mandatorytagcontrol0']} Job template is invalid - missing technical skills")
    elif error_code == 3:
        logger.error(f"for {vacancy_ref_no} and GTM {rep['value'][0]['recruit_mandatorytagcontrol0']} Job template is invalid - missing certifications and degrees")
    elif error_code == 4:
        logger.error(f"for {vacancy_ref_no} and GTM {rep['value'][0]['recruit_mandatorytagcontrol0']} Job template is invalid - missing confidential, industry or recency of must have skills")
    elif error_code == 5:
        logger.error(f"for {vacancy_ref_no} and GTM {rep['value'][0]['recruit_mandatorytagcontrol0']} Job template is invalid - state is unknown")
    if error_code > 0:
        return
    """ 
    #Validate the job template using o3 model.
    ret, advert_text2_minus_template = extract_between_markers(advert_text2, False)
    clean_text = strip_html_tags(advert_text2_minus_template)
    json_text['job_title'] = rep['value'][0]['crimson_jobtitle']
    print(rep['value'][0]['crimson_jobtitle'])
    response = validate_vacancy_json(json_text, clean_text, logger)
    print(response)
    """


def update_sandbox_adverttext3(vacancy_ref_no, text3, logger):
    token = get_token_for_env(Environment.SANDBOX, logger=logger)
    credentials = get_dataverse_credentials_for_env(Environment.SANDBOX, logger=logger)
    dataverse_url = credentials["RESOURCE_URL"]
    fields = ['recruit_adverttext3', 'crimson_vacancyid']
    whereClause = f"crimson_vacancyrefno eq '{vacancy_ref_no}'"
    rep = read_fields_from_dataverse(token, dataverse_url, "crimson_vacancy", fields, whereClause, logger=logger, expand=None, additional_headers=None)
    vacancy_id = rep['value'][0]['crimson_vacancyid']
    update_row_in_dataverse(token, dataverse_url, "crimson_vacancy", vacancy_id, {'recruit_adverttext3': text3})
    
def create_external_posting(token, dataverse_url, vacancy_id, advert_text2, logger):
    job_text = create_adverttext3(advert_text2, logger)
    match = re.search(r'```html\s*(.*?)\s*```', job_text, re.DOTALL | re.IGNORECASE)
    if not match:
        print("No HTML block found.")
        return False, ""
    html_content = match.group(1)
    response = update_row_in_dataverse(token, dataverse_url, "crimson_vacancy", vacancy_id, {'recruit_adverttext3': html_content})
    if response.status_code != 204:
        logger.error(f"Failed to create external posting - adverttext3: {response.status_code}, {response.text}")
    return True, html_content

def test_create_adverttext3(vacancy_ref_no, sandbox_vacancy_ref_no, logger):
    token = get_token_for_env(Environment.PROD, logger=logger)
    credentials = get_dataverse_credentials_for_env(Environment.PROD, logger=logger)
    dataverse_url = credentials["RESOURCE_URL"]
    fields = ['recruit_adverttext2']
    whereClause = f"crimson_vacancyrefno eq '{vacancy_ref_no}'"
    rep = read_fields_from_dataverse(token, dataverse_url, "crimson_vacancy", fields, whereClause, logger=logger, expand=None, additional_headers=None)
    advert_text2 = rep['value'][0]['recruit_adverttext2']

    """
    #Will enable this one job template appended. This will not be called for now.
    success, advert_text_temp = extract_between_markers(advert_text2, False)
    logger.info(advert_text_temp)
    if success:
        advert_text_not_p = clean_text(advert_text_temp)
    #logger.info(advert_text_not_p)
    """
    job_text = create_adverttext3(advert_text2, logger)
    match = re.search(r'```html\s*(.*?)\s*```', job_text, re.DOTALL | re.IGNORECASE)
    if not match:
        print("No HTML block found.")
        return
    html_content = match.group(1)
    print(html_content)
    update_sandbox_adverttext3(sandbox_vacancy_ref_no, html_content, logger)


if __name__ == "__main__":
    if len(sys.argv) > 2:
        mode = sys.argv[1]
        vacancy_req_no = sys.argv[2]
        env_and_more  = sys.argv[3]


    logger = getGlobalAppLogger()
    if mode == "all":
        populate_job_template_for_all_vacancies(env_and_more, vacancy_req_no, env_and_more, logger)
    if mode == "extract":
        test_extract_job_template_adverttext2(vacancy_req_no, env_and_more, logger)
    if mode == "create":
        test_create_adverttext3(vacancy_req_no, env_and_more, logger)
       