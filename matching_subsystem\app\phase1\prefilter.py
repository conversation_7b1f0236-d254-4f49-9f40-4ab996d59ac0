from typing import Dict, List, Optional, Any
from ..utils.appLogger2 import AppLogger2  # Adjusted import
from ..db.operations import fetch_prefiltered_candidates_data, get_candidate_ids_by_semantic_filter  # Adjusted import
import re
import math  # For log calculation if any bonus applied here, though likely in phase2

# A base map of US state/territory names (lowercase) to their USPS codes.
BASE_US_STATES_MAP = {
    "alabama": "AL", "alaska": "AK", "arizona": "AZ", "arkansas": "AR", "california": "CA",
    "colorado": "CO", "connecticut": "CT", "delaware": "DE", "district of columbia": "DC",
    "florida": "FL", "georgia": "GA", "hawaii": "HI", "idaho": "ID", "illinois": "IL",
    "indiana": "IN", "iowa": "IA", "kansas": "KS", "kentucky": "KY", "louisiana": "LA",
    "maine": "ME", "maryland": "MD", "massachusetts": "MA", "michigan": "MI",
    "minnesota": "MN", "mississippi": "MS", "missouri": "MO", "montana": "MT",
    "nebraska": "NE", "nevada": "NV", "new hampshire": "NH", "new jersey": "NJ",
    "new mexico": "NM", "new york": "NY", "north carolina": "NC", "north dakota": "ND",
    "ohio": "OH", "oklahoma": "OK", "oregon": "OR", "pennsylvania": "PA",
    "rhode island": "RI", "south carolina": "SC", "south dakota": "SD", "tennessee": "TN",
    "texas": "TX", "utah": "UT", "vermont": "VT", "virginia": "VA", "washington": "WA",
    "west virginia": "WV", "wisconsin": "WI", "wyoming": "WY",
    "american samoa": "AS", "guam": "GU", "northern mariana islands": "MP",
    "puerto rico": "PR", "virgin islands": "VI"
}

# Automatically generate a comprehensive map that includes space-removed variations.
US_STATES_MAP = {}
for name, code in BASE_US_STATES_MAP.items():
    US_STATES_MAP[name] = code
    if ' ' in name:
        US_STATES_MAP[name.replace(' ', '')] = code

# Create reverse mapping (code -> name) and a set of all valid codes for quick validation.
US_STATE_CODES_MAP = {v: k for k, v in BASE_US_STATES_MAP.items()}
US_STATE_CODES_SET = set(US_STATES_MAP.values())

def _parse_experience_requirement(vacancy_data: Dict[str, Any], logger: AppLogger2) -> float:
    """Parses vacancy experience requirements to find max required months."""
    max_required_months = 0.0
    job_template = vacancy_data.get("job_template", {})
    vacancy_experience_reqs = job_template.get("years of experience", [])
    if not isinstance(vacancy_experience_reqs, list):
        logger.warning("'years of experience' in vacancy is not a list. Skipping experience pre-filter.")
        return 0.0

    for exp_req in vacancy_experience_reqs:
        if not isinstance(exp_req, dict): continue
        years_str = exp_req.get('years')
        if not years_str: continue
        try:
            # Simplified parsing logic from original, can be enhanced
            years_str = str(years_str).strip()
            numeric_part_match = re.match(r"([<>]?)(\d+(\.\d+)?)(\+?)", years_str)
            req_years = 0
            if numeric_part_match:
                operator = numeric_part_match.group(1)
                numeric_value = float(numeric_part_match.group(2))
                if operator == '<': continue # Skip less than for pre-filtering min experience
                req_years = numeric_value
            else: # Try direct float conversion
                req_years = float(years_str)
            
            req_months = req_years * 12
            if req_months > max_required_months:
                max_required_months = req_months
        except (ValueError, TypeError):
            logger.warning(f"Could not parse years for experience requirement: {exp_req}. Skipping.")
    return max_required_months


def execute_prefiltering(
    vacancy_data: Dict[str, Any],
    vacancy_embeddings_map: Dict[str, Any], # Contains pre-generated vacancy embeddings
    effective_config: Dict[str, Any],
    conn_info, # Candidate info DB connection
    conn_embed, # Embedding DB connection
    logger: AppLogger2
) -> Dict[str, Dict[str, Any]]:
    """
    Executes the pre-filtering phase based on configuration.
    Returns a dictionary of candidate_id to their data (total_months_experience, work_experience).
    """
    prefilter_config = effective_config.get("phase1_prefilter", {})
    priority_order_config = prefilter_config.get("priority_order", [])
    
    # Ensure only one of subcategory_match or category_match is active.
    # If an enabled subcategory_match filter exists, it takes precedence,
    # and any enabled category_match filter will be disabled.
    is_subcategory_match_enabled = False
    for filter_conf in priority_order_config:
        if filter_conf.get("filter_type") == "subcategory_match" and filter_conf.get("enabled", True):
            is_subcategory_match_enabled = True
            break
    
    if is_subcategory_match_enabled:
        for filter_conf in priority_order_config:
            if filter_conf.get("filter_type") == "category_match" and filter_conf.get("enabled", True):
                logger.warning(
                    "Both subcategory_match and category_match filters were found enabled. "
                    "Prioritizing subcategory_match. Disabling category_match filter."
                )
                filter_conf["enabled"] = False
    
    # Global pool thresholds (can be overridden by subcategory)
    master_pool_thresholds = effective_config.get("candidate_pool_thresholds", {})
    # Subcategory can override this in its phase1_prefilter.candidate_pool_threshold_config
    pool_threshold_config = prefilter_config.get("candidate_pool_threshold_config", master_pool_thresholds)
    
    target_pool_size = pool_threshold_config.get("prefilter_target_size", 5000)
    upper_bound_factor = pool_threshold_config.get("prefilter_target_size_upper_bound_factor", 0.10)
    absolute_upper_bound = target_pool_size * (1 + upper_bound_factor)

    logger.info(f"Starting pre-filtering. Target pool size: {target_pool_size}, Absolute upper bound: {absolute_upper_bound:.0f}")

    # Store the state of filters applied at each priority level
    # This dict will be passed to db_operations.fetch_prefiltered_candidates_data
    current_db_filters: Dict[str, Any] = {}
    
    # Initial candidate pool is all candidates (implicitly, until first filter is applied)
    # Or, if a very first filter is always mandatory (like subcategory), the logic changes slightly.
    # For now, assume filters progressively narrow down. If a filter set yields 0, we stop.

    # Group filters by priority
    grouped_filters = {}
    for filter_conf in priority_order_config:
        priority = filter_conf.get("priority")
        if priority not in grouped_filters:
            grouped_filters[priority] = []
        grouped_filters[priority].append(filter_conf)

    candidate_pool_data: Dict[str, Dict[str, Any]] = {} # Holds data for final pool
    
    # Sort priorities and iterate
    for priority in sorted(grouped_filters.keys()):
        logger.info(f"Applying pre-filters for priority {priority}...")
        priority_filters_applied_this_step = False
        
        priority_db_filter_updates: Dict[str, Any] = {}

        for filter_conf in grouped_filters[priority]:
            filter_type = filter_conf.get("filter_type")
            filter_params = filter_conf.get("params", {})
            is_enabled = filter_conf.get("enabled", True) 

            if not is_enabled:
                logger.info(f"Filter '{filter_type}' at priority {priority} is disabled. Skipping.")
                continue

            priority_filters_applied_this_step = True
            logger.info(f"Processing filter: {filter_type} with params: {filter_params}")

            if filter_type == "subcategory_match":
                subcat_name_from_vacancy = vacancy_data.get('subcategory')
                subcategory_override_names = filter_params.get("subcategory_override_names")

                if subcategory_override_names:
                    if not isinstance(subcategory_override_names, list) or not all(isinstance(name, str) for name in subcategory_override_names):
                        logger.warning(f"Invalid 'subcategory_override_names' (must be list of strings) for subcategory_match filter. Skipping.")
                        continue
                    priority_db_filter_updates["subcategory_override_names"] = subcategory_override_names
                    logger.info(f"Using subcategory override names: {subcategory_override_names}")
                elif subcat_name_from_vacancy:
                    priority_db_filter_updates["subcategory_name"] = subcat_name_from_vacancy
                else:
                    logger.warning("Subcategory name not found in vacancy data and no 'subcategory_override_names' provided. Cannot apply subcategory_match filter.")
                    continue
                
                initial_match_type = filter_params.get("initial_match_type", "all") 
                priority_db_filter_updates["subcategory_match_type"] = initial_match_type
                
                temp_filters_for_check = {**current_db_filters, **priority_db_filter_updates}
                temp_pool = fetch_prefiltered_candidates_data(temp_filters_for_check, effective_config, conn_info, logger)
                
                pool_check_name_for_log = subcategory_override_names if subcategory_override_names else subcat_name_from_vacancy

                if len(temp_pool) > absolute_upper_bound:
                    logger.info(f"Pool size {len(temp_pool)} with '{initial_match_type}' subcategory match for '{pool_check_name_for_log}' exceeds {absolute_upper_bound:.0f}.")
                    fallback_match_type = filter_params.get("fallback_match_type_if_above_threshold")
                    if fallback_match_type:
                        logger.info(f"Applying fallback subcategory match type: '{fallback_match_type}'.")
                        priority_db_filter_updates["subcategory_match_type"] = fallback_match_type
                    else:
                        logger.info("No fallback subcategory match type defined. Proceeding with current filter result.")
                elif len(temp_pool) == 0:
                    logger.warning(f"Subcategory filter '{initial_match_type}' for '{pool_check_name_for_log}' (priority {priority}) yielded 0 candidates. Stopping pre-filtering.")
                    return {}

            elif filter_type == "category_match":
                category_override_names = filter_params.get("category_override_names")
                category_name_from_vacancy = vacancy_data.get('category') # Fetched by operations.py

                target_category_names: Optional[List[str]] = None

                if category_override_names:
                    if isinstance(category_override_names, list) and all(isinstance(name, str) for name in category_override_names):
                        target_category_names = category_override_names
                        logger.info(f"Using category override names for category_match: {target_category_names}")
                    else:
                        logger.warning(f"Invalid 'category_override_names' (must be list of strings) for category_match filter. Skipping.")
                        continue
                elif category_name_from_vacancy:
                    if isinstance(category_name_from_vacancy, str) and category_name_from_vacancy.strip():
                        target_category_names = [category_name_from_vacancy.strip()]
                        logger.info(f"Using category name from vacancy for category_match: {target_category_names}")
                    else:
                        logger.warning(f"Invalid or empty 'category_name' from vacancy data: '{category_name_from_vacancy}'. Skipping category_match filter.")
                        continue
                else:
                    logger.warning("Filter 'category_match' (priority {priority}) has no 'category_override_names' and no 'category_name' derived from vacancy. Skipping.")
                    continue
                
                priority_db_filter_updates["category_names"] = target_category_names
                
                # Optional: Add pool size check here if needed, similar to subcategory_match
                temp_filters_for_check = {**current_db_filters, **priority_db_filter_updates}
                temp_pool = fetch_prefiltered_candidates_data(temp_filters_for_check, effective_config, conn_info, logger)
                if len(temp_pool) == 0:
                    logger.warning(f"Category filter for '{target_category_names}' (priority {priority}) yielded 0 candidates. Stopping pre-filtering.")
                    return {}

            elif filter_type == "job_title_semantic":
                vacancy_title_embedding = vacancy_embeddings_map.get("job_titles")
                if not vacancy_title_embedding:
                    logger.warning("Vacancy title embedding not available for job_title_semantic pre-filter. Skipping this filter.")
                    continue
                
                semantic_threshold = filter_params.get("semantic_threshold", 0.80)
                apply_recency_filter_for_title = filter_params.get("apply_recency_filter_for_title", False)
                recency_indices_for_title_match = filter_params.get("recency_indices_for_title_match", [0, 1]) # Default to first two experiences

                if apply_recency_filter_for_title and (not isinstance(recency_indices_for_title_match, list) or not all(isinstance(i, int) for i in recency_indices_for_title_match)):
                    logger.warning("Invalid 'recency_indices_for_title_match' (must be list of integers) for job_title_semantic filter. Disabling recency component.")
                    apply_recency_filter_for_title = False # Corrected to disable if invalid

                # Renamed for clarity, this can be List[Dict] or List[str]
                matched_candidates_data = get_candidate_ids_by_semantic_filter(
                    vacancy_title_embedding,
                    semantic_threshold,
                    "job_titles", # Attribute type
                    effective_config,
                    conn_embed,
                    logger,
                    return_with_index=True # Requesting index if available
                )

                if matched_candidates_data is None: # Error occurred in DB call
                    logger.warning("Error in semantic job title filter. Skipping its application for this priority.")
                    continue
                
                if not matched_candidates_data: # No candidates passed semantic match initially
                    logger.warning(f"Job title semantic filter (threshold {semantic_threshold}, priority {priority}) yielded 0 candidates initially. Stopping pre-filtering.")
                    return {}

                # Check if the returned list contains dictionaries (i.e., experience_index is available)
                items_are_dicts = bool(matched_candidates_data and isinstance(matched_candidates_data[0], dict))

                job_title_candidate_ids = []
                if apply_recency_filter_for_title:
                    if items_are_dicts:
                        logger.info(f"Applying recency filter for job title matches (experience_index available). Allowed indices: {recency_indices_for_title_match}")
                        for match_info_dict in matched_candidates_data: # Iterate over list of dicts
                            candidate_id = match_info_dict.get("candidate_id")
                            exp_index = match_info_dict.get("experience_index") 
                            
                            if candidate_id is None or exp_index is None:
                                logger.warning(f"Skipping match due to missing data in dict: {match_info_dict}")
                                continue

                            if exp_index in recency_indices_for_title_match:
                                job_title_candidate_ids.append(candidate_id)
                            else:
                                logger.debug(f"Candidate {candidate_id} excluded by job title recency filter (index {exp_index} not in {recency_indices_for_title_match}).")
                        
                        if not job_title_candidate_ids:
                            logger.warning(f"Job title semantic filter with recency constraint (indices {recency_indices_for_title_match}, priority {priority}) yielded 0 candidates. Stopping pre-filtering.")
                            return {}
                        logger.info(f"After recency filter, {len(job_title_candidate_ids)} candidates remain for job title match.")
                    else:
                        # items_are_dicts is False, meaning matched_candidates_data is List[str]
                        # Cannot apply recency filter based on experience_index.
                        logger.warning(
                            f"Recency filter for job title matches was requested, but 'experience_index' is not available for 'job_titles' attribute. "
                            f"Using all {len(matched_candidates_data)} candidates from semantic match without index-based recency filtering."
                        )
                        job_title_candidate_ids = [cid for cid in matched_candidates_data if isinstance(cid, str)]
                else: # Not applying recency filter (apply_recency_filter_for_title is False)
                    if items_are_dicts:
                        # matched_candidates_data is List[Dict]
                        job_title_candidate_ids = [
                            match_dict.get("candidate_id") 
                            for match_dict in matched_candidates_data 
                            if isinstance(match_dict, dict) and match_dict.get("candidate_id")
                        ]
                    else:
                        # matched_candidates_data is List[str]
                        job_title_candidate_ids = [cid for cid in matched_candidates_data if isinstance(cid, str)]


                if not job_title_candidate_ids: 
                    logger.warning(f"Job title semantic filter (threshold {semantic_threshold}, priority {priority}) yielded 0 candidates after processing. Stopping pre-filtering.")
                    return {}
                
                priority_db_filter_updates["job_title_candidate_ids"] = list(set(job_title_candidate_ids)) # Ensure unique IDs

            elif filter_type == "industry_semantic":
                vacancy_industry_items = vacancy_embeddings_map.get("industries")
                
                # Check if vacancy_industry_items is a list and is not empty
                if not isinstance(vacancy_industry_items, list) or not vacancy_industry_items:
                    logger.warning(
                        "Vacancy 'industries' in embeddings_map is not a non-empty list. "
                        "Skipping industry_semantic pre-filter."
                    )
                    continue
                
                all_industry_candidate_ids = set()
                processed_at_least_one_industry = False

                for industry_info in vacancy_industry_items:
                    industry_embedding = industry_info.get("embedding")
                    industry_name = industry_info.get("name", "N/A")

                    if not industry_embedding:
                        logger.warning(
                            f"Missing embedding for industry '{industry_name}'. "
                            "Skipping this specific industry for semantic filter."
                        )
                        continue

                    semantic_threshold = filter_params.get("semantic_threshold", 0.75)
                    logger.info(
                        f"Applying industry semantic filter for '{industry_name}' "
                        f"with threshold {semantic_threshold}."
                    )
                    
                    current_industry_candidate_ids = get_candidate_ids_by_semantic_filter(
                        industry_embedding,
                        semantic_threshold,
                        "industries", # Attribute type for candidate industries
                        effective_config,
                        conn_embed,
                        logger
                    )
                    processed_at_least_one_industry = True

                    if current_industry_candidate_ids is None: # Error in DB call
                        logger.warning(
                            f"Error in semantic industry filter for '{industry_name}'. "
                            "Skipping its application."
                        )
                        continue 
                    
                    all_industry_candidate_ids.update(current_industry_candidate_ids)

                if not processed_at_least_one_industry:
                    logger.warning(
                        "No industry embeddings were successfully processed for the "
                        "industry_semantic filter. Skipping filter application."
                    )
                    continue

                if not all_industry_candidate_ids:
                    logger.warning(
                        f"Industry semantic filter (priority {priority}) yielded 0 "
                        "candidates overall. Stopping pre-filtering."
                    )
                    return {} # Stop pre-filtering if no candidates match this crucial filter
                
                priority_db_filter_updates["industry_candidate_ids"] = list(all_industry_candidate_ids)

            elif filter_type == "total_job_experience":
                min_experience_months = _parse_experience_requirement(vacancy_data, logger)
                if min_experience_months > 0:
                    logger.info(f"Applying total job experience pre-filter: >= {min_experience_months:.0f} months.")
                    priority_db_filter_updates["min_experience_months"] = min_experience_months
                else:
                    logger.info("No valid 'greater than or equal to' experience requirement for pre-filtering, or filter disabled/not configured.")
            
            elif filter_type == "location_prefilter":
                job_template = vacancy_data.get("job_template", {})
                job_locations = job_template.get("job_location")
                if not isinstance(job_locations, list) or not job_locations:
                    logger.warning("Vacancy 'job_location' is not a valid list. Skipping location_prefilter.")
                    continue
                
                target_state_codes = set()
                for loc in job_locations:
                    if not isinstance(loc, dict): continue
                    state_val = loc.get("state")
                    if not state_val or not isinstance(state_val, str): continue
                    
                    # Normalize the state value from the vacancy to find its code
                    norm_state = state_val.strip().lower().replace(' ', '')
                    if norm_state.upper() in US_STATE_CODES_SET:
                        target_state_codes.add(norm_state.upper())
                    elif norm_state in US_STATES_MAP:
                        target_state_codes.add(US_STATES_MAP[norm_state])
                    else:
                        logger.warning(f"Could not map vacancy location '{state_val}' to a US state. Skipping.")
                
                if target_state_codes:
                    # Create a comprehensive list of all possible values for the DB to check.
                    # e.g., for NY: ['ny', 'new york', 'newyork']
                    match_values = set()
                    for code in target_state_codes:
                        match_values.add(code.lower()) # Add code: 'ny'
                        full_name = US_STATE_CODES_MAP.get(code)
                        if full_name:
                            match_values.add(full_name) # Add name with space: 'new york'
                            match_values.add(full_name.replace(' ', '')) # Add name without space: 'newyork'
                    
                    logger.info(f"Applying location filter for states matching: {list(match_values)}")
                    priority_db_filter_updates["location_match_values"] = list(match_values)
                else:
                    logger.warning("No valid US states found in vacancy's job_location for location_prefilter.")
        
        if priority_filters_applied_this_step:
            current_db_filters.update(priority_db_filter_updates)
            
            logger.info(f"Fetching candidate pool after applying priority {priority} filters. Current filter keys: {list(current_db_filters.keys())}")
            candidate_pool_data = fetch_prefiltered_candidates_data(current_db_filters, effective_config, conn_info, logger)

            if not candidate_pool_data:
                logger.warning(f"Pre-filtering after priority {priority} resulted in 0 candidates. Stopping.")
                return {}
            
            logger.info(f"Candidate pool size after priority {priority}: {len(candidate_pool_data)}")

        else: 
            logger.info(f"No active filters applied for priority {priority}.")

    logger.info(f"Pre-filtering complete. Final candidate pool size: {len(candidate_pool_data)}")
    return candidate_pool_data