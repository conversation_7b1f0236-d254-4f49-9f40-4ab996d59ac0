[{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(\\/?index|\\/?index\\\\.json))?[\\/#\\?]?$", "originalSource": "/"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/candidates(\\\\.json)?[\\/#\\?]?$", "originalSource": "/candidates"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/jobs(\\\\.json)?[\\/#\\?]?$", "originalSource": "/jobs"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/skills-editor(\\\\.json)?[\\/#\\?]?$", "originalSource": "/skills-editor"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/workforce-index(\\\\.json)?[\\/#\\?]?$", "originalSource": "/workforce-index"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/vacancy(\\\\.json)?[\\/#\\?]?$", "originalSource": "/vacancy"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/vacancy(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/vacancy/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/experiments(\\\\.json)?[\\/#\\?]?$", "originalSource": "/experiments"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/CandidateTuning\\/For_Mercury_Portal(\\\\.json)?[\\/#\\?]?$", "originalSource": "/CandidateTuning/For_Mercury_Portal"}]