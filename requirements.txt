annotated-types==0.7.0
anyio==4.9.0
azure-identity>=1.15.0
azure-appconfiguration>=1.5.0
azure-keyvault-secrets>=4.8.0
certifi==2025.1.31
cffi==1.17.1
charset-normalizer==3.4.1
click==8.1.8
cryptography==43.0.3
distro==1.9.0
docx2txt==0.9
easyocr==1.7.1
exceptiongroup==1.2.2
fastapi==0.110.1
filelock==3.18.0
fsspec==2025.3.2
fuzzywuzzy==0.18.0
greenlet==3.1.1
h11==0.14.0
httpcore==1.0.7
httpx==0.27.2
huggingface-hub==0.30.2
idna==3.10
imageio==2.37.0
iniconfig==2.1.0
Jinja2==3.1.6
joblib==1.4.2
lazy_loader==0.4
lxml==5.3.2
MarkupSafe==3.0.2
mpmath==1.3.0
msal==1.25.0
networkx==3.2.1
ninja==1.11.1.4
nltk==3.8.1
numpy==1.26.4
nvidia-cublas-cu12==12.1.3.1
nvidia-cuda-cupti-cu12==12.1.105
nvidia-cuda-nvrtc-cu12==12.1.105
nvidia-cuda-runtime-cu12==12.1.105
nvidia-cudnn-cu12==********
nvidia-cufft-cu12==*********
nvidia-curand-cu12==**********
nvidia-cusolver-cu12==**********
nvidia-cusparse-cu12==**********
nvidia-nccl-cu12==2.19.3
nvidia-nvjitlink-cu12==12.8.93
nvidia-nvtx-cu12==12.1.105
Office365-REST-Python-Client==2.6.1
openai==1.23.2
opencv-python-headless==*********
packaging==24.2
pandas==2.2.2
pdf2image==1.17.0
pillow==11.1.0
pluggy==1.5.0
psycopg2-binary==2.9.9
pyclipper==1.3.0.post6
pycparser==2.22
pydantic==2.11.3
pydantic_core==2.33.1
PyDocX==0.9.10
PyJWT==2.10.1
pyodbc==5.1.0
PyPDF2==3.0.1
pytest==8.1.1
python-bidi==0.6.6
python-dateutil==2.9.0.post0
python-docx==1.1.2
python-dotenv==1.1.0
pytz==2025.2
PyYAML==6.0.2
regex==2024.11.6
requests==2.31.0
safetensors==0.5.3
scikit-image==0.24.0
scikit-learn==1.6.1
scipy==1.13.1
sentence-transformers==2.7.0
shapely==2.0.7
six==1.17.0
sniffio==1.3.1
SQLAlchemy==2.0.29
starlette==0.37.2
sympy==1.13.3
threadpoolctl==3.6.0
tifffile==2024.8.30
tiktoken==0.6.0
tokenizers==0.15.2
tomli==2.2.1
torch==2.2.2
torchvision==0.17.2
tqdm==4.67.1
transformers==4.39.3
triton==2.2.0
typing-inspection==0.4.0
typing_extensions==4.13.2
tzdata==2025.2
urllib3==2.4.0
uvicorn==0.29.0
email_validator==2.2.0
opentelemetry-instrumentation-fastapi==0.52b1
opentelemetry-sdk==1.31.1
opentelemetry-semantic-conventions==0.52b1
opentelemetry-instrumentation-logging==0.52b1
azure-monitor-opentelemetry==1.6.10
