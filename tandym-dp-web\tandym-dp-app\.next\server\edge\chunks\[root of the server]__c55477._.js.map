{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/api/config.ts"], "sourcesContent": ["const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || \"http://0.0.0.0:8005\";\r\nconst PORTAL_SERVICE_BASE_URL =\r\n  process.env.DP_PORTAL_SERVICE || \"http://0.0.0.0:8006\";\r\n// Ensure this is the correct base URL for your experiment APIs,\r\n// the example uses localhost:8006, so adjust if necessary.\r\n// For this example, I will use the user-provided localhost:8006\r\nexport const EXPERIMENT_BASE_URL =\r\n  process.env.NEXT_PUBLIC_BASE_URL || \"http://localhost:8005\";\r\n\r\nexport const isADLogin = (): boolean =>\r\n  process.env.NEXT_PUBLIC_AD_LOGIN === \"true\";\r\n\r\nexport const isParsedResume =\r\n  process.env.NEXT_PUBLIC_PARSE_RESUME_FROM_MERCURY === \"true\";\r\n\r\nexport const IS_WHY_FIT_EDITABLE = process.env.NEXT_PUBLIC_IS_WHY_FIT_EDITABLE;\r\n\r\nexport const IS_LOCK_FEATURE_DISABLED =\r\n  process.env.NEXT_PUBLIC_IS_LOCK_FEATURE_DISABLED;\r\n\r\nexport const API_ENDPOINTS = {\r\n  categories: `${BASE_URL}/categories`,\r\n  subCategories: `${BASE_URL}/subcategories`,\r\n  subCategoriesPools: `${BASE_URL}/subcategory/pools`,\r\n  jobTitlesBySubCategory: `${BASE_URL}/jobtitles/:sub_category_id?limit=10000000`,\r\n  deleteAttribute: `${BASE_URL}/attribute/delete/:attribute_id`,\r\n  fetchAttributesBySubCategory: `${BASE_URL}/attributes/:sub_category_id?limit=10000000`,\r\n  fetchWeightsBySubCategory: `${BASE_URL}/weights/:sub_category_id`,\r\n  updateAttributeWeight: `${BASE_URL}/attributes/:sub_category_id/update`,\r\n  updateSubcategoryOfAttribute: `${BASE_URL}/attributes/:attribute_id/subcategory`,\r\n  updateAttributeApprovalStatus: `${BASE_URL}/attributes/:attribute_id/approval`,\r\n  candidatesData: `${BASE_URL}/candidates`,\r\n  updateCandidatesReviewData: `${BASE_URL}/candidates/update_in_db`,\r\n  jobsData: `${BASE_URL}/jobs`,\r\n  getVacancies: `${BASE_URL}/vacancies`,\r\n  getVacanciesFromFiles: `${BASE_URL}/files/vacancies`,\r\n  getCandidatesByVacancyId: `${BASE_URL}/candidates/:vacancy_id`,\r\n  getResumeByContactId: `${BASE_URL}/resume/:contact_id`,\r\n  getResumeFromFileByContactId: `${BASE_URL}/files/candidate-resume/:contact_id`,\r\n  getAllSubcategoryWeightConfigs: `${BASE_URL}/v1/subcategory/weight-configs`,\r\n  updateSubcategoryWeightConfig: `${BASE_URL}/v1/subcategory/weight-configs/:subcategory_id`,\r\n  getCandidateStats: `${BASE_URL}/api/candidate-stats`,\r\n  getEntitlements: `${PORTAL_SERVICE_BASE_URL}/api/entitlement`,\r\n  getVacancyConfigAndStatus: `${BASE_URL}/vacancies/:vacancy_id/status`,\r\n  startVacancyReview: `${BASE_URL}/vacancies/:vacancy_id/start`,\r\n  completeVacancyReview: `${BASE_URL}/vacancies/:vacancy_id/complete`,\r\n  updateWhyFitData: `${BASE_URL}/candidates/fitness_reason`,\r\n  regenerateCatalystMatch: `${BASE_URL}/vacancies/:vacancy_id/regenerate-catalyst-match`,\r\n\r\n  // New Experiment Endpoints\r\n  experimentGetVacancies: `${EXPERIMENT_BASE_URL}/experiment/vacancies`,\r\n  experimentGetVacancyRunDetails: `${EXPERIMENT_BASE_URL}/experiment/vacancies/:vacancy_id/runs/:run_id/details`,\r\n  experimentGetCandidatesForVacancyRun: `${EXPERIMENT_BASE_URL}/experiment/vacancies/:vacancy_id/runs/:run_id/candidates`,\r\n  experimentGetRunConfig: `${EXPERIMENT_BASE_URL}/experiment/runs/:run_id/config`,\r\n  experimentGetCandidateResume: `${EXPERIMENT_BASE_URL}/experiment/candidates/:contact_id/resume`,\r\n  experimentArchiveVacancy: `${EXPERIMENT_BASE_URL}/experiment/vacancies/archive`, // Added new endpoint\r\n  experimentPromoteResults: `${EXPERIMENT_BASE_URL}/experiment/results/promote`, // + New endpoint for promoting results\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;AAAA,MAAM,WAAW,2DAAoC;AACrD,MAAM,0BACJ,QAAQ,GAAG,CAAC,iBAAiB,IAAI;AAI5B,MAAM,sBACX,2DAAoC;AAE/B,MAAM,YAAY,IACvB,6CAAqC;AAEhC,MAAM,iBACX,6CAAsD;AAEjD,MAAM;AAEN,MAAM;AAGN,MAAM,gBAAgB;IAC3B,YAAY,GAAG,SAAS,WAAW,CAAC;IACpC,eAAe,GAAG,SAAS,cAAc,CAAC;IAC1C,oBAAoB,GAAG,SAAS,kBAAkB,CAAC;IACnD,wBAAwB,GAAG,SAAS,0CAA0C,CAAC;IAC/E,iBAAiB,GAAG,SAAS,+BAA+B,CAAC;IAC7D,8BAA8B,GAAG,SAAS,2CAA2C,CAAC;IACtF,2BAA2B,GAAG,SAAS,yBAAyB,CAAC;IACjE,uBAAuB,GAAG,SAAS,mCAAmC,CAAC;IACvE,8BAA8B,GAAG,SAAS,qCAAqC,CAAC;IAChF,+BAA+B,GAAG,SAAS,kCAAkC,CAAC;IAC9E,gBAAgB,GAAG,SAAS,WAAW,CAAC;IACxC,4BAA4B,GAAG,SAAS,wBAAwB,CAAC;IACjE,UAAU,GAAG,SAAS,KAAK,CAAC;IAC5B,cAAc,GAAG,SAAS,UAAU,CAAC;IACrC,uBAAuB,GAAG,SAAS,gBAAgB,CAAC;IACpD,0BAA0B,GAAG,SAAS,uBAAuB,CAAC;IAC9D,sBAAsB,GAAG,SAAS,mBAAmB,CAAC;IACtD,8BAA8B,GAAG,SAAS,mCAAmC,CAAC;IAC9E,gCAAgC,GAAG,SAAS,8BAA8B,CAAC;IAC3E,+BAA+B,GAAG,SAAS,8CAA8C,CAAC;IAC1F,mBAAmB,GAAG,SAAS,oBAAoB,CAAC;IACpD,iBAAiB,GAAG,wBAAwB,gBAAgB,CAAC;IAC7D,2BAA2B,GAAG,SAAS,6BAA6B,CAAC;IACrE,oBAAoB,GAAG,SAAS,4BAA4B,CAAC;IAC7D,uBAAuB,GAAG,SAAS,+BAA+B,CAAC;IACnE,kBAAkB,GAAG,SAAS,0BAA0B,CAAC;IACzD,yBAAyB,GAAG,SAAS,gDAAgD,CAAC;IAEtF,2BAA2B;IAC3B,wBAAwB,GAAG,oBAAoB,qBAAqB,CAAC;IACrE,gCAAgC,GAAG,oBAAoB,sDAAsD,CAAC;IAC9G,sCAAsC,GAAG,oBAAoB,yDAAyD,CAAC;IACvH,wBAAwB,GAAG,oBAAoB,+BAA+B,CAAC;IAC/E,8BAA8B,GAAG,oBAAoB,yCAAyC,CAAC;IAC/F,0BAA0B,GAAG,oBAAoB,6BAA6B,CAAC;IAC/E,0BAA0B,GAAG,oBAAoB,2BAA2B,CAAC;AAC/E"}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 81, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/middleware.ts"], "sourcesContent": ["import { NextResponse } from \"next/server\";\r\nimport { getToken } from \"next-auth/jwt\";\r\nimport type { NextRequest } from \"next/server\";\r\nimport { isADLogin } from \"@/api/config\";\r\n\r\nexport async function middleware(req: NextRequest) {\r\n  const url = req.nextUrl;\r\n  const pathname = url.pathname;\r\n  const isADlogin = isADLogin();\r\n\r\n  const isCandidateTuning = pathname.startsWith(\r\n    \"/CandidateTuning/For_Mercury_Portal\"\r\n  );\r\n  const referer = req.headers.get(\"referer\") || \"\";\r\n  const isFromCRM = referer.startsWith(process.env.CRM_URL || \"\");\r\n  // Skip AD auth if accessed from iframe or CRM referer\r\n if (isCandidateTuning && (isFromCRM)) {\r\n  return NextResponse.next();\r\n}\r\n\r\n  // Token fetched once\r\n  const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });\r\n\r\n  const protectedRoutes = [\r\n    \"/\",\r\n    \"/candidates\",\r\n    \"/jobs\",\r\n    \"/skills-editor\",\r\n    \"/workforce-index\",\r\n    \"/vacancy\",\r\n    \"/experiments\",\r\n    \"/subcategory-config\",\r\n    // \"/subcategory-config\",\r\n    \"/CandidateTuning/For_Mercury_Portal\",\r\n  ];\r\n\r\n  const isProtected = protectedRoutes.some((path) => pathname.startsWith(path));\r\n\r\n  if (isADlogin && isProtected) {\r\n    // if (isCandidateTuning) {\r\n    //   const noAccessUrl = new URL(\"/no-access\", req.url);\r\n    //   return NextResponse.redirect(noAccessUrl);\r\n    // }\r\n    if (!token) {\r\n      // Avoid recursive callback loops by using the actual page URL\r\n      const signInUrl = new URL(\"/api/auth/signin/azure-ad\", req.url);\r\n      signInUrl.searchParams.set(\"callbackUrl\", req.url);\r\n\r\n      const response = NextResponse.redirect(signInUrl);\r\n\r\n      return response;\r\n    }\r\n  }\r\n\r\n  // Default fallback\r\n  return NextResponse.next();\r\n}\r\n\r\nexport const config = {\r\n  matcher: [\r\n    \"/\",\r\n    \"/candidates\",\r\n    \"/jobs\",\r\n    \"/skills-editor\",\r\n    \"/workforce-index\",\r\n    \"/vacancy\",\r\n    \"/vacancy/:path*\",\r\n    \"/experiments\",\r\n    // \"/subcategory-config\",\r\n    \"/CandidateTuning/For_Mercury_Portal\",\r\n  ], // Add other protected routes\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAEA;AAHA;;;;AAKO,eAAe,WAAW,GAAgB;IAC/C,MAAM,MAAM,IAAI,OAAO;IACvB,MAAM,WAAW,IAAI,QAAQ;IAC7B,MAAM,YAAY,CAAA,GAAA,6GAAA,CAAA,YAAS,AAAD;IAE1B,MAAM,oBAAoB,SAAS,UAAU,CAC3C;IAEF,MAAM,UAAU,IAAI,OAAO,CAAC,GAAG,CAAC,cAAc;IAC9C,MAAM,YAAY,QAAQ,UAAU,CAAC,QAAQ,GAAG,CAAC,OAAO,IAAI;IAC5D,sDAAsD;IACvD,IAAI,qBAAsB,WAAY;QACrC,OAAO,qLAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEE,qBAAqB;IACrB,MAAM,QAAQ,MAAM,CAAA,GAAA,4IAAA,CAAA,WAAQ,AAAD,EAAE;QAAE;QAAK,QAAQ,QAAQ,GAAG,CAAC,eAAe;IAAC;IAExE,MAAM,kBAAkB;QACtB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,yBAAyB;QACzB;KACD;IAED,MAAM,cAAc,gBAAgB,IAAI,CAAC,CAAC,OAAS,SAAS,UAAU,CAAC;IAEvE,IAAI,aAAa,aAAa;QAC5B,2BAA2B;QAC3B,wDAAwD;QACxD,+CAA+C;QAC/C,IAAI;QACJ,IAAI,CAAC,OAAO;YACV,8DAA8D;YAC9D,MAAM,YAAY,IAAI,IAAI,6BAA6B,IAAI,GAAG;YAC9D,UAAU,YAAY,CAAC,GAAG,CAAC,eAAe,IAAI,GAAG;YAEjD,MAAM,WAAW,qLAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;YAEvC,OAAO;QACT;IACF;IAEA,mBAAmB;IACnB,OAAO,qLAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,yBAAyB;QACzB;KACD;AACH"}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}