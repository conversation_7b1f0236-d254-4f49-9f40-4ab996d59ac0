import { X } from "lucide-react";
import React from "react";
import { Vacancy } from "../CandidateTable/helper";

const SkillBadge = ({ skill, weight }: { skill: string; weight: string }) => {
  const getBadgeStyle = (weight: string) => {
    switch (weight) {
      case "high":
        return "bg-green-200/50 text-green-800 border border-green-300 shadow-md";
      case "medium":
        return "bg-blue-200/50 text-blue-800 border border-blue-300 shadow-md";
      case "normal":
        return "bg-yellow-200/50 text-yellow-800 border border-yellow-300 shadow-md";
      case "low":
        return "bg-red-200/50 text-red-800 border border-red-300 shadow-md";
      default:
        return "bg-gray-200 text-gray-700 border border-gray-300";
    }
  };

  return (
    <span
      className={`px-2 py-0.5 rounded-full text-xs font-semibold flex items-center gap-1 ${getBadgeStyle(
        weight
      )}`}
    >
      <span className="text-[12px] capitalize">{skill}</span>
      <span className="text-[10px] opacity-80 font-semibold">({weight})</span>
    </span>
  );
};

interface VacancyDetailsProps {
  vacancy: Vacancy | null;
  setActiveVacancy: (vacancy: Vacancy | null) => void;
  mercuryPortal?: boolean;
}

export default function VacancyDetails({
  vacancy,
  setActiveVacancy,
  mercuryPortal,
}: VacancyDetailsProps) {
  return (
    <div
      onClick={(e: React.MouseEvent<HTMLElement>) => {
        e.stopPropagation();
      }}
      className={`absolute w-[55%] left-[calc((100vw-900px)/2)] ${
        mercuryPortal ? "top-[5%]" : "top-[17%]"
      } bg-white shadow-lg border p-4 text-xs rounded-md z-[999] p-5`}
    >
      {/* Close Button */}
      <button
        className="absolute top-3.5 right-3 text-gray-600 hover:text-black"
        onClick={() => setActiveVacancy(null)}
      >
        <X size={18} />
      </button>

      <h3 className="text-base font-bold text-gray-800 mb-3 border-b pb-2">
        Vacancy Details
      </h3>

      <div className="max-h-[70vh] overflow-y-auto">
        <div className="mb-3">
          <p>
            <strong className="text-gray-800 text-md">Ref No:</strong>{" "}
            <span className="text-gray-800">{vacancy?.refno}</span>
          </p>
        </div>
        <div className="mb-3">
          <p>
            <strong className="text-gray-800 text-md">Vancancy Id:</strong>{" "}
            <span className="text-gray-800">{vacancy?.vacancy_id}</span>
          </p>
        </div>

        <div className="mb-3">
          <h4 className="text-lg font-semibold text-gray-800">Job Title</h4>
          <p className="text-gray-800">
            {vacancy?.vacancy_data?.["job title"]?.join(", ")}
          </p>
        </div>

        <div className="mb-3">
          <h4 className="text-lg font-semibold text-gray-800">Location</h4>
          <p className="text-gray-800">
            {vacancy?.vacancy_data?.["joblocation"]
              ?.map((loc: any) =>
                [loc.city, loc.state].filter(Boolean).join(", ")
              )
              .join(" | ")}
          </p>
        </div>

        {/* Soft Skills */}
        <div className="mb-3">
          <h4 className="text-lg font-semibold text-gray-800 mb-1">
            Soft Skills
          </h4>
          <div className="flex flex-wrap gap-2">
            {vacancy?.vacancy_data?.["soft skills"].map((skill, index) => (
              <SkillBadge
                key={index}
                skill={skill?.name}
                weight={skill?.weight}
              />
            ))}
          </div>
        </div>

        {/* Technical Skills */}
        <div className="mb-3">
          <h4 className="text-lg font-semibold text-gray-800 mb-1">
            Technical Skills
          </h4>
          <div className="flex flex-wrap gap-2">
            {vacancy?.vacancy_data?.["technical skills"].map((skill, index) => (
              <SkillBadge
                key={index}
                skill={skill?.name}
                weight={skill?.weight}
              />
            ))}
          </div>
        </div>

        {/* Tools & Platforms */}
        <div className="mb-3">
          <h4 className="text-lg font-semibold text-gray-800 mb-1">
            Tools & Platforms
          </h4>
          <div className="flex flex-wrap gap-2">
            {vacancy?.vacancy_data?.["tools and platforms"].map(
              (tool, index) => (
                <SkillBadge
                  key={index}
                  skill={tool?.name}
                  weight={tool?.weight}
                />
              )
            )}
          </div>
        </div>

        {/* Degrees & Certifications */}
        {vacancy?.vacancy_data?.["degrees and certifications"]?.length ? (
          <div className="mb-3">
            <h4 className="text-lg font-semibold text-gray-700 mb-3">
              Degrees & Certifications
            </h4>
            <ul className="text-gray-800 list-disc list-inside">
              {vacancy?.vacancy_data?.["degrees and certifications"].map(
                (degree, index) => (
                  <li key={index} className="text-xs capitalize">
                    {degree?.name}
                  </li>
                )
              )}
            </ul>
          </div>
        ) : null}

        {/* Years Of Experience */}
        {vacancy?.vacancy_data?.["years of experience"]?.length ? (
          <div className="mb-3">
            <h4 className="text-lg font-semibold text-gray-700 mb-3">
              Relavant Years Of Experience
            </h4>
            <div className="flex flex-wrap gap-2">
              {vacancy?.vacancy_data?.["years of experience"].map(
                (exp, index) => (
                  <SkillBadge
                    key={index}
                    skill={exp?.years}
                    weight={exp?.weight}
                  />
                )
              )}
            </div>
          </div>
        ) : null}

        {vacancy?.vacancy_data?.job_description ? (
          <div className="mb-1">
            <h4 className="text-lg font-semibold text-gray-700 mb-1">
              Job Description
            </h4>
            <p
              dangerouslySetInnerHTML={{
                __html: vacancy?.vacancy_data?.job_description,
              }}
              className="text-gray-700 mt-1 mb-2"
            ></p>
          </div>
        ) : null}
      </div>
    </div>
  );
}
