import os
import sys
import json
import requests
from dataverse_helper.token_manager import get_token_for_env, get_dataverse_credentials_for_env, Environment
from dataverse_helper.dv_common import update_contact_row_in_dataverse
from datetime import datetime, timedelta, timezone
import phonenumbers
import re
from common.appLogger import AppLogger

def get_ack_applicants_list(token, dataverse_url, logger):

    headers = {
        "Authorization": f"Bearer {token.get_token()}",
        "Content-Type": "application/json"
    }
    # Get current UTC time and 24 hours ago
    #now = datetime.datetime.now(datetime.timezone.utc)
    now = datetime.now(timezone.utc)
    yesterday = now - timedelta(days=3)

    # Format datetimes to ISO 8601 format with Zulu time
    now_str = now.strftime("%Y-%m-%dT%H:%M:%SZ")
    yesterday_str = yesterday.strftime("%Y-%m-%dT%H:%M:%SZ")

    # Construct the filter query
    filter_query = f"""statecode eq 0 and createdon ge {yesterday_str} and createdon lt {now_str}"""

  
    #filter_query = f"""statecode eq 0
    #and createdon ge 2025-04-21T00:00:00Z and createdon lt 2025-04-22T23:59:59Z""".strip()

    
    url = f"{dataverse_url}/api/data/v9.1/mercury_applicants?$select=mercury_name,_recruit_candidatecontact_value,_mercury_vacancyid_value,createdon,statecode&$filter={filter_query}"
    response = requests.get(url, headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        return  data.get("value", [])

    else:
        logger.error(f"file - get_candidate_list -  \nFailed to find. Status code: {response.status_code} - \nResponse:, {response.text}")
    return {}

def get_candidate_info_sms(token, dataverse_url, candidateid, logger):
    fields = ['address2_city', 'recruit_mobilehome', 'lastname','firstname', 'emailaddress2','mercury_lastcontacted', 'recruit_availability', 'telephone2', 'recruit_donotsms']

    # Join the fields for $select
    select_fields = ','.join(fields)
    # Navigation property for the country lookup
    expand_clause = ','.join([
        'recruit_address2_country($select=mercury_name)',
        'recruit_address2_state($select=mercury_name)'
    ])

     # Build the URL with $select, $expand, and $filter
    url = (
        f"{dataverse_url}/api/data/v9.2/contacts?"
        f"$select={select_fields}"
        f"&$expand={expand_clause}"
        f"&$filter=contactid eq {candidateid}"
    )
    # Prepare headers
    headers = {
        'Authorization': f'Bearer {token.get_token()}',
        'Content-Type': 'application/json',
        'OData-MaxVersion': '4.0',
        'OData-Version': '4.0'
    }

    # Make the request
    response = requests.get(url, headers=headers)

    # Check response and return data
    if response.status_code == 200:
        data = response.json()
        if 'value' in data and len(data['value']) > 0:
            return data['value']
        else:
            return []  # Empty if no data found
    else:
        logger.error(f" file get_candidate_newdata_tojson - {response.status_code} - {response.text}")
        return None



def is_valid_us_number(phone_number, logger):
    """
    Check if the provided phone number is a valid US number.
    
    Args:
        phone_number (str): The phone number to validate
        
    Returns:
        bool: True if it's a valid US number, False otherwise
    """
    try:
        # Parse the phone number
        parsed_number = phonenumbers.parse(phone_number, "US")
        
        # Check if it's a valid number
        if not phonenumbers.is_valid_number(parsed_number):
            return False
            
        # Check if it's a US number
        if phonenumbers.region_code_for_number(parsed_number) != "US":
            return False
            
        return True
    except Exception as e:
        logger.error(f"Error validating phone number {phone_number}: {str(e)}")
        return False

def validate_and_clean_phone(phone_number, logger):
    """
    Validates and cleans a US phone number.
    Returns (True, cleaned_number) if valid, (False, "") if invalid.
    
    Requirements:
    - Must start with +1
    - Must have exactly 10 digits after +1
    - No extensions allowed
    - No spaces or dashes allowed in final format
    """
    try:
        # Remove all spaces, dashes, and parentheses
        cleaned = re.sub(r'[\s\-\(\)]', '', phone_number)
        
        # Check for extensions
        if re.search(r'ext\.?|extension', cleaned.lower()):
            return False, ""
            
        # Check if it starts with +1 and has exactly 10 digits after
        if not re.match(r'^\+1\d{10}$', cleaned):
            return False, ""
            
        return True, cleaned
    except Exception as e:
        logger.error(f"Error validating phone number {phone_number}: {str(e)}")
        return False, ""

def thank_and_update_candidate_air(logger, api_version="v1", batch_id=None, assistant_id="", assistant_type="address_gathering_agent", communication_mode=["SMS"]):
    enum_env = Environment.PROD  # Prod
    token = get_token_for_env(enum_env)
    credentials = get_dataverse_credentials_for_env(enum_env)
    dataverse_url = credentials["RESOURCE_URL"]

    rows = get_ack_applicants_list(token, dataverse_url, logger)
    
    # Log differently based on API version
    if api_version == "v2":
        logger.info(f" number entries is {len(rows)} - batch_id: {batch_id} - assistant_id: {assistant_id} - assistant_type: {assistant_type} - communication_mode: {communication_mode}")
    else:
        logger.info(f" number entries is {len(rows)}")

    for row in rows:
        contactid = row['_recruit_candidatecontact_value']
        logger.debug(f"get info on candidate {contactid}")
        candi = get_candidate_info_sms(token, dataverse_url, contactid, logger)

        logger.debug(f" Candidate info - {candi}")
        if candi == []:
           
            logger.info(f"couldn't get info on candidate {contactid}")
            continue
        cand = candi[0]
        if cand['recruit_donotsms'] == True:
            logger.info(f"Skipping candidate {contactid} - donotsms is True")
            continue
        mobile_number = cand['recruit_mobilehome'] 
        if mobile_number == None or mobile_number == "":
            
            
            if cand['telephone2'] != None:
                mobile_number = cand['telephone2']
            else:
                logger.info(f"{contactid} -{cand['emailaddress2']} has no phone number (mobile or telephone2) found for candidate")
                continue 
        if not is_valid_us_number( mobile_number, logger):
            logger.info(f"{contactid} -{cand['emailaddress2']} has no valid US phone number found for candidate")
            continue
        is_valid, cleaned_number = validate_and_clean_phone(mobile_number, logger) 
        if not is_valid:
            logger.info(f"{contactid} -{cand['emailaddress2']} has no valid US phone number found for candidate")
            continue
        mobile_number = cleaned_number
        firstname = cand['firstname']
        lastname = cand['lastname']
        today = datetime.now().date()
        last_contacted = datetime.strptime(cand['mercury_lastcontacted'], '%Y-%m-%d').date() if cand['mercury_lastcontacted'] else None
        days_since_last_contact = (today - last_contacted).days if last_contacted else float('inf')
        if days_since_last_contact < 14:
            logger.info(f"Skipping candidate {contactid} - last contacted {days_since_last_contact} days ago")
            continue
       
        if cand['address2_city'] == None or cand['recruit_address2_state'] == None:
            getaddress = "True"

        else: 
            getaddress = "False"
        

        
        logger.info(f"{firstname} {lastname} - {contactid} - {cand['emailaddress2']} - getaddress: {getaddress}")

        
        status = get_candidate_address_air(mobile_number, firstname, contactid, Environment.PROD.name, getaddress, logger, api_version, batch_id, assistant_id, assistant_type, communication_mode)
        if status == "failed":
            logger.error(f"Failed to send sms for {contactid}")
            continue

        #Update mercury_lastcontacted and mercury_lastcontactedby
        user_id = 'ffcabcc0-45df-ef11-a730-6045bddab5d6' #Tandymbot's user id for prod
        new_row_data = {
        'mercury_lastcontacted': datetime.today().date().isoformat(),
        '<EMAIL>': f"/systemusers({user_id})"
        }
        response = update_contact_row_in_dataverse(token, dataverse_url, contactid, new_row_data)
        if response is None:
            logger.error(f"couldn't update mercury with new address {contactid}")
            continue
        if response.status_code != 204:
            logger.error(f"file - update_candidate_classification_batch -  \nFailed to add no 'nosubcat'. Status code: {response.status_code} - \nResponse:, {response.text}")
            continue
        
def test_get_address_air(prod, logger, api_version="v1", batch_id=None, assistant_id="", assistant_type="address_gathering_agent", communication_mode=["SMS"]):
    if prod:
        if api_version == "v2":
            logger.info(f"sending sms - prod - {batch_id} - {assistant_id} - {assistant_type} - {communication_mode}")
        else:
            logger.info("sending sms - prod")
        get_candidate_address_air("+14083064682", "Satish", 'e5684464-40a8-ef11-8a69-7c1e525c7700', Environment.PROD.name, "True", logger, api_version, batch_id, assistant_id, assistant_type, communication_mode)
        get_candidate_address_air("+17819752653", "Prakash", '65b2e43e-40a8-ef11-8a69-7c1e525c7700', Environment.PROD.name, "True", logger, api_version, batch_id, assistant_id, assistant_type, communication_mode)
    else:
        if api_version == "v2":
            logger.info(f"sending sms - sandbox - {batch_id} - {assistant_id} - {assistant_type} - {communication_mode}")
            get_candidate_address_air("+17819752653", "Prakash", 'a3053c48-15db-ee11-904d-0022482d3acf', Environment.SANDBOX.name, "True", logger, api_version, batch_id, assistant_id, assistant_type, communication_mode)
        else:
            logger.info("sending sms - sandbox")
            get_candidate_address_air("+14083064682", "Satish", 'ff70eb31-4d77-ef11-a670-6045bdd94053', Environment.SANDBOX.name, "True", logger, api_version, batch_id, assistant_id, assistant_type, communication_mode)
            get_candidate_address_air("+14084806015", "Rajesh", 'a3053c48-15db-ee11-904d-0022482d3acf', Environment.SANDBOX.name, "True", logger, api_version, batch_id, assistant_id, assistant_type, communication_mode)
            get_candidate_address_air("+19145723325", "Ravi", '429b8dac-7864-ef11-bfe3-000d3a16b58c', Environment.SANDBOX.name, "True", logger, api_version, batch_id, assistant_id, assistant_type, communication_mode)
            get_candidate_address_air("+17819752653", "Prakash", 'a3053c48-15db-ee11-904d-0022482d3acf', Environment.SANDBOX.name, "True", logger, api_version, batch_id, assistant_id, assistant_type, communication_mode)

def get_candidate_address_air(mobile_number, firstname, contactid, env, getaddress, logger, api_version="v1", batch_id=None, assistant_id="", assistant_type="", communication_mode=["SMS"]):
    if env == Environment.PROD.name:
        key = os.getenv("AIR_KEYS_PROD")
        url = f"https://api.tandymgroup.com/manager/api/{api_version}/queue/send-message"
    else:
        key = os.getenv("AIR_KEYS_SANDBOX")
        url = f"https://api.sb.tandymgroup.com/manager/api/{api_version}/queue/send-message"
    
    headers = {
        "accept": "application/json",
        "Content-Type": "application/json",
        "X-Tandym-Manager-API-Key": key
    }

    # Different data structure based on API version
    if api_version == "v2":
        data = {
            "communication_info": {
                "communication_mode": communication_mode,
                "communication_batch_id": batch_id
            },
            "assistant_info": {
                "assistant_type": assistant_type,
                "assistant_id": assistant_id,
                "initial_utterance": f"Start prompting the user= {firstname}, address = {getaddress} and todaydate= {datetime.today().date().isoformat()}"
            },
            "contact_info": {
                "contact_id": contactid,
                "contact_name": firstname,
                "contact_phone": mobile_number
            }
        }
    else:  # v1
        data = {
            "topic": "ADDRESS_INITILAIZE",
            "message": {
                "contact_id": contactid,
                "name": firstname,
                "get_address": getaddress,
                "phone": mobile_number
            }
        }

    response = requests.post(url, headers=headers, json=data)
    if response.status_code == 200:
        logger.info(f"sent sms to {mobile_number} - {firstname} - {contactid} - {env} - {getaddress} - {response.status_code} - {response.text}")
        return "success"
    else:
        logger.error(f"failed to send sms to {mobile_number} - {firstname} - {contactid} - {env} - {getaddress} - {response.status_code} - {response.text}")
        return "failed"

def generate_batch_id(env, batch_id):
    if batch_id == None:
        return f"batch_{env}_{datetime.now().strftime('%Y%m%d-%H%M%S')}"
    else:
        return f"batch_{env}_{datetime.now().strftime('%Y%m%d-%H%M%S')}_{batch_id}"
        
if __name__ == "__main__":
    config = {
        "name": "logger_taac",
        "use_syslog": False,
        "log_to_stdout": False,
        "log_file": f"/mnt/incoming/logs/thank_avail_add_cand.log",
        "log_level": "INFO",
    }
    logger_taac = AppLogger(config)
    
    if len(sys.argv) > 1:
        type = sys.argv[1]
        # New parameter for API version (sys.argv[2])
        api_version = sys.argv[2] if len(sys.argv) > 2 else "v1"
        # Optional batch_id parameter (sys.argv[3])
        batch_id_param = sys.argv[3] if len(sys.argv) > 3 else None
        
        batch_id = generate_batch_id(type, batch_id_param)
        assistant_id = ""
        assistant_type = "address_gathering_agent"
        communication_mode = ["SMS"]

        if type == "prod":
            if api_version == "v2":
                assistant_id = "asst_6nbTtaKXDhVGSumkIWMIZA1K"
            thank_and_update_candidate_air(logger_taac, api_version, batch_id, assistant_id, assistant_type, communication_mode)
        elif type == "sandbox":
            if api_version == "v2":
                assistant_id = "asst_6nbTtaKXDhVGSumkIWMIZA1K"
            test_get_address_air(False, logger_taac, api_version, batch_id, assistant_id, assistant_type, communication_mode)
        elif type == "prod-test":
            if api_version == "v2":
                assistant_id = "asst_6nbTtaKXDhVGSumkIWMIZA1K"
            test_get_address_air(True, logger_taac, api_version, batch_id, assistant_id, assistant_type, communication_mode)

    #thank_and_update_candidate()
    #test_thank_candidates()
    #test_get_address()
    
    #load_secrets_env_variables() #uncomment this to run update_avail_address
    #add_obj = {'street': '123 MLK Jr Street', 'city': 'Huntsville', 'state': 'Alabama', 'zip code': '35802'}
    #add_obj = {'street': '3450 pin ct', 'city': 'Washington', 'state': 'DC', 'zip code': '20002'}
    #add_obj = {'availability': '2025-05-01', 'address': {'street': '3499 pin ct', 'city': 'San Jose', 'state': 'CA', 'zip code': '95148'}}
    #add_obj = {'availability': '2025-05-11', 'address': {'street': '5656 Alog Way', 'city': 'Santa Clara', 'state': 'CA', 'zip code': '95138'}}
    #update_avail_address ("e5684464-40a8-ef11-8a69-7c1e525c7700", Environment.PROD.name, add_obj)
    #test_get_address()