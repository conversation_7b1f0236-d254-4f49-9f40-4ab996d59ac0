# dpserver/main.py
import uuid
from fastapi import FastAPI, Request
from dpserver.apis.routes import Routes
from common.db.postgres_connector import PostgresConnector, PostgresEnvironment
from common.appLogger import AppLogger
from common.secrets_env import load_all_secrets
from dpserver.services.attribute_service import AttributeService
from dpserver.services.subcategory_service import SubcategoryService
from dpserver.services.vacancy_service import VacancyService
from dpserver.services.parse_resume_service import ParseResumeService
from dpserver.services.experiment_service import ExperimentService # New Import
from dataverse_helper.token_manager import Environment
import uvicorn
import json
from fastapi.middleware.cors import CORSMiddleware
import os
import logging
import sys
from dpserver.utils.app_diagnostics_logger import AppDiagnosticsLogger
from common.utils.error_handler import add_error_handlers
from dpserver.utils.context import correlation_id_ctx
from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
from dpserver.config.azure_config_fetcher import fetch_config_from_azure
from dpserver.utils.env_utils import EnvUtils
from opentelemetry.sdk.resources import SERVICE_NAME, Resource
from opentelemetry.semconv.resource import ResourceAttributes
from opentelemetry.instrumentation.logging import LoggingInstrumentor

CORRELATION_ID_HEADER = "X-Correlation-Id"     

class AppServer:
    """Encapsulates the FastAPI server setup, database connection, and lifecycle management."""

    def __init__(self, app_config_file):
        """Initialize the FastAPI application and configure lifecycle events."""
        config = EnvUtils.load_config(app_config_file)
        self.config = config
        log_name = config.get("logging", {}).get("name", "DEFAULT_LOGGER")

        IS_FETCH_FROM_KEY_VAULT =  self.get_env_flag()
        
        db_env_str = config.get("db", {}).get("env", "DEV").upper()
        db_env = PostgresEnvironment[db_env_str] if db_env_str in PostgresEnvironment.__members__ else PostgresEnvironment.DEV
        if IS_FETCH_FROM_KEY_VAULT:
            db_env = PostgresEnvironment.AZ_APP_VAULT
            
        self.sp_env_str = config.get("sharepoint", {}).get("env", "SANDBOX").upper()
        # Assuming sp_env values 0:SANDBOX, 1:UAT, 2:PROD, 3:AZ_APP_VAULT
        sp_env_map = {"SANDBOX": 0, "UAT": 1, "PROD": 2}
        self.sp_env = sp_env_map.get(self.sp_env_str, 0)
        if IS_FETCH_FROM_KEY_VAULT:
            self.sp_env = 3

        # Configure dataverse environment
        self.dataverse_env_str = config.get("dataverse", {}).get("env", "SANDBOX").upper()
        # Map string values to Environment enum
        dataverse_env_map = {"SANDBOX": Environment.SANDBOX, "UAT": Environment.UAT, "PROD": Environment.PROD}
        self.dataverse_env = dataverse_env_map.get(self.dataverse_env_str, Environment.SANDBOX)
        
        if IS_FETCH_FROM_KEY_VAULT:
            self.dataverse_env = Environment.AZ_APP_VAULT

        self.logger = AppDiagnosticsLogger(log_name)

        app_config = config.get("app", {})
        self.host = app_config.get("host", "0.0.0.0")
        self.port = app_config.get("port", 8005)
        self.app = FastAPI(title="DP Server API") 

        FastAPIInstrumentor.instrument_app(self.app)

        if not IS_FETCH_FROM_KEY_VAULT:
            secrets_config = config.get("secrets", {})
            if secrets_config: # Check if secrets_config is not empty
                 load_all_secrets(secrets_config)
            else:
                 self.logger.warning("Secrets configuration is missing or empty in appconfig.json")
        else:
            fetch_config_from_azure()

        if IS_FETCH_FROM_KEY_VAULT:
          self.configure_logging_and_telemetry(config, log_name)
        else:      
          self.logger.warning(f"IS_FETCH_FROM_KEY_VAULT environment variable is {IS_FETCH_FROM_KEY_VAULT}")

        self.db_env = db_env
        self.db_connector = PostgresConnector(self.db_env, logger=self.logger)
        # Connection is established in startup or by get_connection() when first needed

        # Initialize services
        self.attribute_service = AttributeService(self.logger, self.db_connector)
        self.subcategory_service = SubcategoryService(self.logger, self.db_connector)
        self.vacancy_service = VacancyService(self.logger, self.db_connector, self.dataverse_env) # Kept intact
        self.parse_resume_service = ParseResumeService(self.logger, self.db_connector, self.sp_env, self.db_env)
        self.experiment_service = ExperimentService(self.logger, self.db_connector) # New Service

        # Initialize Routes class and include its router
        # Ensure postgres_db_connector argument in Routes matches parameter name
        self.routes = Routes(
            logger=self.logger, 
            attribute_service=self.attribute_service, 
            subcategory_service=self.subcategory_service, 
            vacancy_service=self.vacancy_service, 
            parse_resume_service=self.parse_resume_service,
            experiment_service=self.experiment_service, 
            postgres_db_connector=self.db_connector # Corrected argument name
        )
        self.app.include_router(self.routes.router)
        self.add_middlewares()
        self.app.add_event_handler("startup", self.startup)
        self.app.add_event_handler("shutdown", self.shutdown)

    def get_env_flag(self):
        return os.getenv("APP_ENV", "local").lower() != "local"
    
    def configure_logging_and_telemetry(self, config, log_name):
        # === Enhance log records with correlation ID ===
        old_factory = logging.getLogRecordFactory()

        def record_factory(*args, **kwargs):
            record = old_factory(*args, **kwargs)
            record.correlation_id = correlation_id_ctx.get() or "n/a"
            return record

        logging.setLogRecordFactory(record_factory)

        # === Initialize base logging ===
        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s [%(levelname)s] %(name)s: %(message)s"
        )
        logger = logging.getLogger(__name__)

        # === Append project root to sys.path if needed ===
        sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

        # === Application Insights Setup ===
        APP_INSIGHTS_CONNECTION_STRING = EnvUtils.get_clean_app_insights_connection_string()
        logger.warning(f"APP_INSIGHTS_CONNECTION_STRING environment variable is {APP_INSIGHTS_CONNECTION_STRING} ")
        resource = Resource.create({
            ResourceAttributes.SERVICE_NAME: log_name,
        })

        try:
            from azure.monitor.opentelemetry import configure_azure_monitor
        except ImportError:
            configure_azure_monitor = None
            logger.warning("Azure Monitor SDK not found. Skipping telemetry setup.")

        if not APP_INSIGHTS_CONNECTION_STRING:
            logger.warning("APP_INSIGHTS_CONNECTION_STRING environment variable is not set.")
        elif configure_azure_monitor is None:
            logger.info("Telemetry is not configured because the SDK is unavailable.")
        else:
            logger.info("Using Application Insights connection string.")
            configure_azure_monitor(
                connection_string=APP_INSIGHTS_CONNECTION_STRING,
                logger_level="DEBUG",
                resource=resource,
                exporter_options={
                    "timeout": 30,
                    "max_batch_size": 50
                }
            )

        # === Telemetry Exporter Logger Setup ===
        telemetry_logger = logging.getLogger("telemetry_exporter")
        telemetry_logger.setLevel(logging.ERROR)

        if not telemetry_logger.handlers:
            handler = logging.StreamHandler()
            handler.setFormatter(logging.Formatter(
                '%(asctime)s %(levelname)s trace_id=%(otelTraceID)s '
                'span_id=%(otelSpanID)s correlation_id=%(correlation_id)s %(message)s'
            ))
            telemetry_logger.addHandler(handler)

        # Enable OpenTelemetry log injection
        LoggingInstrumentor().instrument(set_logging_format=True)

    
    def add_middlewares(self):
            add_error_handlers(self.app)

            # Add correlation ID middleware
            self.app.middleware("http")(self.correlation_id_middleware)

            # Configure CORS
            allowed_origins = self.config.get("cors", {}).get("allowed_origins", ["http://localhost"])
            self.app.add_middleware(
                CORSMiddleware,
                allow_origins=allowed_origins, 
                allow_credentials=True,
                allow_methods=["GET", "POST", "PUT", "DELETE"],
                allow_headers=["*"],
            )

    
    async def correlation_id_middleware(self, request: Request, call_next):
        correlation_id = request.headers.get(CORRELATION_ID_HEADER)

        if not correlation_id:
         correlation_id = str(uuid.uuid4())

        correlation_id_ctx.set(correlation_id)
        response = await call_next(request)
        response.headers[CORRELATION_ID_HEADER] = correlation_id
        return response
        
    def startup(self):
        """Initialize database connection when FastAPI starts."""
        self.logger.info("Starting application...")
        try:
            # Attempt to establish and verify the connection at startup
            # The get_connection method in PostgresConnector should handle the actual connect() call
            self.db_connector.connect() 
            self.logger.info("Database connection verified successfully at startup.")
        except Exception as e:
            self.logger.error(f"Failed to establish database connection at startup: {e}", exc_info=True)
            # Depending on policy, you might want to exit or let it try on first request
            # For now, it will log the error and proceed; requests will try to connect.

    def shutdown(self):
        """Close database connection when FastAPI shuts down."""
        self.logger.info("Shutting down application...")
        if self.db_connector:
            self.db_connector.close()

    def run(self):
        """Run the FastAPI server with Uvicorn."""
        uvicorn.run(self.app, host=self.host, port=self.port)

# Test functions (kept for your convenience)
def test_config():
    db_env = PostgresEnvironment.DEV
    config_path = "dpserver/config/appconfig.json"
    with open(config_path, "r") as f:
        config = json.load(f)
    db = config.get("db", {})
    env = db.get("env", "DEV")
    print(env)
    if env == "QA": db_env = PostgresEnvironment.QA
    if env == "UAT": db_env = PostgresEnvironment.UAT
    if env == "PROD": db_env = PostgresEnvironment.PROD
    print(db_env)

def test():
    config_path = "dpserver/config/appconfig.json"
    server = AppServer(config_path)
    logger = server.logger
    logger.info("Testing FastAPI server startup...")
    server.startup()
    logger.info("Testing database connection...")
    if server.db_connector and server.db_connector.connection and not server.db_connector.connection.closed:
        logger.info("Database connection established successfully.")
    else:
        logger.info("Database connection potentially not established or failed during startup.")
    logger.info("Testing FastAPI server shutdown...")
    server.shutdown()
    logger.info("Test function executed successfully.")

def main():
    app_config_path = os.getenv("APP_CONFIG_PATH", "dpserver/config/appconfig.json")
    server = AppServer(app_config_path) 
    server.run()

if __name__ == "__main__":
    main()