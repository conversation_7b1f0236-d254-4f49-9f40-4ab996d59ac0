trigger:
  branches:
    include:
      - develop
  paths:
    include:
      - tandym-dp-web

pr:
  branches:
    include:
      - develop
  paths:
    include:
      - tandym-dp-web
parameters:
  - name: ImageId
    displayName: Image ID
    type: string
    default: 1234
  - name: location
    displayName: Location
    type: string
    values:
      - ue
    default: ue
  - name: environment
    displayName: Environment
    type: string
    values:
      - dv
      - qa
      - sbe
      - ua
      - pd
    default: dv
  - name: Deploy
    displayName: Deploy
    type: boolean
    default: true

variables:
  - template: variables/${{ parameters.environment }}.yml
  - template: variables/global_variables.yml
    parameters:
      location: ${{ parameters.location }}
      environment: ${{ parameters.environment }}
      # Deploy: ${{ parameters.Deploy }}
  - name: IMAGE_REPOSITORY
    value: "tg${{ parameters.environment }}${{ parameters.location }}envcr001.azurecr.io/tandym-dp-web"
  - name: imageId
    ${{ if eq( parameters['environment'] , 'sbe') }}:
      value: "$(Build.SourceBranchName)-$(Build.BuildId)"
    ${{ else }}:
      value: $(Build.BuildId)
  - name: BuildConfiguration
    value: Release
  # - group: SONAR_CONFIG

stages:
  - stage: Deploy
    displayName: Deploy Stage
    condition: and(succeeded(), eq(${{ parameters.Deploy }}, true))
    pool:
      name: tg-${{ parameters.environment }}-pool
    jobs:
      - job: Deploy
        displayName: Deploy ${{ parameters.environment }} DP WEB
        steps:
          - checkout: self
          - script: |
              # Update package lists
              sudo apt-get update
              sudo apt-get install -y azure-cli postgresql-client

              # Install dependencies
              sudo apt-get install -y software-properties-common
              sudo add-apt-repository ppa:deadsnakes/ppa
              sudo apt-get update

              # Install PostgreSQL client
              sudo apt-get install -y postgresql-client

              # Install Azure CLI
              curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash
            displayName: "Install Azure CLI and PostgreSQL CLI"
          - script: |
              echo "Checking and installing unzip if needed"
              if ! command -v unzip &> /dev/null; then
                echo "unzip not found, installing..."
                sudo apt-get update
                sudo apt-get install -y unzip
              else
                echo "unzip is already installed"
              fi
            displayName: Check and Install unzip

          # Fetch secrets from Key Vault
          - template: common/fetch_keyvault_secrets.yml
            parameters:
              armServiceConnection: ${{ variables.ServiceConnectionName }}
              keyVaultName: ${{ variables.KeyVaultName }}
              secretsToFetch: "AcrName,AcrUsername,AcrPassword,AcrUrl,Client-ID,Client-Secret,AzTenant-Id,Subscription-ID,recruiter-sso-client-id,recruiter-sso-client-secret,nextauth-secret"
              runAsPreJob: true

          # Update deployment YAML
          - bash: |
              echo "Loading Azure Identity"
              envsubst < $(System.DefaultWorkingDirectory)/tandym-dp-web/manifests/deployment.yml > temp_data_deployment && mv temp_data_deployment $(System.DefaultWorkingDirectory)/tandym-dp-web/manifests/deployment.yml
              cat $(System.DefaultWorkingDirectory)/tandym-dp-web/manifests/deployment.yml
              envsubst < $(System.DefaultWorkingDirectory)/tandym-dp-web/manifests/service.yml > temp_data_service && mv temp_data_service $(System.DefaultWorkingDirectory)/tandym-dp-web/manifests/service.yml
              cat $(System.DefaultWorkingDirectory)/tandym-dp-web/manifests/service.yml
              envsubst < $(System.DefaultWorkingDirectory)/tandym-dp-web/manifests/autoscale.yml > temp_data_deployment && mv temp_data_deployment $(System.DefaultWorkingDirectory)/tandym-dp-web/manifests/autoscale.yml
              cat $(System.DefaultWorkingDirectory)/tandym-dp-web/manifests/autoscale.yml
            displayName: Loading Azure Identity
            env:
              AZURE_CLIENT_ID: $(Client-ID)
              AZURE_CLIENT_SECRET: $(Client-Secret)
              AZURE_TENANT_ID: $(AzTenant-Id)
              AZURE_SUBSCRIPTION_ID: $(Subscription-ID)
              AppConfigurationEndpoint: ${{ variables.AppConfigurationEndpoint }}
              webcontainerimage: $(webcontainerimage)
              WEB_LB_IP: ${{ variables.WebLBIP }}
              HPA_NAME: tandym-dp-web
              NAMESPACE: tandym-dp
              MIN_REPLICAS: ${{ variables.WebMinReplicas }}
              MAX_REPLICAS: ${{ variables.WebMaxReplicas }}
              DEPLOYMENT_NAME: tandym-dp-web
              MEM_PERCENTAGE: ${{ variables.WebMemThreshold }}
              CPU_PERCENTAGE: ${{ variables.WebCpuThreshold }}
              NEXT_PUBLIC_BASE_URL: http://${{ variables.ServerLBIP }}
              RECRUITER_SSO_CLIENT_ID: $(recruiter-sso-client-id)
              RECRUITER_SSO_CLIENT_SECRET: $(recruiter-sso-client-secret)
              NEXTAUTH_SECRET: $(nextauth-secret)
              NEXTAUTH_URL: ${{ variables.NEXTAUTH_URL }}
              DP_PORTAL_SERVICE: http://${{ variables.PortalLBIP }}
              NEXT_PUBLIC_PARSE_RESUME_FROM_MERCURY: ${{ variables.NEXT_PUBLIC_PARSE_RESUME_FROM_MERCURY }}
              NEXT_PUBLIC_AD_LOGIN: ${{ variables.NEXT_PUBLIC_AD_LOGIN }}
              NEXT_PUBLIC_AUTH_URL: ${{ variables.NEXT_PUBLIC_AUTH_URL }}
              NEXT_PUBLIC_APPINSIGHTS_CONNECTION_STRING: ${{ variables.NEXT_PUBLIC_APPINSIGHTS_CONNECTION_STRING }}
              CRM_URL: ${{ variables.CRM_URL }}

          # Deploy to AKS
          - template: ./common/aks-deploy.yml
            parameters:
              location: ${{ parameters.location }}
              environment: ${{ parameters.environment }}
              namespace: tandym-dp
              imagePullSecrets: tandym-dp-pull-secret
              manifests: |
                $(System.DefaultWorkingDirectory)/tandym-dp-web/manifests/deployment.yml
                $(System.DefaultWorkingDirectory)/tandym-dp-web/manifests/service.yml
                $(System.DefaultWorkingDirectory)/tandym-dp-web/manifests/autoscale.yml
              containers: |
                $(AcrUrl)/tandym-dp-web:${{ parameters.ImageId }}
              sharepointSecret: $(${{ parameters.environment }}-sharepoint)
