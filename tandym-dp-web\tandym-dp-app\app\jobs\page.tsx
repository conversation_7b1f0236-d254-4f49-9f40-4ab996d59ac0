"use client";

import React, { useCallback, useEffect, useState } from "react";
// import { usePathname, useRouter } from "next/navigation";
import { SquareChevronLeft, SquareChevronRight } from "lucide-react";
import { getData } from "@/api/get";
import { API_ENDPOINTS } from "@/api/config";

interface JobDetails {
  id: number;
  job_title: string;
  client_name: string;
  job_code: string;
  posted_date: string; // Consider using Date if you plan to parse it
  job_role: string;
  job_description: string;
  job_status: string;
  job_posted_link: string;
  job_location: string;
  job_type: string;
  pay_rate: number | string; // Use number if it's strictly numerical
  pay_type: string;
  recruiter_name: string;
  recruiter_number: string;
  recruiter_email: string;
  applied_candidates_count: number;
}

const Jobs: React.FC = () => {
  const [jobs, setJobs] = useState<JobDetails[]>([]);
  const [selectedJob, setSelectedJob] = useState<JobDetails | null>(null);
  const [page, setPage] = useState<number>(1);
  const [limit, setLimit] = useState<number>(10); // Number of items per page
  const [totalJobs, setTotalJobs] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(false);
  const [search, setSearch] = useState<string>("");
  // const router = useRouter();
  // const pathname = usePathname();
  // const searchParams = useSearchParams();

  const fetchJobs = useCallback(async () => {
    try {
      const skip = (page - 1) * limit;
      setLoading(true);
      const response = await getData(
        `${API_ENDPOINTS.jobsData}?skip=${skip}&limit=${limit}&search=${search}`
      );
      if (response) {
        setJobs(response.jobs || []);
        setTotalJobs(response.total || 0);
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
      console.error("Error fetching jobs:", error);
    }
  }, [page, limit, search]);

  useEffect(() => {
    fetchJobs();
  }, [page, limit, search, fetchJobs]);

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setSearch(value);
    // const params = new URLSearchParams(searchParams);
    // if (value) {
    //   params.set("search", value);
    // } else {
    //   params.delete("search");
    // }

    // router.push(`${pathname}?${params.toString()}`);
  };

  return (
    <div className="p-4">
      <h2 className="text-xl font-bold mb-4">Jobs</h2>
      <div className="">
        <input
          type="search"
          value={search}
          onChange={handleSearch}
          placeholder="Search by name or email"
          className="py-2 px-2 border my-2 w-1/3 outline-none"
        />
      </div>
      <table className="w-full border-collapse border border-gray-300">
        <thead>
          <tr className="bg-gray-100">
            <th className="border p-2">Job Title</th>
            <th className="border p-2">Client</th>
            <th className="border p-2">Job Code</th>
            <th className="border p-2">Status</th>
            <th className="border p-2">Posted Date</th>
            <th className="border p-2">Role</th>
            <th className="border p-2">Location</th>
            <th className="border p-2">Recruiter Email</th>
            <th className="border p-2">Applied Candidates</th>
            <th className="border p-2">Job Link</th>
          </tr>
        </thead>
        <tbody>
          {loading ? (
            <div className="absolute w-full">
              <div className="h-1 w-full overflow-hidden">
                <div className="progress w-full h-full bg-gradient-to-r from-teal-500 to-transparent left-right"></div>
              </div>
            </div>
          ) : null}
          {!loading && jobs.length > 0 ? (
            jobs.map((job) => {
              return (
                <tr key={job.id} className="text-center">
                  <td className="border p-2">{job.job_title}</td>
                  <td className="border p-2">{job.client_name}</td>
                  <td className="border p-2">{job.job_code}</td>
                  <td className="border p-2">{job?.job_status || "N/A"}</td>
                  <td className="border p-2">{job?.posted_date || "N/A"}</td>
                  <td className="border p-2">{job?.job_role || "N/A"}</td>
                  <td className="border p-2">{job?.job_location || "N/A"}</td>
                  <td className="border p-2">{job.recruiter_email}</td>
                  <td className="border p-2">{job.applied_candidates_count}</td>
                  <td className="border p-2">
                    {job.job_posted_link ? (
                      <a
                        href={job.job_posted_link}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-500 underline"
                      >
                        View
                      </a>
                    ) : (
                      "N/A"
                    )}
                  </td>
                  <td className="border p-2">
                    <button
                      onClick={() => setSelectedJob(job)}
                      className="bg-blue-500 text-white px-3 py-1 rounded"
                    >
                      More
                    </button>
                  </td>
                </tr>
              );
            })
          ) : (
            <tr>
              <td colSpan={9} className="border p-4 text-center">
                No jobs found
              </td>
            </tr>
          )}
        </tbody>
      </table>

      {/* Pagination */}
      <div className="flex justify-between items-center mt-4">
        {/* Items Per Page Selection */}
        <div className="">
          <label className="mr-2">Per page:</label>
          <select
            value={limit}
            onChange={(e) => {
              setLimit(Number(e.target.value));
              setPage(1); // Reset to first page when changing limit
            }}
            className="border p-1 outline-none"
          >
            {[2, 5, 10, 20, 50, 100].map((num) => (
              <option key={num} value={num}>
                {num}
              </option>
            ))}
          </select>
        </div>
        <div className="flex items-center">
          <button
            onClick={() => setPage((prev) => Math.max(prev - 1, 1))}
            disabled={page === 1}
            className={`px-0 py-0 mx-2`}
          >
            <SquareChevronLeft
              className={`${page === 1 ? "text-gray-400" : "text-gray-800"}`}
            />
          </button>
          <span className="mx-2">{page}</span>
          <button
            onClick={() =>
              setPage((prev) => (prev * limit < totalJobs ? prev + 1 : prev))
            }
            disabled={page * limit >= totalJobs}
            className={`px-0 py-0 mx-2`}
          >
            <SquareChevronRight
              className={`${
                page * limit >= totalJobs ? "text-gray-400" : "text-gray-800"
              }`}
            />
          </button>
        </div>
      </div>

      {/* Job Details Modal */}
      {selectedJob && (
        <div className="fixed inset-0 bg-gray-800 bg-opacity-50 flex items-center justify-center">
          <div className="bg-white w-[80vw] p-6 rounded-lg max-h-[95vh] overflow-y-auto">
            <h2 className="text-xl font-bold mb-4">Job Details</h2>

            <p>
              <strong>Job Title:</strong> {selectedJob.job_title}
            </p>
            <p>
              <strong>Client:</strong> {selectedJob.client_name}
            </p>
            <p>
              <strong>Job Code:</strong> {selectedJob.job_code}
            </p>
            <p>
              <strong>Status:</strong> {selectedJob.job_status}
            </p>
            <p>
              <strong>Posted Date:</strong> {selectedJob.posted_date}
            </p>
            <p>
              <strong>Role:</strong> {selectedJob.job_role}
            </p>
            <p>
              <strong>Description:</strong> {selectedJob.job_description}
            </p>
            <p>
              <strong>Location:</strong> {selectedJob.job_location}
            </p>
            <p>
              <strong>Job Type:</strong> {selectedJob.job_type}
            </p>
            <p>
              <strong>Pay Rate:</strong> ${selectedJob.pay_rate}{" "}
              {selectedJob.pay_type}
            </p>
            <p>
              <strong>Applied Candidates:</strong>{" "}
              {selectedJob.applied_candidates_count}
            </p>

            <h3 className="text-lg font-semibold mt-4">Recruiter Details</h3>
            <p>
              <strong>Name:</strong> {selectedJob.recruiter_name}
            </p>
            <p>
              <strong>Email:</strong> {selectedJob.recruiter_email}
            </p>
            <p>
              <strong>Phone:</strong> {selectedJob.recruiter_number}
            </p>

            <p>
              <strong>Job Link:</strong>{" "}
              <a
                href={selectedJob.job_posted_link}
                className="text-blue-500 underline"
                target="_blank"
                rel="noopener noreferrer"
              >
                View Job
              </a>
            </p>

            <button
              onClick={() => setSelectedJob(null)}
              className="bg-red-500 text-white px-4 py-2 mt-4 rounded"
            >
              Close
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default Jobs;
