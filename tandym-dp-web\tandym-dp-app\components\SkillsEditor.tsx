"use client";
import { useNotification } from "@/hooks/useNotification";
import Loading from "@/components/Loading";
import Button from "@/components/Button";
import Modal from "@/components/Modal";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { getColumnName } from "@/app/helper";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./ui/select";
import { ArrowRight, ArrowUpDown, Trash2 } from "lucide-react";
import { Badge } from "./ui/badge";
import { unFormattedDateWithBrowserTimezone } from "@/utils/utils";
import { useSkills } from "@/context/SkillsContext";
import { useEffect, useState } from "react";
import { updateData } from "@/api/put";
import { AxiosError } from "axios";
import { withEntitlementCheck } from "../utils/withEntitlementCheck";
import DiscardModal from "./candidates/DiscardModal";
import { trackedFetch } from "@/library/trackApi";
import { getAppInsights } from "@/library/appInsights";

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const infoDetails: any = {
  "Soft Skills":
    " Personal attributes that enhance teamwork, communication, and problem-solving in the workplace.",
  "Technical Skills":
    "Specialized knowledge and expertise required to perform specific tasks or solve technical challenges.",
  "Tools And Platforms":
    "Software, frameworks, and technologies used to build, deploy, and manage projects.",
  "Degrees And Certifications":
    "Formal education and professional credentials that validate expertise in a field.",
  "Job Titles":
    "Designations that define roles, responsibilities, and hierarchy within an organization.",
};

type Attribute = {
  id: number;
  name: string;
  weight: string;
  subcategory_id: number;
  attribute_type_id: number;
  created_at: string;
  updated_at: string;
  is_approved: boolean;
};
interface Weight {
  id: number;
  subcategory_id: number;
  attribute_type_id: number;
  weight_level: string;
  weight_value: number;
}

type CategorizedAttributes = {
  [key: string]: Attribute[];
};

const SkillsEditor = () => {
  const [pendingTab, setPendingTab] = useState<string | null>(null);
  const [pendingCategory, setPendingCategory] = useState<any>(null);
  const [jobTitlesData, setJobTitlesData] = useState<CategorizedAttributes>({});
  const {
    skillsData,
    selectedTab,
    setSelectedTab,
    singleSkillData,
    setSingleSkillData,
    selectedCategory,
    setSelectedCategory,
    sendUpdatedData,
    setSendUpdatedData,
    trackCurrentData,
    setTrackCurrentData,
    loading,
    setLoading,
    loadingPost,
    setLoadingPost,
    loadingDelete,
    setLoadingDelete,
    isDeleteOpen,
    setDeleteOpen,
    isAllDeleteOpen,
    setAllDeleteOpen,
    selectedRows,
    setSelectedRows,
    weightsOfSubcategory,
    setWeightsOfSubcategory,
    sortColumn,
    setSortColumn,
    sortOrder,
    setSortOrder,
    isOpenDiscardModal,
    setIsOpenDiscardModal,
    updatedAction,
    setUpdatedAction,
    selecteApproveddRows,
    setSelectedApproveddRows,
    loadingApprovedStatus,
    setLoadingApprovedStatus,
    setIsHeaderTabChange,
  } = useSkills();
  const [originalSkillData, setOriginalSkillData] = useState<Attribute[]>(
    singleSkillData ?? []
  );
  const handleSort = (column: string) => {
    if (sortColumn === column) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortColumn(column);
      setSortOrder("asc");
    }
  };

  const { showNotification } = useNotification();

  function getWeightLevelsByAttributeType(
    data: Weight[],
    attributeTypeId: number
  ): string[] {
    return [
      ...new Set(
        data
          .filter((item) => item.attribute_type_id === attributeTypeId)
          .map((item) => item.weight_level)
      ),
    ];
  }

  const categorizeAttributes = (
    attributes: Attribute[]
  ): CategorizedAttributes => {
    const categories: CategorizedAttributes = {
      "Soft Skills": [],
      "Technical Skills": [],
      "Tools And Platforms": [],
      "Degrees And Certifications": [],
      "Job Titles": [],
    };

    attributes.forEach((attr) => {
      if (attr.attribute_type_id > 5) return;

      const categoryMap: { [key: number]: keyof CategorizedAttributes } = {
        1: "Soft Skills",
        2: "Technical Skills",
        3: "Tools And Platforms",
        4: "Degrees And Certifications",
        5: "Job Titles",
      };

      const category = categoryMap[attr.attribute_type_id];
      if (category) {
        categories[category].push(attr);
      }
    });

    return categories;
  };

  const selectedFormattedData = singleSkillData
    ? categorizeAttributes(singleSkillData)
    : {};

  const fetchSingleSkills = async (id: number) => {
    try {
      setLoading(true);
      const response = await trackedFetch(
        `/api/skills/${id}`,
        {},
        { context: "SkillsEditor - fetchSingleSkills" }
      );
      if (response.ok) {
        const data: { attributes: Attribute[] } = await response.json();
        setSingleSkillData(data?.attributes);
        setOriginalSkillData(data?.attributes);
        setJobTitlesData(categorizeAttributes(data?.attributes));
        setIsHeaderTabChange(false);
        getAppInsights()?.trackEvent({
          name: "FE_SkillsEditor_fetchSkills",
          properties: {
            subcategoryId: id,
            attributesCount: data?.attributes.length,
          },
        });
      } else {
        console.error("Failed to fetch attributes:", response.statusText);
      }
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      console.error("Error fetching skills:", error);
      showNotification(`Failed to fetch skills: ${error?.message}`, "error");
      getAppInsights()?.trackException({
        error: new Error(
          `SkillsEditor - fetchSingleSkills api with error : ${error?.message}`
        ),
        properties: { subcategoryId: id },
        severityLevel: 3,
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchWeightsForSubcategory = async (id: number) => {
    try {
      const response = await trackedFetch(
        `/api/skills/${id}/weights`,
        {},
        { context: "SkillsEditor - fetchWeightsForSubcategory" }
      );
      if (response.ok) {
        const data: { weights: Weight[] } = await response.json();
        setWeightsOfSubcategory(data.weights);
        setSelectedApproveddRows({});
        setSelectedRows(new Set());
        setUpdatedAction({});
        setSendUpdatedData({});
        setTrackCurrentData({});
        setIsHeaderTabChange(false);
        getAppInsights()?.trackEvent({
          name: "FE_SkillsEditor_fetchWeights",
          properties: {
            subcategoryId: id,
            weightsCount: data.weights.length,
          },
        });
      } else {
        console.error("Failed to fetch weights:", response.statusText);
      }
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      console.error("Error fetching weights:", error);
      showNotification(`Failed to fetch weights: ${error?.message}`, "error");
      getAppInsights()?.trackException({
        error: new Error(
          `SkillsEditor - fetchWeightsForSubcategory api with error : ${error?.message}`
        ),
        properties: { subcategoryId: id },
        severityLevel: 3,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCategoryChange = (val: string) => {
    const allApprovedRowsUndefined = Object.values(selecteApproveddRows).every(
      (val) => val === undefined
    );
    if (
      Object.keys(trackCurrentData).length > 0 ||
      Object.keys(updatedAction).length > 0 ||
      !allApprovedRowsUndefined
    ) {
      setIsOpenDiscardModal(true);
      setPendingCategory(val);
      return;
    }

    const data = JSON.parse(val);
    setIsHeaderTabChange(true);
    setSelectedCategory(data);
    fetchSingleSkills(data.id);
    fetchWeightsForSubcategory(data.id);
  };

  const handleUpdate = (attributeId: number, newValue: string) => {
    if (!selectedCategory) return;
    const updateSingleSkillData = singleSkillData?.map((item) => {
      if (item.id === attributeId) {
        setTrackCurrentData({
          ...trackCurrentData,
          [attributeId]: item.weight,
        });
        return { ...item, weight: newValue };
      }
      return item;
    });

    setSingleSkillData(updateSingleSkillData);
    setSendUpdatedData({ ...sendUpdatedData, [attributeId]: newValue });
  };

  useEffect(() => {
    // Check if all updated weights are the same as original weights
    const allWeightsSame = Object.entries(sendUpdatedData).every(
      ([id, newWeight]) => {
        const original = originalSkillData.find((s) => s.id === Number(id));
        return original && original.weight === newWeight;
      }
    );

    if (allWeightsSame && Object.keys(sendUpdatedData).length > 0) {
      setTrackCurrentData({});
    }
  }, [sendUpdatedData]);

  const handleActionUpdate = (attributeId: number, newValue: string) => {
    if (!selectedCategory) return;
    const selectedCategoryName = selectedCategory.name;
    if (Object.keys(updatedAction).length > 0) {
      if (updatedAction[attributeId]) {
        if (newValue.split("_")[0] === selectedCategoryName) {
          // Remove this value from updatedAction
          const { [attributeId]: _, ...rest } = updatedAction;
          setUpdatedAction(rest);
          return;
        }
      }
    }
    setUpdatedAction({ ...updatedAction, [attributeId]: newValue });
  };

  const saveUpdates = async () => {
    if (!selectedCategory) return;
    setLoadingPost(true);

    try {
      await trackedFetch(
        `/api/skills/${selectedCategory.id}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(sendUpdatedData),
        },
        { context: "SkillsEditor - saveUpdates" }
      );
      showNotification("Skills updated successfully", "success");
      setLoadingPost(false);
      setSendUpdatedData({});
      setTrackCurrentData({});
      fetchSingleSkills(selectedCategory.id);
      setIsHeaderTabChange(false);
      getAppInsights()?.trackEvent({
        name: "FE_SkillsEditor_saveUpdates",
        properties: {
          subcategoryId: selectedCategory.id,
          updatedCount: Object.keys(sendUpdatedData).length,
        },
      });
    } catch (error) {
      console.error("Error saving skills:", error);
      showNotification("Error saving skills:", "error");
      setLoadingPost(false);
      getAppInsights()?.trackException({
        error: new Error(
          `SkillsEditor - saveUpdates api with error : ${error}`
        ),
        properties: { subcategoryId: selectedCategory.id },
        severityLevel: 3,
      });
    }
  };

  const updateSubcategoryOfAttribute = async () => {
    if (!selectedCategory) return;
    setLoadingPost(true);

    const updateEntries = Object.entries(updatedAction) as [string, string][];

    const updatePromises = updateEntries.map(([id, value]) =>
      updateData(`/api/skills/${id}/subcategory`, {
        new_subcategory_id: value?.split("_")[1],
      })
        .then(() => ({ id, success: true }))
        .catch((error) => ({
          id,
          new_subcategory_id: value?.split("_")[1],
          success: false,
          error: error as AxiosError,
        }))
        .finally(() => {
          setIsHeaderTabChange(false);
          setIsOpenDiscardModal(false);
        })
    );

    const results = await Promise.allSettled(updatePromises);

    const failedUpdates: string[] = [];
    const successfulIds: string[] = [];

    results.forEach((res) => {
      if (res.status === "fulfilled") {
        const result = res.value;
        if (result.success) {
          successfulIds.push(result.id);
        } else {
          if (!result.success && "error" in result) {
            failedUpdates.push(result.id);
          }
        }
      } else {
        console.error("Unexpected error:", res.reason);
      }
    });

    // Remove successfully updated items
    setSingleSkillData((prev) =>
      prev?.filter((skill) => !successfulIds.includes(String(skill.id)))
    );

    // Show appropriate messages
    if (failedUpdates.length > 0) {
      showNotification(
        `${failedUpdates.length} job titles failed to update subcategory`,
        "error"
      );
    }

    if (successfulIds.length > 0) {
      showNotification(
        `${successfulIds.length} subcategories updated successfully`,
        "success"
      );
      setUpdatedAction({});
      setIsHeaderTabChange(false);
    }
    setLoadingPost(false);
  };

  const handleDeleteSkill = async (id: number) => {
    if (!selectedCategory) return;

    try {
      const response = await trackedFetch(
        `/api/jobs/${id}`,
        {
          method: "DELETE",
        },
        { context: "SkillsEditor - handleDeleteSkill" }
      );
      if (response.ok) {
        setSingleSkillData((prev) => prev?.filter((skill) => skill.id !== id));
        showNotification("Skill deleted successfully", "success");
        setIsHeaderTabChange(false);
        getAppInsights()?.trackEvent({
          name: "FE_SkillsEditor_deleteSkill",
          properties: {
            subcategoryId: selectedCategory.id,
            skillId: id,
          },
        });
      }
    } catch (error) {
      console.error("error: ", error);
      showNotification("Failed to delete skill", "error");
      getAppInsights()?.trackException({
        error: new Error(
          `SkillsEditor - handleDeleteSkill api with error : ${error}`
        ),
        properties: { subcategoryId: selectedCategory.id, skillId: id },
        severityLevel: 3,
      });
    }
  };

  useEffect(() => {
    // If selecteApproveddRows is empty or all values are undefined, set to false
    const hasAnyValue = Object.values(selecteApproveddRows).some(
      (val) => val !== undefined && val !== null && val !== ""
    );
    const hasAnyUpdatedAction = Object.values(updatedAction).length > 0;
    const hasSelectedRows = selectedRows.size > 0;
    const hasTrackCurrentData =
      selectedTab === "Job Titles"
        ? false
        : Object.values(trackCurrentData).length > 0;
    const updatedStatus =
      hasAnyValue ||
      hasAnyUpdatedAction ||
      hasSelectedRows ||
      hasTrackCurrentData;
    setIsHeaderTabChange(updatedStatus);
  }, [selecteApproveddRows, updatedAction, selectedRows, trackCurrentData]);

  const toggleRowSelection = (id: number) => {
    setSelectedRows((prev) => {
      const newSelection = new Set(prev);
      if (newSelection.has(id)) {
        newSelection.delete(id);
      } else {
        newSelection.add(id);
      }
      return newSelection;
    });
  };

  const handleDeleteSelected = async () => {
    if (selectedRows.size === 0) return;
    setLoadingDelete(true);

    const deletePromises = Array.from(selectedRows).map((id) =>
      trackedFetch(
        `/api/jobs/${id}`,
        { method: "DELETE" },
        { context: "SkillsEditor - handleDeleteSelected" }
      )
    );

    try {
      await Promise.all(deletePromises);
      setSingleSkillData((prev) =>
        prev?.filter((skill) => !selectedRows.has(skill.id))
      );
      showNotification("Selected skills deleted successfully", "success");
      setLoadingDelete(false);
      setSelectedRows(new Set());
      setIsHeaderTabChange(false);
      getAppInsights()?.trackEvent({
        name: "FE_SkillsEditor_deleteSelectedSkills",
        properties: {
          subcategoryId: selectedCategory?.id,
          deletedCount: selectedRows.size,
        },
      });
    } catch (error) {
      console.error("Error deleting selected skills", error);
      showNotification("Failed to delete selected skills", "error");
      setLoadingDelete(false);
      getAppInsights()?.trackException({
        error: new Error(
          `SkillsEditor - handleDeleteSelected api with error : ${error}`
        ),
        properties: { subcategoryId: selectedCategory?.id },
        severityLevel: 3,
      });
    }
  };

  const toggleApprovedRowSelection = (id: number, status: string) => {
    if (!selectedCategory) return;
    setSelectedApproveddRows({
      ...selecteApproveddRows,
      [id]: selecteApproveddRows[id] ? undefined : status,
    });
    singleSkillData?.map((item) => {
      if (item.id === id) {
        setTrackCurrentData({
          ...trackCurrentData,
          [id]: item.is_approved === true ? "Approved" : "Not Approved",
        });
      }
      return item;
    });
  };

  const updateApprovedStatus = async () => {
    if (!selectedCategory) return;
    setLoadingApprovedStatus(true);
    const statusPromises = Object.keys(selecteApproveddRows)
      .filter((id: string) => selecteApproveddRows[id])
      .map((id) =>
        updateData(`/api/skills/${id}/approval`, {
          is_approved: selecteApproveddRows[id] === "Approved",
        })
      );
    try {
      await Promise.all(statusPromises);
      setSingleSkillData((prev) =>
        prev?.map((skill) => {
          if (selecteApproveddRows[skill.id]) {
            return {
              ...skill,
              is_approved: selecteApproveddRows[skill.id] === "Approved",
            };
          }
          return skill;
        })
      );
      showNotification("Skill status updated successfully", "success");
      setLoadingApprovedStatus(false);
      setSelectedApproveddRows({});
      setIsHeaderTabChange(false);
    } catch (error) {
      console.error("Error while updating status", error);
      showNotification("Failed to updating status", "error");
      setLoadingPost(false);
    }
  };

  const handleTabChange = (val: string) => {
    if (
      (Object.keys(trackCurrentData).length && val !== selectedTab) ||
      Object.keys(selecteApproveddRows).length ||
      selectedRows.size ||
      Object.keys(updatedAction).length
    ) {
      setIsOpenDiscardModal(true);
      setIsHeaderTabChange(true);
      setPendingTab(val);
      return;
    } else {
      setSelectedTab(val);
    }
  };

  // When the user confirms discarding changes
  const confirmTabChange = () => {
    setSelectedRows(new Set());
    setSelectedApproveddRows({});
    setUpdatedAction({});
    setSendUpdatedData({});
    setTrackCurrentData({});
    if (Object.keys(trackCurrentData)?.length) {
      const updateSingleSkillData = singleSkillData?.map((item) => {
        if (trackCurrentData[item.id]) {
          return { ...item, weight: trackCurrentData[item.id] };
        }
        return item;
      });
      setIsHeaderTabChange(false);
      setSingleSkillData(updateSingleSkillData);
    }
    setIsOpenDiscardModal(false);
    if (pendingTab) {
      setSelectedTab(pendingTab);
      setPendingTab(null);
      setIsHeaderTabChange(false);
    }
    // If updatedAction is discarded, revert the subcategory changes for Job Titles
    if (updatedAction && Object.keys(updatedAction).length) {
      setIsHeaderTabChange(false);
      setIsOpenDiscardModal(false);
    }
    // Handle pending category change
    if (pendingCategory) {
      const data = JSON.parse(pendingCategory);
      setSelectedCategory(data);
      fetchSingleSkills(data.id);
      fetchWeightsForSubcategory(data.id);
      setPendingCategory(null);
    }
  };

  // If user cancels, just close modal
  const cancelTabChange = () => {
    setIsOpenDiscardModal(false);
    setPendingTab(null);
  };

  const sortedData = selectedFormattedData[selectedTab]
    ? [...selectedFormattedData[selectedTab]].sort((a, b) => {
        let valueA, valueB;
        if (
          (sortColumn === "name" || sortColumn === "weight") &&
          sortOrder === "asc"
        ) {
          return a[sortColumn].localeCompare(b[sortColumn]);
        } else if (
          (sortColumn === "name" || sortColumn === "weight") &&
          sortOrder === "desc"
        ) {
          return b[sortColumn].localeCompare(a[sortColumn]);
        }
        switch (sortColumn) {
          case "created_at":
            valueA = new Date(a.created_at).getTime();
            valueB = new Date(b.created_at).getTime();
            break;
          case "updated_at":
            valueA = new Date(a.updated_at).getTime();
            valueB = new Date(b.updated_at).getTime();
            break;
          default:
            return 0;
        }
        return sortOrder === "asc" ? valueA - valueB : valueB - valueA;
      })
    : [];

  if (loading) return <Loading />;

  const hasWeightChanged = () => {
    return Object.entries(sendUpdatedData).some(([id, newWeight]) => {
      const original = originalSkillData.find((s) => s.id === Number(id));
      return original && original.weight !== newWeight;
    });
  };

  const allRowsStatusChanged = () => {
    // Check if selecteApproveddRows has any keys
    const hasAnyApprovedRows = Object.keys(selecteApproveddRows)?.length > 0;
    if (hasAnyApprovedRows) {
      // Check if all values are undefined
      const allApprovedRowsUndefined = Object.values(
        selecteApproveddRows
      )?.every((val) => val === undefined);
      return !allApprovedRowsUndefined;
    }
  };
  return (
    <div className="w-full lg:w-[1260px] 2xl:w-[95%] mx-auto mt-5 p-5 pb-2 bg-white shadow-lg rounded-lg">
      <div className="relative">
        <h2 className="text-2xl font-bold mb-5 text-center capitalize flex items-center justify-center">
          Subcategory Library Editor
        </h2>
      </div>

      <div className="flex mb-6">
        <div className="flex-1">
          <label className="block text-lg font-medium mb-2">
            Select Subcategory:
          </label>
          <Select
            value={JSON.stringify(selectedCategory)}
            onValueChange={handleCategoryChange}
          >
            <SelectTrigger>
              <SelectValue placeholder="-- Select a Subcategory --" />
            </SelectTrigger>
            <SelectContent className="w-[90%] md:w-full ">
              <SelectGroup>
                {skillsData.map((category) => (
                  <SelectItem
                    key={category.id}
                    value={JSON.stringify(category)}
                    className="sm:truncate break-words"
                  >
                    <div className="flex sm:flex-wrap flex-col md:flex-row">
                      <p>{category.name}</p>
                      <p> &nbsp;-&nbsp;</p>
                      <p>({category.category_name})</p>
                    </div>
                  </SelectItem>
                ))}
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>
      </div>
      {loading ? (
        <Loading />
      ) : (
        singleSkillData && (
          <Tabs
            defaultValue={selectedTab}
            className=""
            onValueChange={handleTabChange}
            activationMode="manual"
          >
            <TabsList className="py-3 h-16 w-full justify-between pr-5">
              {Object.keys(selectedFormattedData)?.map((category) => (
                <TabsTrigger
                  data-state={category === selectedTab ? "active" : "inactive"}
                  className="block"
                  value={category}
                  key={category}
                >
                  {category.toUpperCase()}{" "}
                </TabsTrigger>
              ))}
            </TabsList>
            <TabsContent
              forceMount={true}
              className="w-full"
              value={selectedTab}
            >
              <p className="py-2 text-sm">{infoDetails[selectedTab]}</p>
              <div className="border rounded-lg overflow-auto">
                <div>
                  <Table className="w-full table-fixed">
                    <TableHeader className="table-header ">
                      <TableRow className="bg-gray-900 hover:bg-gray-900 cursor-pointer">
                        <TableHead
                          className="text-white"
                          onClick={() => handleSort("name")}
                        >
                          {getColumnName(selectedTab)}{" "}
                          <ArrowUpDown className="inline ml-1 h-4 w-4" />
                        </TableHead>
                        {selectedTab !== "Job Titles" && (
                          <TableHead
                            className="text-white w-[190px] sticky right-24"
                            onClick={() => handleSort("weight")}
                          >
                            Weight
                            <ArrowUpDown className="inline ml-1 h-4 w-4" />
                          </TableHead>
                        )}
                        {selectedTab === "Job Titles" && (
                          <TableHead className="text-white cursor-pointer">
                            <Button
                              disabled={
                                !Object.keys(updatedAction)?.length ||
                                isOpenDiscardModal
                              }
                              onClick={updateSubcategoryOfAttribute}
                              className="rounded-3xl text-[12px]"
                              style={{
                                padding: "0.25rem .75rem",
                                borderRadius: "30px",
                                ...(Object.keys(updatedAction)?.length &&
                                !isOpenDiscardModal
                                  ? { background: "#3B81F6", color: "#fff" }
                                  : {}),
                              }}
                              loading={loadingPost}
                            >
                              Update Subcategory
                            </Button>
                          </TableHead>
                        )}
                        {selectedTab !== "Job Titles" && (
                          <TableHead
                            className="text-white cursor-pointer w-[220px] sticky right-24"
                            onClick={() => handleSort("updated_at")}
                          >
                            Weight Updated{" "}
                            <ArrowUpDown className="inline ml-1 h-4 w-4" />
                          </TableHead>
                        )}
                        {selectedTab !== "Job Titles" && (
                          <TableHead className="px-4 py-3 w-[150px] sticky right-16 bg-gray-900">
                            <Button
                              disabled={
                                !hasWeightChanged() || isOpenDiscardModal
                              }
                              onClick={saveUpdates}
                              className="rounded-3xl text-[12px]"
                              style={{
                                padding: "0.25rem .75rem",
                                borderRadius: "30px",
                                ...(hasWeightChanged() && !isOpenDiscardModal
                                  ? { background: "#3B81F6", color: "#fff" }
                                  : {}),
                              }}
                              loading={loadingPost}
                            >
                              Save All
                            </Button>
                          </TableHead>
                        )}

                        {selectedTab === "Job Titles" && (
                          <TableHead className="text-white text-left px-4 py-3 w-[280px] sticky right-4 bg-gray-900">
                            {allRowsStatusChanged() ? (
                              <Button
                                disabled={
                                  !Object.keys(selecteApproveddRows).length
                                }
                                onClick={updateApprovedStatus}
                                className="rounded-3xl text-[12px]"
                                style={{
                                  padding: "0.25rem .75rem",
                                  // margin: "0 auto",
                                  borderRadius: "30px",
                                  ...(Object.keys(selecteApproveddRows).length
                                    ? { background: "#3B81F6", color: "#fff" }
                                    : {}),
                                }}
                                loading={loadingApprovedStatus}
                              >
                                Save All Status
                              </Button>
                            ) : (
                              <p className="px-3">Status</p>
                            )}
                          </TableHead>
                        )}
                        {/* <TableHead className="px-4 py-3 w-[120px] sticky right-5 bg-gray-900"></TableHead> */}
                        <TableHead className="px-4 py-3 w-[130px] sticky right-0 bg-gray-900">
                          <Button
                            onClick={() => setAllDeleteOpen(true)}
                            disabled={!selectedRows.size}
                            className="rounded-3xl text-[12px]"
                            style={{
                              padding: "0.25rem .75rem",
                              borderRadius: "10px",
                              ...(selectedRows.size
                                ? { background: "#e95151", color: "#fff" }
                                : {}),
                            }}
                            loading={loadingDelete}
                          >
                            <Trash2 />
                          </Button>
                        </TableHead>
                      </TableRow>
                    </TableHeader>
                  </Table>
                  <div className="max-h-[44vh] overflow-auto">
                    <Table className="w-full table-fixed">
                      <TableBody>
                        {sortedData?.map((skill, ind) => (
                          <TableRow
                            key={skill.id}
                            className={`${ind % 2 === 1 ? "bg-gray-50" : ""}`}
                          >
                            <TableCell>
                              <span className="capitalize">{skill.name}</span>
                            </TableCell>
                            {selectedTab !== "Job Titles" && (
                              <TableCell className="w-[190px] sticky right-24 pl-0">
                                <Select
                                  value={skill.weight}
                                  onValueChange={(val: string) =>
                                    handleUpdate(skill.id, val)
                                  }
                                >
                                  <SelectTrigger className="w-24 shadow-none border-none py-0 h-4 mb-1 outline-none focus:ring-0 focus:outline-none">
                                    <SelectValue placeholder="Select a weight" />
                                  </SelectTrigger>
                                  <SelectContent className="outline-none">
                                    <SelectGroup>
                                      {getWeightLevelsByAttributeType(
                                        weightsOfSubcategory,
                                        skill.attribute_type_id
                                      ).map((op) => (
                                        <SelectItem
                                          key={op.toLowerCase()}
                                          value={op.toLowerCase()}
                                        >
                                          {op}
                                        </SelectItem>
                                      ))}
                                    </SelectGroup>
                                  </SelectContent>
                                </Select>
                                {originalSkillData.find(
                                  (s) => s.id === skill.id
                                )?.weight !== skill.weight && (
                                  <div className="flex items-center">
                                    <Badge
                                      variant="outline"
                                      className="bg-[#f49f9f]"
                                    >
                                      {
                                        originalSkillData.find(
                                          (s) => s.id === skill.id
                                        )?.weight
                                      }
                                    </Badge>
                                    <ArrowRight size={16} />
                                    <Badge
                                      variant="outline"
                                      className="bg-[#9ff4ba]"
                                    >
                                      {skill.weight}
                                    </Badge>
                                  </div>
                                )}
                              </TableCell>
                            )}
                            {selectedTab === "Job Titles" && (
                              <TableCell className="">
                                <Select
                                  value={
                                    updatedAction[skill.id]
                                      ? updatedAction[skill.id]
                                      : `${
                                          skill.subcategory_id &&
                                          skillsData.find(
                                            (cat) =>
                                              cat.id === skill.subcategory_id
                                          )?.name
                                        }_${skill.subcategory_id}`
                                  }
                                  onValueChange={(val: string) =>
                                    handleActionUpdate(skill.id, val)
                                  }
                                >
                                  <SelectTrigger className="w-44 shadow-none border-none py-0 h-4 mb-1 outline-none focus:ring-0 focus:outline-none">
                                    <SelectValue placeholder="Select Subcategory" />
                                  </SelectTrigger>
                                  <SelectContent className="outline-none">
                                    <SelectGroup>
                                      {skillsData.map((category) => (
                                        <SelectItem
                                          key={category.id}
                                          value={`${category.name}_${category.id}`}
                                        >
                                          {category.name}
                                        </SelectItem>
                                      ))}
                                    </SelectGroup>
                                  </SelectContent>
                                </Select>
                                {updatedAction[skill.id] &&
                                  updatedAction[skill.id]?.split("_")[0] !==
                                    selectedCategory?.name && (
                                    <div className="flex items-center">
                                      <Badge
                                        variant="outline"
                                        className="bg-[#f49f9f]"
                                      >
                                        {selectedCategory?.name}
                                      </Badge>
                                      <ArrowRight size={16} />
                                      <Badge
                                        variant="outline"
                                        className="bg-[#9ff4ba] ml-2"
                                      >
                                        {updatedAction[skill.id]?.split("_")[0]}
                                      </Badge>
                                    </div>
                                  )}
                              </TableCell>
                            )}
                            {selectedTab !== "Job Titles" && (
                              <TableCell className="w-[220px] sticky right-24">
                                <span className="capitalize">
                                  {unFormattedDateWithBrowserTimezone(
                                    skill.updated_at
                                  )}{" "}
                                  (PST)
                                </span>
                              </TableCell>
                            )}
                            {selectedTab !== "Job Titles" && (
                              <TableCell className="text-center pl-6 w-[150px] sticky right-24">
                                <Button
                                  className={`py-0 px-3 rounded-3xl text-[12px] ${
                                    true
                                      ? "bg-[#246438] hover:bg-[#246438]"
                                      : ""
                                  }`}
                                  onClick={saveUpdates}
                                  disabled={
                                    originalSkillData.find(
                                      (s) => s.id === skill.id
                                    )?.weight === skill.weight
                                  }
                                  style={{
                                    border: "1px solid #246438",
                                    padding: "0.25rem .75rem",
                                    borderRadius: "30px",
                                    ...(originalSkillData.find(
                                      (s) => s.id === skill.id
                                    )?.weight !== skill.weight
                                      ? { background: "#3B81F6", color: "#fff" }
                                      : {}),
                                  }}
                                >
                                  Save
                                </Button>
                              </TableCell>
                            )}
                            {selectedTab === "Job Titles" && (
                              <TableCell className="pl-2 w-[280px]">
                                {/* <div className="flex items-center "> */}
                                <Select
                                  value={
                                    selecteApproveddRows[skill.id] !== undefined
                                      ? selecteApproveddRows[skill.id]
                                      : skill.is_approved
                                      ? "Approved"
                                      : "Not Approved"
                                  }
                                  onValueChange={(val: string) => {
                                    toggleApprovedRowSelection(skill.id, val);
                                  }}
                                >
                                  <SelectTrigger className="w-[8.3rem] shadow-none border-none py-0 h-4 mb-1 outline-none focus:ring-0 focus:outline-none">
                                    <SelectValue placeholder="Select Subcategory" />
                                  </SelectTrigger>
                                  <SelectContent className="outline-none">
                                    <SelectGroup>
                                      <SelectItem value={"Approved"}>
                                        Approved
                                      </SelectItem>
                                      <SelectItem value={"Not Approved"}>
                                        Not Approved
                                      </SelectItem>
                                    </SelectGroup>
                                  </SelectContent>
                                </Select>
                                {selecteApproveddRows[skill.id] && (
                                  <div className="flex items-center">
                                    <Badge
                                      variant="outline"
                                      className="bg-[#f49f9f]"
                                    >
                                      {trackCurrentData[skill.id]}
                                    </Badge>
                                    <ArrowRight size={16} />
                                    <Badge
                                      variant="outline"
                                      className="bg-[#9ff4ba] ml-2"
                                    >
                                      {selecteApproveddRows[skill.id]}
                                    </Badge>
                                  </div>
                                )}
                                {/* </div> */}
                              </TableCell>
                            )}
                            <TableCell className="text-center pl-6 w-[130px] sticky right-5">
                              <input
                                type="checkbox"
                                checked={selectedRows.has(skill.id)}
                                onChange={() => toggleRowSelection(skill.id)}
                                className="accent-gray-800 cursor-pointer"
                              />
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        )
      )}
      {isOpenDiscardModal && (
        <DiscardModal
          isOpenDiscardModal={isOpenDiscardModal}
          cancelTabChange={cancelTabChange}
          confirmTabChange={confirmTabChange}
          loadingPost={loadingPost}
        />
      )}

      <Modal isOpen={Boolean(isDeleteOpen)} onClose={() => setDeleteOpen("")}>
        <p className="mb-5"> Are you sure, you want to delete?</p>
        <div className="flex justify-end">
          <Button
            onClick={() => setDeleteOpen("")}
            className=""
            style={{
              color: "#e95151",
              border: "1px solid #e95151",
              background: "#fff",
            }}
          >
            Cancel
          </Button>
          <Button
            onClick={() => {
              const [category, id] = isDeleteOpen.split("-");
              handleDeleteSkill(parseInt(id));
              setDeleteOpen("");
              getAppInsights()?.trackEvent({
                name: "FE_SkillsEditor_deleteSkill",
                properties: {
                  subcategoryId: category,
                  skillId: id,
                },
              });
            }}
            style={{ background: "#e95151" }}
            className="bg-[#cc4949] hover:bg-[#e95151]"
          >
            Delete
          </Button>
        </div>
      </Modal>
      <Modal isOpen={isAllDeleteOpen} onClose={() => setAllDeleteOpen(false)}>
        <p className="mb-5">
          {" "}
          Are you sure, you want to delete all selected rows?
        </p>
        <div className="flex justify-end">
          <Button
            onClick={() => setAllDeleteOpen(false)}
            className=""
            style={{
              color: "#e95151",
              border: "1px solid #e95151",
              background: "#fff",
            }}
          >
            Cancel
          </Button>
          <Button
            onClick={() => {
              handleDeleteSelected();
              setAllDeleteOpen(false);
              getAppInsights()?.trackEvent({
                name: "FE_SkillsEditor_deleteSelectedSkills",
                properties: {
                  subcategoryId: selectedCategory?.id,
                  deletedCount: selectedRows.size,
                },
              });
            }}
            style={{ background: "#e95151" }}
            className="bg-[#cc4949] hover:bg-[#e95151]"
          >
            Delete Selected Rows
          </Button>
        </div>
      </Modal>
    </div>
  );
};

export default withEntitlementCheck(SkillsEditor, "Sub_Catregory");
