"use client";

import { useEffect } from "react";
import { usePathname } from "next/navigation";
import { initAppInsights, getAppInsights } from "@/library/appInsights";
import { getOrCreateUserUuid } from "@/library/utils";
import { useSession } from "next-auth/react";

const AppInsightsClient = () => {
  const pathname = usePathname();
  const { data: session } = useSession();

  // 1. Initialize App Insights once
  useEffect(() => {
    initAppInsights();

    window.onerror = (msg, src, line, col, err) => {
      getAppInsights()?.trackException({
        error: err || new Error(String(msg)),
      });
    };
  }, []);

  // 2. Register telemetry initializer once, but always use latest session/uuid
  useEffect(() => {
    const uuid = getOrCreateUserUuid();
    const ai = getAppInsights();
    if (!ai) return;

    // Remove previous initializers if needed (optional, depends on SDK)
    ai.addTelemetryInitializer((envelope) => {
      const baseData: any = envelope.data?.baseData ?? {};
      baseData.properties = {
        ...(baseData.properties || {}),
        userUuid: uuid,
        email: session?.user?.email || "",
      };
      envelope.data!.baseData = baseData;
    });

    // Set authenticated user context
    ai.setAuthenticatedUserContext(uuid);
  }, [session]);

  // 3. Track page views
  useEffect(() => {
    getAppInsights()?.trackPageView({ uri: pathname });
  }, [pathname]);

  return null;
};

export default AppInsightsClient;
