{"name": "recruiter-tr-ui", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3000 --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest --config jest.config.ts", "test:watch": "jest --config jest.config.ts --watch", "test:coverage": "jest --config jest.config.ts --coverage"}, "dependencies": {"@microsoft/applicationinsights-web": "^3.3.8", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-tooltip": "^1.1.8", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.4.10", "lucide-react": "^0.475.0", "moment": "^2.30.1", "moment-timezone": "^0.5.47", "next": "15.1.7", "next-auth": "^4.24.11", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^1.1.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@testing-library/jest-dom": "^6", "@testing-library/react": "^14", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "babel-jest": "^30.0.0-beta.3", "eslint": "^9", "eslint-config-next": "15.1.7", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.0-beta.3", "postcss": "^8", "tailwindcss": "^3.4.1", "ts-jest": "^29.3.4", "ts-node": "^10.9.2", "typescript": "^5"}}