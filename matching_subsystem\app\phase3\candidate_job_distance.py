import json
import os
from pathlib import Path
from typing import List, Dict, Optional
from openai import OpenAI
import tiktoken # Added import
import logging # Added import for logger type hint

# Add missing imports for the test function and other dependencies
from common.secrets_env import load_secrets_env_variables
from common.db.global_dbconnector import GlobalDBConnector, PostgresEnvironment
from matching_subsystem.app.config_loader import ConfigManager
from matching_subsystem.app.utils.appLogger2 import AppLogger2
from matching_subsystem.app.vacancy_loader import load_vacancies_from_db

MODEL     = "o3"                       # or gpt-4o
MAX_COMPLETION_TOKENS = 100000
MAX_PROMPT_TOKENS_PER_API_CALL = 100000 # Max tokens for a single prompt to OpenAI

def build_prompt(job_city: str, job_state: str, candidates):
    """
    Build a single prompt that asks GPT to return JSON with distances.
    Each candidate dict must contain: candidate_id, city, state.
    """
    header = (
        f"You are an assistant that returns *only* JSON.\n"
        f"For each candidate below, estimate a reasonable straight-line "
        f"distance in miles from the job location {job_city}, {job_state}.\n"
        f"Return a JSON array where each element has:\n"
        f"  • candidate_id\n  • city\n  • state\n  • distance_from_job_location\n\n"
        f"Candidates:\n"
    )

    body_lines = [
        f"- {{candidate_id: {c['candidate_id']}, city: {c['city']}, state: {c['state']}}}"
        for c in candidates
    ]

    footer = "\n\nRespond ONLY with the JSON array."
    return header + "\n".join(body_lines) + footer

def _call_openai_api_for_chunk(
    prompt_text: str,
    client: OpenAI,
    model: str,
    max_completion_tokens: int,
    logger: Optional[logging.Logger] = None
) -> List[Dict[str, str]]:
    """Helper function to make a single OpenAI API call and parse the JSON response."""
    try:
        response = client.chat.completions.create(
            model=model,
            messages=[{"role": "user", "content": prompt_text}],
            max_completion_tokens=max_completion_tokens
        )
        content = response.choices[0].message.content
        if not content:
            if logger:
                logger.error(f"OpenAI returned no content for a chunk. Full response: {response}")
            return []
        
        return json.loads(content)
    except json.JSONDecodeError as e:
        if logger:
            # Avoid logging potentially very large 'content' directly if it's not JSON
            logged_content = content[:500] + "..." if content and len(content) > 500 else content
            logger.error(f"JSON decode error from OpenAI for a chunk: {e}. Content (truncated): {logged_content}")
        return []
    except Exception as e:
        if logger:
            logger.error(f"OpenAI API call failed for a chunk: {e}", exc_info=True)
        return []

def compute_distances(
    job_city: str,
    job_state: str,
    candidates: List[Dict[str, str]],
    client: OpenAI,
    model: str = MODEL,
    logger: Optional[logging.Logger] = None, # Added logger parameter
) -> List[Dict[str, str]]:
    """
    Calls OpenAI model (e.g., o3, gpt-4o) and returns LIST of dicts with distances.
    Chunks requests if the prompt token count exceeds MAX_PROMPT_TOKENS_PER_API_CALL.
    """
    if not candidates:
        return []

    try:
        # Using "cl100k_base" as it's common for gpt-4, gpt-3.5-turbo models
        # If MODEL can be very different, this might need adjustment or error handling
        encoding = tiktoken.get_encoding("cl100k_base")
    except Exception as e:
        if logger:
            logger.error(f"Failed to get tiktoken encoding: {e}. Proceeding without token checking for chunks.", exc_info=True)
        # Fallback: make a single call if tiktoken fails (original behavior for one chunk)
        prompt = build_prompt(job_city, job_state, candidates)
        return _call_openai_api_for_chunk(prompt, client, model, MAX_COMPLETION_TOKENS, logger)

    all_api_results: List[Dict[str, str]] = []
    current_candidates_batch: List[Dict[str, str]] = []

    for candidate_data in candidates:
        trial_batch = current_candidates_batch + [candidate_data]
        prompt_for_trial_batch = build_prompt(job_city, job_state, trial_batch)
        
        try:
            num_tokens = len(encoding.encode(prompt_for_trial_batch))
        except Exception as e:
            if logger:
                logger.error(f"Failed to encode prompt with tiktoken: {e}. Cannot perform chunking by token count.", exc_info=True)
            # If encoding fails mid-process, it's hard to recover chunking.
            # Option: process remaining as one large chunk, or stop.
            # For now, let's log and attempt to process the current_candidates_batch + remaining as one.
            # This part would need more robust error strategy if tiktoken encoding is unreliable.
            # For simplicity here, we'll assume if initial encoding worked, subsequent ones will too.
            # A more robust solution might involve a flag to stop using tiktoken if it errors.
            # Sticking to the main logic path assuming encoding works:
            pass # num_tokens will be used below

        if num_tokens > MAX_PROMPT_TOKENS_PER_API_CALL:
            if current_candidates_batch:  # If there's a batch to send
                prompt_to_send = build_prompt(job_city, job_state, current_candidates_batch)
                if logger:
                    logger.info(f"Sending chunk of {len(current_candidates_batch)} candidates. Prompt tokens (estimate): {len(encoding.encode(prompt_to_send))}")
                
                chunk_api_result = _call_openai_api_for_chunk(prompt_to_send, client, model, MAX_COMPLETION_TOKENS, logger)
                all_api_results.extend(chunk_api_result)
                
                current_candidates_batch = [candidate_data] # Start new batch with current candidate
                
                # Check if this single new candidate itself is too large
                prompt_for_single_new_candidate = build_prompt(job_city, job_state, current_candidates_batch)
                if len(encoding.encode(prompt_for_single_new_candidate)) > MAX_PROMPT_TOKENS_PER_API_CALL:
                    if logger:
                        logger.warning(
                            f"Candidate {candidate_data.get('candidate_id')} alone makes prompt too large "
                            f"({len(encoding.encode(prompt_for_single_new_candidate))} tokens). Skipping this candidate."
                        )
                    current_candidates_batch = [] # Clear batch, effectively skipping
            else:
                # Current candidate alone makes the prompt too large
                if logger:
                    logger.warning(
                        f"Candidate {candidate_data.get('candidate_id')} alone makes prompt too large "
                        f"({num_tokens} tokens). Skipping this candidate."
                    )
                # current_candidates_batch is already empty, so this candidate is skipped.
        else:
            # Candidate fits, add to current batch
            current_candidates_batch = trial_batch

    # Process any remaining candidates in the last batch
    if current_candidates_batch:
        prompt_to_send = build_prompt(job_city, job_state, current_candidates_batch)
        if logger:
            logger.info(f"Sending final chunk of {len(current_candidates_batch)} candidates. Prompt tokens (estimate): {len(encoding.encode(prompt_to_send))}")
        
        chunk_api_result = _call_openai_api_for_chunk(prompt_to_send, client, model, MAX_COMPLETION_TOKENS, logger)
        all_api_results.extend(chunk_api_result)

    return all_api_results

def prune_by_max_distance(
    ranked_list: list[dict],
    distances: list[dict],
    max_distance: float,
) -> list[dict]:
    """
    Keeps only candidates whose distance_from_job_location ≤ max_distance,
    attaches the distance if present, removes if missing.
    """
    # 1️⃣ Make a safe lookup
    dist_map = {
        d.get("candidate_id"): d.get("distance_from_job_location")
        for d in distances
        if d.get("candidate_id") is not None
    }

    pruned = []
    for cand in ranked_list:
        cid = cand.get("candidate_id") or cand.get("contact_id")
        dist = dist_map.get(cid, None)

        # 2️⃣ If missing or None, drop it
        if dist is None:
            continue

        # 3️⃣ If distance too big, drop it
        if dist > max_distance:
            continue

        # 4️⃣ If valid, attach and keep
        cand["distance_from_job_location"] = dist
        pruned.append(cand)

    ranked_list = pruned
    return ranked_list

def fill_candidate_location(
    ranked_list: list[dict],
    *,
    db_connector,
    logger
) -> list[dict]:
    """
    Adds 'city' and 'state' (if present in resume_data) to each candidate dict.
    Expects every dict to contain either 'candidate_id' or 'contact_id'.

    Parameters
    ----------
    candidates   : wrapper-dict that contains key "ranked_candidates"
    db_connector : PostgresConnectorV2 instance (already configured / connected)
    logger       : AppLogger

    Returns the same wrapper-dict with city/state keys added.
    """
    conn = db_connector.connect()
    cur  = conn.cursor()

    sql = """
        SELECT
            contact_id::text,
            resume_data->>'city'  AS city,
            resume_data->>'state' AS state
        FROM tandymdpprod.candidates
        WHERE contact_id = %s;
    """

    for cand in ranked_list:
        cid = cand.get("candidate_id") or cand.get("contact_id")
        if not cid:
            logger.warning("Skipping candidate without ID: %s", cand)
            cand["city"] = cand["state"] = ""
            continue

        cur.execute(sql, (cid,))
        row = cur.fetchone()

        cand["city"]  = row[1] if row and row[1] else ""
        cand["state"] = row[2] if row and row[2] else ""

    cur.close()
    conn.close()
    return ranked_list

def filter_candidates_by_distance(
    vacancy_data: dict,
    ranked_list: list[dict],
    logger,
    db_connector,
    max_distance_miles: int = 30
) -> list[dict]:
    # Validate 'job location' structure before using it
    job_template = vacancy_data.get("job_template", {})
    job_location = job_template.get("job_location")
    if not job_location or not isinstance(job_location, list) or not job_location[0]:
        logger.error("Missing or invalid 'job location' in vacancy data — Not filtering by distance.")
        return ranked_list

    job_city = job_location[0].get("city")
    job_state = job_location[0].get("state")

    if not job_city or not job_state:
        logger.error("Missing city or state in job location — Not filtering by distance.")
        return ranked_list

    # Step 1: Fill missing city/state via Dataverse and update ranked_list
    #No longer needed.

    # Step 2: Prepare candidates with valid city/state
    candidates_with_location = [c for c in ranked_list if c.get("city") and c.get("state")]
    if len(candidates_with_location) < len(ranked_list):
        logger.warning(f"{len(ranked_list) - len(candidates_with_location)} candidates still missing city/state. Skipping them for distance.")

    if not candidates_with_location:
        logger.info("No candidates with valid location data. Skipping distance filter.")
        return ranked_list

    # Step 3: Compute distances using OpenAI
    openai_api_key = os.getenv("OPENAI_API_KEY")
    if not openai_api_key:
        logger.error("OPENAI_API_KEY missing from environment. Cannot compute distances.")
        return ranked_list

    try:
        client = OpenAI(api_key=openai_api_key)

        result = compute_distances(
            job_city,
            job_state,
            candidates_with_location, # Use the list that's guaranteed to have city/state
            client=client, # Fixed: use the client variable instead of undefined openai_client_for_distances
            model=MODEL, # Explicitly pass model or let compute_distances use its default
            logger=logger # Pass the logger
        )

        # Step 4: Apply distance filtering
        ranked_list_after_pruning = prune_by_max_distance(
            candidates_with_location, result, max_distance=float(max_distance_miles)
        )
        
        return ranked_list_after_pruning

    except Exception as e:
        logger.error(f"Distance computation failed: {e}", exc_info=True)
        return ranked_list

def test():
    load_secrets_env_variables()
    ref_nos = "CR/506112"
    temp_logger = logging.getLogger("bootstrap_logger")
    db_connector = GlobalDBConnector.get_connector(PostgresEnvironment.PROD, temp_logger)
    connection = db_connector.connect()

    try:
        config_manager = ConfigManager(master_config_path="matching_subsystem/configs/master_config.json", logger=temp_logger)
        master_config_loaded = config_manager.get_master_config()
    except Exception as e:
        temp_logger.error(f"Failed to initialize ConfigManager: {e}", exc_info=True)
        exit(1)

    # Initialize AppLogger2 with the loaded config and run_tag
    log_conf = master_config_loaded.get("logging_config")
    if not log_conf:
        temp_logger.error("logging_config not found in master_config.json. Exiting.")
        exit(1)

    # Ensure the logger name from config is used, or provide a default
    if "name" not in log_conf:
        log_conf["name"] = "matching_engine_default" # Default name if not in config

    logger = AppLogger2(config=log_conf, run_tag="test")
    logger.info(f"AppLogger2 initialized for run_tag: Test. Logging to configured files and/or stdout.")
    
    vacancy_json_list = load_vacancies_from_db(
        refnos_csv=ref_nos, 
        db_connector_info=db_connector, # This is a PostgresConnector instance
        master_config=master_config_loaded, 
        logger= logger
    )
    job_city = vacancy_json_list[0][0]['job location'][0]['city']
    job_state = vacancy_json_list[0][0]['job location'][0]['state']
    print(f"City: {job_city}, State: {job_state}")

    # ------------ 1. PARAMETERS -------------------------------------------------
    CANDIDATE_FILE = "output_results/CR_506112_top_100_shortlist.json"
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
    openai_client = OpenAI(api_key=OPENAI_API_KEY)

    # ----------------------------------------------------------------------------
    # 1) load wrapper-dict and add city/state --------------------
    data = json.loads(Path(CANDIDATE_FILE).read_text("utf-8"))
    candidate_list = fill_candidate_location(data["ranked_candidates"], db_connector=db_connector, logger=logger)

    # 2) get distances -------------------------------------------
    result = compute_distances(
        job_city,
        job_state,
        candidate_list,                 # ← pass the list, not the wrapper dict
        client=openai_client,
        logger=logger # Pass the logger
    )

    MAX_DISTANCE = 30  # miles
    data = prune_by_max_distance(data, result, max_distance=MAX_DISTANCE)

    print(json.dumps(data["ranked_candidates"], indent=2))

if __name__ == "__main__":
    test()
