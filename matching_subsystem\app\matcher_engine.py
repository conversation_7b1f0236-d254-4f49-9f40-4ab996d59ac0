import os
import json
from typing import Dict, Any, Optional, List
from openai import OpenAI
import psycopg2 # For type hinting and error handling

from .utils.appLogger2 import AppLogger2
from common.db.postgres_connector import PostgresConnector
from pgvector.psycopg2 import register_vector

from .config_loader import ConfigManager, DuplicateSubcategorySpecError
from .embedding_utils import generate_vacancy_embeddings
from .phase1 import prefilter as phase1_prefilter # Updated import
from .phase2.orchestrator import calculate_all_semantic_scores
from .phase3 import ranking as phase3_ranking # Updated import
from .db import inserter as score_inserter 
from .db import operations as db_operations 
from .utils.versioning import generate_config_hash
from .phase3.candidate_job_distance import filter_candidates_by_distance

class MatcherEngineError(Exception):
    """Base exception for errors in the MatcherEngine."""
    pass

class VacancyProcessingError(MatcherEngineError):
    """Exception raised for errors during the processing of a single vacancy."""
    pass


class MatcherEngine:
    def __init__(
        self,
        config_manager: ConfigManager,
        db_connectors_map: Dict[str, PostgresConnector],
        openai_client: OpenAI,
        logger: AppLogger2,
        run_tag: str # New parameter
    ):
        if not isinstance(config_manager, ConfigManager):
            raise TypeError("config_manager must be an instance of ConfigManager.")
        if not isinstance(db_connectors_map, dict) or not all(isinstance(c, PostgresConnector) for c in db_connectors_map.values()):
            raise TypeError("db_connectors_map must be a dict of PostgresConnector instances.")
        if not isinstance(openai_client, OpenAI):
            raise TypeError("openai_client must be an instance of openai.OpenAI.")
        if not hasattr(logger, 'info'): # Basic check for logger compatibility
            raise TypeError("logger does not appear to be a valid logger instance.")
        if not isinstance(run_tag, str) or not run_tag.strip():
            raise ValueError("run_tag must be a non-empty string.")

        self.config_manager = config_manager
        self.db_connectors_map = db_connectors_map
        self.openai_client = openai_client
        self.logger = logger
        self.run_tag = run_tag # Store the run_tag
        self.logger.info(f"MatcherEngine initialized successfully for run_tag: '{self.run_tag}'.")

    def _save_results_to_file(self, data_to_save: Dict, vacancy_refno: str, base_output_folder: str, top_n_for_shortlist: Optional[int] = None):
        """
        Saves matching results to a JSON file.
        If top_n_for_shortlist is provided, saves a separate shortlist file.
        """
        if not isinstance(data_to_save, dict) or not vacancy_refno or not base_output_folder:
            self.logger.error("Invalid arguments for _save_results_to_file. Aborting save.")
            return

        try:
            os.makedirs(base_output_folder, exist_ok=True)
        except OSError as e:
            self.logger.error(f"Could not create output directory '{base_output_folder}': {e}", exc_info=True)
            return # Cannot save if directory creation fails

        safe_refno = "".join(c if c.isalnum() or c in ('_', '-') else '_' for c in vacancy_refno)
        
        # Save full results
        full_results_filename = os.path.join(base_output_folder, f"{safe_refno}_all_ranked_candidates.json")
        try:
            with open(full_results_filename, 'w') as f:
                json.dump(data_to_save, f, indent=4)
            self.logger.info(f"Full match results for '{vacancy_refno}' saved to {full_results_filename}")
        except IOError as e:
            self.logger.error(f"Failed to write full output file '{full_results_filename}': {e}", exc_info=True)

        # Save shortlist if top_n is specified
        if top_n_for_shortlist is not None and top_n_for_shortlist > 0:
            shortlist_data = {k: v for k, v in data_to_save.items() if k != "ranked_candidates"} # Copy metadata
            shortlist_data["ranked_candidates"] = data_to_save.get("ranked_candidates", [])[:top_n_for_shortlist]
            
            shortlist_filename = os.path.join(base_output_folder, f"{safe_refno}_top_{top_n_for_shortlist}_shortlist.json")
            try:
                with open(shortlist_filename, 'w') as f:
                    json.dump(shortlist_data, f, indent=4)
                self.logger.info(f"Shortlist (Top {top_n_for_shortlist}) for '{vacancy_refno}' saved to {shortlist_filename}")
            except IOError as e:
                self.logger.error(f"Failed to write shortlist file '{shortlist_filename}': {e}", exc_info=True)

    def _get_db_connection(self, connector_key: str) -> Any:
        """Gets a database connection from the specified connector pool."""
        if connector_key not in self.db_connectors_map:
            self.logger.error(f"Database connector key '{connector_key}' not found in db_connectors_map.")
            raise MatcherEngineError(f"Missing DB connector: {connector_key}")
        try:
            connection = self.db_connectors_map[connector_key].connect()
            self.logger.debug(f"Successfully obtained DB connection for '{connector_key}'.")
            return connection
        except Exception as e:
            self.logger.error(f"Failed to connect to database using connector '{connector_key}': {e}", exc_info=True)
            raise MatcherEngineError(f"DB connection failure for {connector_key}")

    def _get_db_connector(self, connector_key: str) -> Any:
        """Gets a database connector from the specified connector pool."""
        return self.db_connectors_map[connector_key]

    def _get_vacancy_overrides(self, vacancy_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Gets the vacancy overrides from the vacancy data.
        """
        vacancy_overrides = {}
        vacancy_template = vacancy_data.get("job_template", {})
        recency_of_must_have_skills = vacancy_template.get("recency_of_must_have_skills", [])
        recency_indices = [0,1]
        if recency_of_must_have_skills.lower().strip() == "recent":
            recency_indices = [0]
        vacancy_overrides["recency_indices_for_match"] = recency_indices
        
        vacancy_overrides["job_location"] = []
        if vacancy_template.get("job_location"):
            for job_location in vacancy_template.get("job_location"):
                job_location_copy = job_location.copy()
                if not job_location_copy.get("radius"):
                    job_location_copy["radius"] = "50"
                vacancy_overrides["job_location"].append(job_location_copy)

        return vacancy_overrides

    def process_vacancy(self, vacancy_data: Dict[str, Any], vacancy_file_path: Optional[str] = "N/A") -> Optional[Dict[str, Any]]:
        """
        Processes a single vacancy through all matching phases.
        Returns a dictionary with results or None if a critical error occurs early.
        """
        vacancy_id = vacancy_data.get("vacancy_id")
        vacancy_refno = vacancy_data.get("refno")

        if not vacancy_id or not vacancy_refno:
            self.logger.error(f"Vacancy data from '{vacancy_file_path}' is missing 'vacancy_id' or 'refno'. Skipping.")
            return None # Cannot process without essential identifiers

        log_prefix = f"Vacancy '{vacancy_refno}' (ID: {vacancy_id}, File: {vacancy_file_path}):"
        self.logger.info(f"{log_prefix} Starting processing.")

        subcategory_name = vacancy_data.get('subcategory') or vacancy_data.get('sub_cat')
        if subcategory_name:
            # Check if a specific configuration (spec) exists for this subcategory
            # The ConfigManager would have logged a warning during init if the spec file had issues,
            # but get_subcategory_spec will return None if it's not in the loaded map.
            if self.config_manager.get_subcategory_spec(subcategory_name) is None:
                self.logger.error(f"{log_prefix} No pre-loaded spec found for subcategory '{subcategory_name}'. Skipping vacancy.")
                return {
                    "vacancy_id": vacancy_id,
                    "refno": vacancy_refno,
                    "source_file_path": vacancy_file_path,
                    "run_tag": self.run_tag,
                    "subcategory_applied": subcategory_name, # Record which subcategory was problematic
                    "processing_summary": {
                        "status": "skipped_missing_subcategory_spec",
                        "reason": f"No pre-loaded spec found for subcategory '{subcategory_name}'. Vacancy processing aborted."
                    }
                }
        elif not subcategory_name: # If subcategory_name is None or empty string
            self.logger.warning(f"{log_prefix} No 'subcategory' specified. Using master config defaults only for overrides.")
        
        conn_info = None
        conn_embed = None
        conn_results = None

        try:
            vacancy_overrides = self._get_vacancy_overrides(vacancy_data)
            effective_config = self.config_manager.get_effective_config(subcategory_name, vacancy_overrides=vacancy_overrides)
            # Store CLI args if they were added to master_config for engine use
            cli_args_from_config = effective_config.get("cli_args", {})

            # Generate config hash from the effective_config
            effective_config_hash, cleaned_config_json_for_hash = generate_config_hash(effective_config, self.logger)
            self.logger.info(f"{log_prefix} Effective configuration loaded for subcategory: '{subcategory_name or 'None'}'. Config Hash: {effective_config_hash}")

            # --- Database schema and table names from config ---
            results_db_settings = effective_config.get("database_environments", {}).get("results_db", {})
            results_db_schema = results_db_settings.get("schema")
            
            all_results_table_names = effective_config.get("table_names",{}).get("results_db_table_names", {})
            table_experiment_runs = all_results_table_names.get("experiment_runs")
            table_vacancy_category_max_score_for_check = all_results_table_names.get("vacancy_category_max_score") # Get this table name
            # table_run_configurations is needed by insert_vacancy_scores, will be fetched later if saving to DB.
            output_options = effective_config.get("output_options", {})
            # --- Pre-computation Check for DB Uniqueness (Fast Fail) ---
            save_to_db_enabled = output_options.get("save_results_to_db", False) # Use resolved save_to_db from results_db settings
            
            if save_to_db_enabled:
                if not results_db_schema or not table_experiment_runs or not table_vacancy_category_max_score_for_check:
                    self.logger.warning(f"{log_prefix} Results DB schema, experiment_runs table, or vacancy_category_max_score table name not configured. Skipping pre-check for existing run data for this vacancy.")
                else:
                    self.logger.info(f"{log_prefix} Performing pre-check for existing run data for vacancy '{vacancy_id}', run_tag '{self.run_tag}', and config hash '{effective_config_hash}'.")
                    conn_results_for_check = None
                    try:
                        conn_results_for_check = self._get_db_connection("results")
                        if conn_results_for_check:
                            if score_inserter.check_vacancy_processed_for_run( # Use the new function
                                conn_results_for_check,
                                self.run_tag,
                                effective_config_hash,
                                str(vacancy_id), # Ensure vacancy_id is a string
                                self.logger,
                                results_db_schema,
                                table_experiment_runs,
                                table_vacancy_category_max_score_for_check # Pass the specific table
                            ):
                                self.logger.error(f"{log_prefix} Fast fail: Results for vacancy '{vacancy_id}' with run tag '{self.run_tag}' and config hash '{effective_config_hash}' already exist in the database. Halting processing for this vacancy.")
                                # Ensure connection is closed before returning
                                if conn_results_for_check and not conn_results_for_check.closed:
                                    conn_results_for_check.close()
                                return {
                                    "vacancy_id": vacancy_id,
                                    "refno": vacancy_refno,
                                    "run_tag": self.run_tag,
                                    "effective_config_hash": effective_config_hash,
                                    "processing_summary": {
                                        "status": "failed_fast_duplicate_run",
                                        "reason": f"Duplicate run_tag and config_hash combination: tag='{self.run_tag}', hash='{effective_config_hash}'"
                                    },
                                    "effective_config_used": effective_config 
                                }
                            else:
                                self.logger.info(f"{log_prefix} Pre-check passed: No existing run data found for vacancy '{vacancy_id}', run_tag '{self.run_tag}', and config hash '{effective_config_hash}'.")
                        else:
                            self.logger.error(f"{log_prefix} Could not establish connection to results_db for pre-check. Cannot verify run uniqueness. Processing will continue but DB insertion might fail later.")
                    except Exception as e:
                        self.logger.error(f"{log_prefix} Error during pre-check for existing run data: {e}. Processing will continue, but DB insertion might fail.", exc_info=True)
                    finally:
                        if conn_results_for_check and not conn_results_for_check.closed:
                            conn_results_for_check.close()

            # Initialize connections
            conn_info = self._get_db_connection("info")
            conn_embed = self._get_db_connection("embed")
            register_vector(conn_embed) # Essential for pgvector operations on this connection

            # --- Generate Vacancy Embeddings ---
            self.logger.info(f"{log_prefix} Generating embeddings for vacancy data...")
            vacancy_embeddings_map = generate_vacancy_embeddings(vacancy_data, effective_config, self.openai_client, self.logger)
            if "error" in vacancy_embeddings_map: # Check if embedding_utils returned an error indicator
                raise VacancyProcessingError(f"{log_prefix} Failed to generate vacancy embeddings: {vacancy_embeddings_map['error']}")

            # --- Initialize Final Results Data Structure ---
            job_template = vacancy_data.get("job_template", {})
            final_results_data = {
                "vacancy_id": vacancy_id,
                "refno": vacancy_refno,
                "source_file_path": vacancy_file_path,
                "run_tag": self.run_tag, # Add run_tag
                "effective_config_hash": effective_config_hash, # Add config hash
                "subcategory_applied": subcategory_name,
                "years_of_experience_requested": job_template.get("years of experience", []),
                "ranked_candidates": [],
                "max_category_scores": {},
                "category_weights_applied": effective_config.get("default_category_weights", {}),
                "effective_scoring_parameters_used": effective_config.get("effective_scoring_parameters", {}),
                "effective_config_used": effective_config, # Add the full effective config used for this run
                "processing_summary": {"status": "started", "stages_completed": []}
            }

            # --- Phase 1: Prefiltering ---
            self.logger.info(f"{log_prefix} Starting Phase 1: Prefiltering.")
            prefiltered_candidate_data = phase1_prefilter.execute_prefiltering(
                vacancy_data, vacancy_embeddings_map, effective_config, conn_info, conn_embed, self.logger
            )
            final_results_data["processing_summary"]["prefilter_candidate_count"] = len(prefiltered_candidate_data)
            if not prefiltered_candidate_data:
                self.logger.warning(f"{log_prefix} No candidates found after pre-filtering. Aborting further matching.")
                final_results_data["processing_summary"]["status"] = "completed_no_candidates_after_prefilter"
                return final_results_data
            final_results_data["processing_summary"]["stages_completed"].append("Phase1_Prefiltering")
            self.logger.info(f"{log_prefix} Prefiltering complete. {len(prefiltered_candidate_data)} candidates proceeding.")

            # --- Phase 2: Semantic Score Calculation ---
            self.logger.info(f"{log_prefix} Starting Phase 2: Semantic Score Calculation.")
            candidate_scores_list, max_category_scores = calculate_all_semantic_scores(
                prefiltered_candidate_data, vacancy_data, vacancy_embeddings_map,
                effective_config, conn_embed, self.logger, self.openai_client
            )

            final_results_data["max_category_scores"] = {k: round(v, 4) for k, v in max_category_scores.items()}
            final_results_data["vacancy_total_max_score"] = round(sum(max_category_scores.values()), 4)
            final_results_data["processing_summary"]["scored_candidate_count"] = len(candidate_scores_list)

            if not candidate_scores_list:
                self.logger.warning(f"{log_prefix} No candidates were scored in Phase 2.")
                final_results_data["processing_summary"]["status"] = "completed_no_candidates_scored"
                return final_results_data
            final_results_data["processing_summary"]["stages_completed"].append("Phase2_SemanticScoring")

            # --- Phase 3: Ranking and Post Filtering ---
            self.logger.info(f"{log_prefix} Starting Phase 3: Ranking.")
            ranked_candidates_scored_data = phase3_ranking.rank_candidates(
                candidate_scores_list, effective_config, self.logger,  self.openai_client, vacancy_data
            )
            final_results_data["processing_summary"]["stages_completed"].append("Phase3_Ranking")

            # Get distance filtering settings from effective_config
            phase3_config = effective_config.get("phase3_ranking", {})
            enable_distance_filter = phase3_config.get("enable_distance_filtering", False)
            max_distance_for_filter = phase3_config.get("max_distance_miles", 30) # Default if not specified

            processed_ranked_list = []
            if ranked_candidates_scored_data: # Only proceed if there are candidates from ranking
                self.logger.info(f"{log_prefix} Starting candidate detail fetching for {len(ranked_candidates_scored_data)} candidates.")

                candidate_ids_from_ranking = [cand['candidate_id'] for cand in ranked_candidates_scored_data]
                candidate_details_map = db_operations.fetch_candidate_details(
                    candidate_ids_from_ranking, effective_config, conn_info, self.logger
                )
                self.logger.info(f"{log_prefix} Fetched details for {len(candidate_details_map)} candidates from DB.")

                candidates_to_process = []
                for score_data in ranked_candidates_scored_data:
                    cid = score_data["candidate_id"]
                    details = candidate_details_map.get(cid)
                    if details:
                        merged_candidate_data = {**score_data, **details}
                        candidates_to_process.append(merged_candidate_data)
                    else:
                        self.logger.warning(f"{log_prefix} Details not found for candidate_id: {cid}. Skipping for final list.")
                
                self.logger.info(f"{log_prefix} Prepared {len(candidates_to_process)} candidates with details.")

                # Conditionally apply distance filtering
                if enable_distance_filter and candidates_to_process:
                    db_connector = self._get_db_connector("info")
                    self.logger.info(f"{log_prefix} Distance filtering enabled. Max distance: {max_distance_for_filter} miles.")
                    candidates_after_distance_filter = filter_candidates_by_distance(
                        vacancy_data,
                        candidates_to_process, # This list has scores, city, state, etc.
                        logger=self.logger,
                        db_connector=db_connector,
                        max_distance_miles=max_distance_for_filter # Pass the configured max distance
                    )
                    self.logger.info(f"{log_prefix} {len(candidates_after_distance_filter)} candidates remaining after distance filtering.")
                    # The candidates_after_distance_filter list already contains all necessary data
                    # (scores, details from DB, distance).
                    list_to_finalize = candidates_after_distance_filter
                    final_results_data["processing_summary"]["stages_completed"].append("Phase3_DistanceFiltering")
                elif not enable_distance_filter:
                    self.logger.info(f"{log_prefix} Distance filtering is disabled via configuration.")
                    list_to_finalize = candidates_to_process # Skip distance filtering
                else: # No candidates to process
                    self.logger.info(f"{log_prefix} No candidates with details to process for distance filtering (or filtering disabled).")
                    list_to_finalize = []


                for final_candidate_entry in list_to_finalize:
                    # Round scores for final output
                    final_candidate_entry["category_raw_scores"] = {k: round(v,4) for k,v in final_candidate_entry.get("category_raw_scores",{}).items()}
                    final_candidate_entry["category_intermediate_scores"] = {k: round(v,4) for k,v in final_candidate_entry.get("category_intermediate_scores",{}).items()}
                    final_candidate_entry["total_raw_score"] = round(final_candidate_entry.get("total_raw_score",0.0),4)
                    final_candidate_entry["total_intermediate_score"] = round(final_candidate_entry.get("total_intermediate_score",0.0),4)
                    final_candidate_entry["normalized_score"] = round(final_candidate_entry.get("normalized_score",0.0),4)
                    
                    # Ensure 'name' and 'email' are present, even if they were not in candidate_details_map initially
                    # (though fetch_candidate_details should provide them if available)
                    # The merging logic for candidates_to_process should handle this,
                    # but as a safeguard for the final list:
                    final_candidate_entry.setdefault("name", "N/A")
                    final_candidate_entry.setdefault("email", "N/A")

                    processed_ranked_list.append(final_candidate_entry)
        
            final_results_data["ranked_candidates"] = processed_ranked_list
            final_results_data["processing_summary"]["ranked_candidate_count"] = len(final_results_data["ranked_candidates"])

            # Set status to completed_successfully, assuming core processing was successful.
            # This status will be reflected in the output file.
            # DB operation outcomes are tracked separately in db_insertion_status.
            final_results_data["processing_summary"]["status"] = "completed_successfully"

            # --- Output and Storage ---
            output_options = effective_config.get("output_options", {})
            top_n_for_output = cli_args_from_config.get("top_n", 20) # Get from CLI args via master_config, or default

            if output_options.get("save_results_to_filesystem", True):
                self.logger.info(f"{log_prefix} File save option is enabled. Results will be saved to the filesystem.")
                # Use output_folder from CLI if provided, else from master_config output_options
                output_folder_path = cli_args_from_config.get("output_folder") or output_options.get("default_results_filesystem_path", "./output_results")
                self._save_results_to_file(final_results_data, vacancy_refno, output_folder_path, top_n_for_output)
                final_results_data["processing_summary"]["stages_completed"].append("Output_SaveToFileSystem")


            if output_options.get("save_results_to_db", True) and save_to_db_enabled: # Ensure save_to_db_enabled is still true (it might be set by effective_config)
                self.logger.info(f"{log_prefix} Database save is enabled. Proceeding to save results to the database.")
                conn_results = None # Initialize to ensure it's defined for finally block
                try:
                    # Fetch all required table names for score_inserter.insert_vacancy_scores
                    table_run_configurations_for_insert = all_results_table_names.get("run_configurations")
                    table_experiment_runs_for_insert = all_results_table_names.get("experiment_runs") # Already fetched as table_experiment_runs
                    table_vacancy_category_max_score_for_insert = all_results_table_names.get("vacancy_category_max_score")
                    table_candidate_application_shortlists_for_insert = all_results_table_names.get("candidate_application_shortlists")

                    db_table_names_for_inserter = {
                        "run_configurations": table_run_configurations_for_insert,
                        "experiment_runs": table_experiment_runs_for_insert, # or table_experiment_runs
                        "vacancy_category_max_score": table_vacancy_category_max_score_for_insert,
                        "candidate_application_shortlists": table_candidate_application_shortlists_for_insert
                    }
                    
                    if not results_db_schema:
                        self.logger.error(f"{log_prefix} Results DB schema not found in configuration. Skipping DB insertion.")
                        final_results_data["processing_summary"]["db_insertion_status"] = "skipped_missing_schema_config"
                    elif not all(db_table_names_for_inserter.values()):
                        missing_tables = [k for k,v in db_table_names_for_inserter.items() if not v]
                        self.logger.error(f"{log_prefix} Missing one or more results DB table names in configuration: {missing_tables}. Skipping DB insertion.")
                        final_results_data["processing_summary"]["db_insertion_status"] = "skipped_missing_table_config"
                    else:
                        conn_results = self._get_db_connection("results")
                        self.logger.info(f"{log_prefix} Attempting to insert results into DB using insert_vacancy_scores.")
                        
                        # Ensure 'vacancy_data_for_run' is in final_results_data for score_inserter
                        if "vacancy_data_for_run" not in final_results_data["effective_config_used"]:
                            final_results_data["effective_config_used"]["vacancy_data_for_run"] = vacancy_data 

                        final_results_data["max_possible_score_for_run"] = final_results_data.get("vacancy_total_max_score", 0.0)

                        score_inserter.insert_vacancy_scores(
                            conn_results,
                            final_results_data, 
                            self.run_tag,
                            effective_config_hash,
                            cleaned_config_json_for_hash,
                            top_n_for_output, 
                            self.logger,
                            results_db_schema, # Pass schema
                            db_table_names_for_inserter # Pass dict of table names
                        )
                        self.logger.info(f"{log_prefix} DB insertion call to insert_vacancy_scores completed.")
                        final_results_data["processing_summary"]["stages_completed"].append("Output_SaveToDB_Orchestrated")
                        final_results_data["processing_summary"]["db_insertion_status"] = "success_orchestrated"

                except psycopg2.Error as db_err: # Catch psycopg2 errors specifically
                    self.logger.error(f"{log_prefix} DB operation failed during orchestrated insert: {db_err}", exc_info=True)
                    # No explicit rollback here as 'insert_vacancy_scores' handles its transaction.
                    final_results_data["processing_summary"]["db_insertion_status"] = "failed_orchestrated_db_error"
                    final_results_data["processing_summary"]["db_insertion_error"] = str(db_err)
                except Exception as e_orch: # Catch other errors from the orchestration
                    self.logger.error(f"{log_prefix} Orchestrated DB insertion failed: {e_orch}", exc_info=True)
                    final_results_data["processing_summary"]["db_insertion_status"] = "failed_orchestrated_general_error"
                    final_results_data["processing_summary"]["db_insertion_error"] = str(e_orch)
            elif not save_to_db_enabled:
                self.logger.info(f"{log_prefix} Skipping database insertion as per effective configuration (save_results_to_db is false).")
                final_results_data["processing_summary"]["db_insertion_status"] = "skipped_configuration"
            
            self.logger.info(f"{log_prefix} Processing completed successfully.")
            return final_results_data

        except VacancyProcessingError as vpe: # Errors specific to this vacancy's processing logic
            self.logger.error(f"{log_prefix} VacancyProcessingError: {vpe}", exc_info=True)
            if 'final_results_data' in locals(): # If initialized, update status
                final_results_data["processing_summary"]["status"] = "failed"
                final_results_data["processing_summary"]["error_message"] = str(vpe)
                return final_results_data
            return {"vacancy_id": vacancy_id, "refno": vacancy_refno, "status": "failed", "error": str(vpe)}
        except psycopg2.Error as db_e: # Catch DB errors not caught by specific operations
            self.logger.error(f"{log_prefix} A database error occurred: {db_e}", exc_info=True)
            if 'final_results_data' in locals():
                final_results_data["processing_summary"]["status"] = "failed_db_error"
                final_results_data["processing_summary"]["error_message"] = f"Database error: {db_e}"
                return final_results_data
            return {"vacancy_id": vacancy_id, "refno": vacancy_refno, "status": "failed_db_error", "error": str(db_e)}
        except Exception as e: # Catch-all for other unexpected errors
            self.logger.error(f"{log_prefix} An unexpected critical error occurred: {e}", exc_info=True)
            if 'final_results_data' in locals():
                final_results_data["processing_summary"]["status"] = "failed_unexpected_error"
                final_results_data["processing_summary"]["error_message"] = str(e)
                return final_results_data
            return {"vacancy_id": vacancy_id, "refno": vacancy_refno, "status": "failed_unexpected_error", "error": str(e)}
        finally:
            if conn_info:
                try: conn_info.close()
                except Exception as e_cls: self.logger.error(f"{log_prefix} Error closing conn_info: {e_cls}")
            if conn_embed:
                try: conn_embed.close()
                except Exception as e_cls: self.logger.error(f"{log_prefix} Error closing conn_embed: {e_cls}")
            if conn_results:
                try: conn_results.close()
                except Exception as e_cls: self.logger.error(f"{log_prefix} Error closing conn_results: {e_cls}")
            self.logger.info(f"{log_prefix} Finished processing attempt.")

