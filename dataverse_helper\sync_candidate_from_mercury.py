from dataverse_helper.dv_contact import get_all_candidate_info
from generator.get_files_from_sharepoint import get_file_metadata_from_sharepoint
from data_helper.resume_ingestion import ResumeIngestionService
from datetime import datetime, timedelta
import json
from common.utils.helpers import Helpers

def sync_candidate_from_mercury(candidate, token, dataverse_url, logger, connector, sharepointenv):
    """
    #resume_data_fields = ["city", "state", "availability", "last_contacted", "email", "phone"]
    #mercury_data_fields = ["address2_city", "recruit_address2_state", "availability", "last_contacted", "email", "phone"]
    """
    connector.connect()

    contactid = candidate['contact_id']
    cand_data = get_all_candidate_info(token, dataverse_url, contactid)
    if cand_data == []:
        logger.error(f"Candidate not found - {contactid}")
        return
    logger.debug(f"Candidate data = {cand_data}")

    city = cand_data[0]['address2_city']
    state_obj = cand_data[0].get('recruit_address2_state')
    state = state_obj.get('mercury_name') if isinstance(state_obj, dict) else None
    state2 = cand_data[0]['address2_stateorprovince']
    availability = cand_data[0]['recruit_availability']
    last_contacted = cand_data[0]['mercury_lastcontacted']
    email = cand_data[0]['emailaddress2']
    phone = cand_data[0]['telephone2']
    donot_sms = cand_data[0]['recruit_donotsms']
    placement_status = cand_data[0]['recruit_candidatecontactstatus']

    if state is None:
        logger.info(f"----Dataverse State is None for contact_id: {contactid}. Using state2: {state2}")
        state = state2
    
    resume_service = ResumeIngestionService(logger=logger, db_connector=connector)
    r_data = resume_service.read_candidate_from_db(contactid)
    resume_data = r_data.get('resume_data')

    if resume_data is None:
        logger.debug(f"resume_data is None for contact_id: {contactid}")
        return

    #get information from sharepoint   
    modified = None
    cv_url = resume_data.get('resume_file')
    if cv_url is not None:
        # Find the position of '/data' in the URL
        data_index = cv_url.find("/sites")
        if data_index != -1:
            cv_url = cv_url[data_index:]
            file_metadata = get_file_metadata_from_sharepoint(sharepointenv, cv_url, logger=logger)
            logger.debug(f"resume_data: modified = {resume_data.get('modified')}, url = {resume_data.get('resume_file')}")
    else:
        logger.info(f"resume_file is None for contact_id: {contactid}")
    
    logger.info(f"** syncing candidate {contactid} previous - state: {resume_data.get('state')}, city: {resume_data.get('city')}, availability: {resume_data.get('availability')}, last_contacted: {resume_data.get('last_contacted')}, email: {resume_data.get('email')}, phone: {resume_data.get('phone')}, placement_status: {resume_data.get('placement_status')}, donot_sms: {donot_sms}")    
    if modified is not None:
        resume_data['modified'] = modified
    if availability is not None:
        resume_data['availability'] = availability
    if last_contacted is not None:
        resume_data['last_contacted'] = last_contacted
    if email is not None:
        resume_data['email'] = email
    if phone is not None:
        resume_data['phone'] = phone
    if city is not None:
        resume_data['city'] = city
    if state is not None:
        resume_data['state'] = state
    if placement_status is not None:
        resume_data['placement_status'] = placement_status
    if donot_sms is not None:
        resume_data['donot_sms'] = donot_sms

    logger.info(f"** syncing candidate {contactid} - state: {state}, city: {city}, availability: {availability}, last_contacted: {last_contacted}, email: {email}, phone: {phone}, placement_status: {placement_status}, donot_sms: {donot_sms}")    
    '''
    helpers = Helpers(logger=logger)    
    resume_data2 = helpers.serialize_datetime_for_json(resume_data)
    print(f"resume_data: {json.dumps(resume_data2, indent=4)}")
    '''

    resume_service.ingest_candidate_raw_data(resume_data, commit=True)

def sync_candidates_in_list_from_mercury(candidates, token, dataverse_url, logger, connector, sharepointenv):
    count = 0
    logger.info(f"Syncing {len(candidates)} candidates")
    for candidate in candidates:
        sync_candidate_from_mercury(candidate, token, dataverse_url, logger, connector, sharepointenv)
        count += 1
        if count % 100 == 0:
            logger.info(f"Synced {count} candidates")
    logger.info(f"Synced {count} candidates")

def get_updated_candidates_since(since_date: datetime, db_connector, logger):
    """
    Retrieve candidates from candidate_application_shortlists updated after the given timestamp.
    """
    try:
        db_connector.connect()
        conn = db_connector.connection
        cur = conn.cursor()
        query = f"""
            SELECT application_id, vacancy_id, contact_id, updated_at
            FROM {db_connector.schema}.candidate_application_shortlists
            WHERE updated_at > %s AND archived = false
        """
        cur.execute(query, (since_date,))
        rows = cur.fetchall()
        col_names = [desc[0] for desc in cur.description]
        cur.close()
        return [dict(zip(col_names, row)) for row in rows]
    except Exception as e:
        logger.error(f"Error retrieving updated candidates: {e}")
        return []

def sync_shortlisted_results(last_synced_date, token, dataverse_url, logger, connector, sharepointenv):
    candidates = get_updated_candidates_since(last_synced_date, connector, logger)

    helpers = Helpers(logger=logger)
    logger.info(f"Total candidates: {len(candidates)}")
    #candidates2 = helpers.serialize_datetime_for_json(candidates)
    #print(f"candidates: {json.dumps(candidates2, indent=4)}")
    unique_candidates = {c["contact_id"]: c for c in candidates}.values()
    unique_candidates = list(unique_candidates)
    logger.info(f"Total unique candidates: {len(unique_candidates)}")
    #print(f"unique_candidates: {json.dumps(unique_candidates, indent=4)}")
    sync_candidates_in_list_from_mercury(unique_candidates, token, dataverse_url, logger, connector, sharepointenv)


from dataverse_helper.dv_common import read_fields_from_dataverse
from dataverse_helper.token_manager import get_token_for_env, get_dataverse_credentials_for_env, Environment, get_enum_for_env_num
from common.secrets_env import load_secrets_env_variables
from common.db.global_dbconnector import GlobalDBConnector
from common.appLogger import getGlobalAppLogger
from common.db.config_postgres import PostgresEnvironment
import time
import sys

def test_sync_candidate_from_mercury():
    load_secrets_env_variables()
    env = Environment.SANDBOX
    postgres_env = PostgresEnvironment.SANDBOX
    sharepointenv = 2
    
    logger=getGlobalAppLogger()
    token = get_token_for_env(env)
    dataverse_url = get_dataverse_credentials_for_env(env)["RESOURCE_URL"]
    connector = GlobalDBConnector.get_connector(postgres_env, logger)
    sync_candidate_from_mercury("45cc37b0-90e7-ee11-85fb-6045bd7b41b0", token, dataverse_url, logger, connector, sharepointenv)

def test_sync_shortlisted_results():
    load_secrets_env_variables()
    env = Environment.PROD
    postgres_env = PostgresEnvironment.PROD
    sharepointenv = 2
    
    logger = getGlobalAppLogger()
    token = get_token_for_env(env)
    dataverse_url = get_dataverse_credentials_for_env(env)["RESOURCE_URL"]
    connector = GlobalDBConnector.get_connector(postgres_env, logger)
    sync_shortlisted_results(datetime.now()-timedelta(days=20), token, dataverse_url, logger, connector, sharepointenv)

def main():
    if len(sys.argv) != 3:
        print("Usage: python sync_runner.py <wait_seconds> <last_synced_iso>")
        sys.exit(1)

    try:
        wait_seconds = int(sys.argv[1])
        last_synced_date = datetime.fromisoformat(sys.argv[2])
    except ValueError:
        print("wait_seconds must be an integer")
        print("last_synced_date must be an ISO format string")
        sys.exit(1)

    load_secrets_env_variables()
    env = Environment.PROD
    postgres_env = PostgresEnvironment.PROD
    sharepointenv = 2

    logger = getGlobalAppLogger()
    token = get_token_for_env(env)
    dataverse_url = get_dataverse_credentials_for_env(env)["RESOURCE_URL"]
    connector = GlobalDBConnector.get_connector(postgres_env, logger)

    while True:
        try:
            logger.info("Starting sync_shortlisted_results")
            sync_shortlisted_results(last_synced_date, token, dataverse_url, logger, connector, sharepointenv)
            last_synced_date = datetime.utcnow()
            logger.info(f"Sync complete. Sleeping for {wait_seconds} seconds.")
        except Exception as e:
            logger.error(f"Unexpected error during sync: {e}")

        time.sleep(wait_seconds)

if __name__ == "__main__":
    #main()
    #test_sync_candidate_from_mercury()
    test_sync_shortlisted_results()