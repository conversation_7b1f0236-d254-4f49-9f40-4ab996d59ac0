locals {

  json_records = {
    "EntitlementsJson" = file("${path.module}/terraform_pipelines/config/${var.environment}/entitlements.json")

  }

  config_records = {

    AZURE_SQLSERVER_SERVER             = var.AZURE_SQLSERVER_SERVER
    AZURE_SQLSERVER_DATABASE           = var.AZURE_SQLSERVER_DATABASE
    AZURE_SHAREPOINT_SITE_URL          = var.AZURE_SHAREPOINT_SITE_URL
    AZURE_DATAVERSE_RESOURCE_URL       = var.AZURE_DATAVERSE_RESOURCE_URL
    AZURE_DATAVERSE_CRM_URL            = var.AZURE_DATAVERSE_CRM_URL
    NEXTAUTH_URL                       = var.NEXTAUTH_URL
    # NEXT_APPINSIGHTS_CONNECTION_STRING = var.NEXT_APPINSIGHTS_CONNECTION_STRING
    # NEXT_PUBLIC_AUTH_URL               = var.NEXT_PUBLIC_AUTH_URL

    # Shared DB Connections #
    DBSharedConnectionUsername = var.DBSharedConnectionUsername
    DBSharedConnectionURL      = var.DBSharedConnectionURL
    DBSharedConnectionDatabase = var.DBSharedConnectionDatabase
    DBSharedConnectionPort     = var.DBSharedConnectionPort

  }
}

resource "azurerm_app_configuration_key" "json_app_configs" {
  for_each = local.json_records

  configuration_store_id = data.azurerm_app_configuration.appconfig.id
  key                    = each.key
  value                  = each.value
  content_type           = "application/json"
  label                  = var.label
  depends_on             = [data.azurerm_app_configuration.appconfig]
}


resource "azurerm_app_configuration_key" "dp_app_configs" {
  for_each = local.config_records

  configuration_store_id = data.azurerm_app_configuration.appconfig.id
  key                    = each.key
  value                  = each.value
  label                  = var.label
  depends_on             = [data.azurerm_app_configuration.appconfig]
}
