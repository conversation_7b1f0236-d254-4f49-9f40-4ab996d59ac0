import { ReactNode } from "react";

interface ModalProps extends React.HTMLAttributes<HTMLDivElement> {
  title?: string;
  isOpen: boolean;
  children: ReactNode;
  onClose: () => void;
  width?: string;
  height?: string;
  mercury?: boolean;
}

const Modal = ({
  isOpen,
  title,
  children,
  onClose,
  width = "max-w-md",
  height,
  mercury = false,
  ...rest
}: ModalProps) => {
  if (!isOpen) return <></>;
  return (
    <div className="fixed inset-0 bg-black bg-opacity-20 flex justify-center items-center z-50">
      <div
        className={`bg-white rounded-lg shadow-lg w-[90%] ${width} ${height} p-6 relative`}
        {...rest}
      >
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-500 hover:text-gray-700 text-xl z-10"
          aria-label="Close"
        >
          ✖
        </button>
        {/* Header */}
        {title ? (
          <div className="flex justify-between items-center border-b pb-2 mb-4">
            <h2 className="text-xl font-semibold">{title}</h2>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700"
            >
              ✖
            </button>
          </div>
        ) : null}

        {/* Content */}
        <div className={mercury ? "p-4 w-[100%] h-[100%]" : undefined}>
          {children}
        </div>
      </div>
    </div>
  );
};

export default Modal;
