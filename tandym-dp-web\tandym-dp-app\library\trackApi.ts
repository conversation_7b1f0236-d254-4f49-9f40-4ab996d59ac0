// lib/trackedFetch.ts
import { getAppInsights } from "./appInsights";
import { getOrCreateUserUuid } from "./utils";

export async function trackedFetch(
  input: RequestInfo | URL,
  init: RequestInit = {},
  extraCtx: Record<string, any> = {}
): Promise<Response> {
  const uuid = getOrCreateUserUuid();

  // --- inject header so the backend can log/link this request too
  const headers = new Headers(init.headers);
  headers.set("X-User-UUID", uuid);

  const start = performance.now();
  try {
    const response = await fetch(input, { ...init, headers });
    const dur = performance.now() - start;

    getAppInsights()?.trackDependencyData({
      id: crypto.randomUUID(), // per-call correlation id
      name: typeof input === "string" ? input : input.toString(),
      target: window.location.hostname,
      duration: dur,
      success: response.ok,
      responseCode: response.status,
      type: "Fetch",
      properties: { userUuid: uuid, ...extraCtx },
    });

    if (!response.ok) throw new Error(`HTTP ${response.status}`);
    return response;
  } catch (err) {
    const dur = performance.now() - start;
    getAppInsights()?.trackDependencyData({
      id: crypto.randomUUID(),
      name: typeof input === "string" ? input : input.toString(),
      target: window.location.hostname,
      duration: dur,
      success: false,
      responseCode: 0,
      type: "Fetch",
      properties: {
        userUuid: uuid,
        error: err instanceof Error ? err.message : String(err),
        ...extraCtx,
      },
    });
    getAppInsights()?.trackException({
      error: err as Error,
      properties: { userUuid: uuid },
    });
    throw err;
  }
}
