import requests
import json
#import msal
import sys
import os
import re
import argparse

from bs4 import BeautifulSoup
from common.utils.user_input import UserInput
from data_helper.category_subcat_cache_db import CategoryCache
from dataverse_helper.dv_get_subcategory import get_sub_category_from_dv
from matcher.parse_jobdescription import parse_jobdescription_w_weights
from dataverse_helper.dv_common import find_row_in_dataverse
from dataverse_helper.dv_conn import get_oauth_token, getUATDataverseCredentials, getProdDataverseCredentials, getSandBoxDataverseCredentials
from common.CustomExceptions import DataverseError
from common.appLogger import getGlobalAppLogger, getGlobalAppLogger_vacancy
from dataverse_helper.dv_common import read_fields_from_dataverse
from dataverse_helper.token_manager import get_token_for_env, get_dataverse_credentials_for_env, Environment
from common.secrets_env import load_secrets_env_variables
from common.db.config_postgres import PostgresEnvironment
from common.db.global_dbconnector import GlobalDBConnector
from data_helper.subcat_skills_db import SubcatSkills
from data_helper.vacancy_db import VacancyDB
from datetime import datetime
from dataverse_helper.request_respone import make_http_request
from generator.jobtemplate_utils import populate_job_template, extract_validate_job_template, extract_between_markers
# Import the SendGrid helper
from utils.email.sendgrid_helper import SendGridEmailHelper

# Email configuration constants
DEFAULT_SUPPORT_EMAIL = os.getenv("EMAIL_NOTIFICATION_SUPPORT_EMAIL", "<EMAIL>")
DEFAULT_CC_EMAIL = os.getenv("EMAIL_NOTIFICATION_CC_EMAIL", ["<EMAIL>", 
                                                             "<EMAIL>",
                                                             "<EMAIL>",
                                                             "<EMAIL>",
                                                             "<EMAIL>",
                                                             "<EMAIL>",
                                                             "<EMAIL>"])
DEFAULT_FROM_EMAIL = os.getenv("SENDGRID_DEFAULT_SENDER", "<EMAIL>")
DEFAULT_CRM_BASE_URL = os.getenv("EMAIL_NOTIFICATION_CRM_BASE_URL", "https://tandymgroup.crm.dynamics.com/main.aspx?appid=0ec72dfd-7eb1-ee11-a569-00224822704f&pagetype=entityrecord&etn=crimson_vacancy&id=")
DEFAULT_FALLBACK_EMAIL = os.getenv("EMAIL_NOTIFICATION_FALLBACK_EMAIL", "<EMAIL>")

JOB_TEMPLATE_HTML = """
    <p><strong>Introduction: </strong>(DO NOT include the word introduction when writing up the job.)</p>
    <p>This section is a brief one to two sentences confidentially introducing the client, the location, and the job title. An accompanying sentence giving an overview of the job can go here as well.</p>
    <p>For example:</p>
    <p><em>"A professional services firm in Florida is currently seeking…."</em></p><br/>

<p><strong>About the Opportunity:</strong> (If applicable, this section title is bolded & typed into the job publishing page for each posting.)</p>
<p>This section is used to highlight any specific details about the role. It helps to deter any candidates who can't meet the hourly, shift, etc. requirements of the position.</p>
<ul>
  <li>Hybrid or Remote:</li>
  <li>Shift:</li>
  <li>Schedule: </li>
  <li>Hours: </li>
  <li>Setting: </li>
  <li>Etc.: (anything applicable that the candidate should know before applying)</li>
</ul>
<br/>

<p><strong>Responsibilities:</strong> (This section title is bolded & typed into the job publishing page for each posting.)</p>
<p>The <strong><em>(insert job title)</em></strong> will be responsible for:</p>
<ul>
  <li>Task #1</li>
  <li>Task #2</li>
  <li>Task #3</li>
  <li>Task #4</li>
  <li>Task #5</li>
</ul>
<br/>

<p><strong>Qualifications:</strong> (This section title is bolded & typed into the job publishing page for each posting.)</p>
<ul>
  <li>(Insert years of experience)</li>
  <li>(Insert degree / education)</li>
  <li>(Insert applicable certifications, if any)</li>
  <li>(Insert Hard Skill #1)</li>
  <li>(Insert Hard Skill #2)</li>
  <li>(Insert Hard Skill #3)</li>
  <li>(Insert Soft Skill #1)</li>
  <li>(Insert Soft Skill #2)</li>
  <li>(Insert Soft Skill #3)</li>
</ul>
<br/>

<p><strong>Desired Skills:</strong> (If applicable, this section title is bolded & typed into the job publishing page for each posting.)</p>
<ul>
  <li>(Insert years of experience)</li>
  <li>(Insert degree / education)</li>
  <li>(Insert applicable certifications, if any)</li>
  <li>(Insert Hard Skill #1)</li>
  <li>(Insert Soft Skill #1)</li>
</ul>
<br/>
"""

JOB_TEMPLATE_HTML1 = """
    <p><strong>Introduction: </strong>(DO NOT include the word introduction when writing up the job.)</p>
    <p>This section is a brief one to two sentences confidentially introducing the client, the location, and the job title. An accompanying sentence giving an overview of the job can go here as well.</p>
    <p>For example:</p>
    <p><em>"A professional services firm in Florida is currently seeking….”"</em></p><br/>

<p><strong>About the Opportunity:</strong> (If applicable, this section title is bolded & typed into the job publishing page for each posting.)</p>
<p>This section is used to highlight any specific details about the role. It helps to deter any candidates who can't meet the hourly, shift, etc. requirements of the position.</p>
<ul>
  <li>Hybrid or Remote:</li>
  <li>Shift:</li>
  <li>Schedule: </li>
  <li>Hours: </li>
  <li>Setting: </li>
  <li>Etc.: (anything applicable that the candidate should know before applying)</li>
</ul>
<br/>

<p><strong>Responsibilities:</strong> (This section title is bolded & typed into the job publishing page for each posting.)</p>
<p>The <strong><em>(insert job title)</em></strong> will be responsible for:</p>
<ul>
  <li>Task #1</li>
  <li>Task #2</li>
  <li>Task #3</li>
  <li>Task #4</li>
  <li>Task #5</li>
</ul>
<br/>

<p><strong>Qualifications:</strong> (This section title is bolded & typed into the job publishing page for each posting.)</p>
<ul>
  <li>(Insert years of experience)</li>
  <li>(Insert degree / education)</li>
  <li>(Insert applicable certifications, if any)</li>
  <li>(Insert Hard Skill #1)</li>
  <li>(Insert Hard Skill #2)</li>
  <li>(Insert Hard Skill #3)</li>
  <li>(Insert Soft Skill #1)</li>
  <li>(Insert Soft Skill #2)</li>
  <li>(Insert Soft Skill #3)</li>
</ul>
<br/>

<p><strong>Desired Skills:</strong> (If applicable, this section title is bolded & typed into the job publishing page for each posting.)</p>
<ul>
  <li>(Insert years of experience)</li>
  <li>(Insert degree / education)</li>
  <li>(Insert applicable certifications, if any)</li>
  <li>(Insert Hard Skill #1)</li>
  <li>(Insert Soft Skill #1)</li>
</ul>
<br/>
"""

DV_COLUMNS_TO_FETCH = [
    'crimson_vacancyid',               # Vacancy ID
    'crimson_jobtitle',
    'crimson_addresscity',
    'crimson_vacancyrefno',           # Ref No: PR/503125, CR/503125 etc
    'crimson_jobsummaryemail',
    'mercury_emaildescription',
    'crimson_jobsummary',
    'recruit_adverttext2',
    'recruit_booleanuserfield3',       # Catalyst Match
    'recruit_adverttext3',             # Advertized Job Description - Broadbean, Shazamme
    'recruit_dateuserfield1',          # Start Date set by recruiter
    'recruit_mandatorytagcontrol0',    # Job Category
    'recruit_mandatorytagcontrol2',    # Job Subcategory
    'recruit_numericuserfield5',       # Processed flag
    'mercury_visibleonportal',         # Visible on portal
    'recruit_publishedpostingurl',     # job URL posted on https://careers.tandymgroup.com
    # 'statecode',                       # State code - TODO: Why we do this as the query filter already has this?
    'createdon'                        # Created on
]
# Function to escape and encode each tag
def escape_and_encode_tag(tag):
    import urllib.parse
    # Escape apostrophes for Dataverse (SQL escaping)
    escaped_tag = tag.replace("'", "''")
    # URL encode the escaped tag
    encoded_tag = urllib.parse.quote(escaped_tag)
    return encoded_tag
def find_tags_batch(token, dataverse_url, tags, logger = None):
    if logger == None:
        logger = getGlobalAppLogger()
    # Build a $filter query
    filter_query = " or ".join([f"(mercury_fullname eq '{escape_and_encode_tag(name)}' and statecode eq 0)" for name in tags])

    headers = {
        "Authorization": f"Bearer {token.get_token()}",
        "Content-Type": "application/json"
    }
    url = f"{dataverse_url}/api/data/v9.1/mercury_tags?$select=mercury_name,mercury_fullname,mercury_tagid&$filter={filter_query}"
    logger.debug(url)

    result = make_http_request('GET', url, headers=headers, logger=logger)

    if result["error"]:
        logger.error(f"find_tags_batch Error: {result['error']}")
        return {}
    if result["status_code"] == 200:
        #print("GET request successful!")
        return (result["response"].json())
    else:
        raise DataverseError(f"file - find_tags_batch -  \nFailed to find. Status code: {result["status_code"]} - \nResponse:, {result["response"].text}")

def find_existing_cat_subcat(token, dataverse_url, contactid, logger=None):
    if logger == None:
        logger = getGlobalAppLogger()


    headers = {
    "Authorization": f"Bearer {token.get_token()}",
    "Accept": "application/json",
    "Content-Type": "application/json"
    }

    url = f"{dataverse_url}/api/data/v9.1/contacts({contactid})/recruit_contact_candidatetag?$select=mercury_name,mercury_fullname&$filter=contains(mercury_fullname, 'Subcategory >') or contains(mercury_fullname, 'Functional Skills >')"


    # Make the GET request
    response = make_http_request('GET', url, headers=headers,logger=logger)

    subcategories = set()
    categories = set()
    if response["error"]:
        logger.error(f"find_tags_batch Error: {response['error']}")
        return categories, subcategories   
    if response["status_code"] == 200:
        data = response["response"].json()
        # Process the response data
        for record in data["value"]:
            mercury_name = record["mercury_name"]
            mercury_fullname = record["mercury_fullname"]

            if "Subcategory >" in mercury_fullname:
                subcategories.add(mercury_name)
            elif "Functional Skills >" in mercury_fullname:
                categories.add(mercury_name)
    else:
        raise DataverseError(f"function - find_existing_cat_subcat -  \nFailed to find. Status code: {response["status_code"]} - \nResponse:, {response["response"].text}")

    return categories, subcategories
def add_skill_tags_batch (token, dataverse_url, add_list, data, logger = None):
    if logger == None:
        logger = getGlobalAppLogger()
    import re
    import uuid
    batch_boundary = f"batch_{uuid.uuid4().hex}"
    #Get parent tags
    batch_url = f"{dataverse_url}/api/data/v9.1/$batch"
    headers = {
    "Authorization": f"Bearer {token.get_token()}",
    "Accept": "application/json",
    "Content-Type": f"multipart/mixed; boundary={batch_boundary}"
    }
    i = 0
    # Build the batch request body
  
    batch_body = []
    url = f"/api/data/v9.1/mercury_tags"
    for nm_skill in add_list:
        #logger.debug(f" -- {nm_skill}-----")

        new_row_data = {
            "mercury_fullname": nm_skill['mercury_fullname'],
            "mercury_name": nm_skill['mercury_name'],
            "mercury_description": nm_skill['mercury_name'],
            "<EMAIL>":f"/mercury_tags({nm_skill['p_id']})",
            "mercury_isleaf":True,
            "mercury_isnode":False,
            "mercury_importednote":f"{data}",
            "statuscode":1,
            "statecode":0,
        }
        batch_body.append(
            f"--{batch_boundary}\r\n"
            f"Content-Type: application/http\r\n"
            f"Content-Transfer-Encoding: binary\r\n\r\n"
            f"POST {url} HTTP/1.1\r\n"
            f"Content-Type: application/json\r\n\r\n"
            f"{json.dumps(new_row_data)}\r\n\r\n"
        )
        #batch_body.append("\r\n")
        if i >= 1:
            break
        #i = i + 1
        

    #batch_body.append(f"--{batch_boundary}--\r\n")
    # Join all parts of the batch body
    batch_request_body = "\r\n".join(batch_body)
    #logger.debug(batch_request_body)

    # Send the batch request
    result = make_http_request('POST', batch_url, headers=headers, data=batch_request_body, logger=logger)

    if result["error"]:
        logger.error(f"add_skill_tags_batch Error: {result['error']}")
        return None
    rep = result["response"]
    if result["status_code"] != 200:
        raise DataverseError(f"function - add_skill_tags_batch -  \nadd new skills. Status code: {result["status_code"]} - \nResponse:, {rep.text}")

    
    #logger.debug(response.text)
    # Regular expression to extract IDs inside mercury_tags(...)
    pattern = r"OData-EntityId: .*?/mercury_tags\((.*?)\)"

    # Find all matches
    ids = re.findall(pattern, rep.text)
    return ids

def check_guid_in_response(response_json, target_guid):
    """
    Check if a specific GUID exists in the response data.

    Parameters:
        response_json (dict): The JSON response containing the data.
        target_guid (str): The GUID to search for.

    Returns:
        bool: True if the GUID is found, False otherwise.
    """
    # Iterate through the 'value' array in the response
    for item in response_json.get("value", []):
        # Extract the '@odata.id' value, which contains the GUID
        odata_id = item.get("@odata.id", "")
        # Check if the target GUID is in the '@odata.id' URL
        if target_guid in odata_id:
            return True
    
    # If no match was found, return False
    return False

def associate_vacancy_with_skill(token, dataverse_url, vacancy_id, skill_id, weight, logger = None):
    if logger == None:
        logger = getGlobalAppLogger()
    headers = {
        'Authorization': f'Bearer {token.get_token()}',
        'Content-Type': 'application/json',
        'OData-MaxVersion': '4.0',
        'OData-Version': '4.0',
        'Prefer': 'return=representation'
    }
    if weight == "" or weight == 'normal':
        url = f"{dataverse_url}/api/data/v9.1/crimson_vacancies({vacancy_id})/mercury_desirable_tags/$ref"
    else:
        url = f"{dataverse_url}/api/data/v9.1/crimson_vacancies({vacancy_id})/mercury_mandatory_tags/$ref"
    #print(f" searching - {url}")
    response = requests.get(url, headers=headers)
    context_url = f"{dataverse_url}/api/data/v9.1/$metadata#$ref"

    # Check the response status
    if response.status_code == 200:
        # Check if the skill_id exists in the response
        associated_skills = response.json().get("value", [])

        # Extract the skill_id portion from the full URL in associated_skills
        for tag in associated_skills:
            # Extract the GUID from the @odata.id URL
            associated_skill_id = tag["@odata.id"].split('tags(')[-1].split(')')[0]
            
            # Compare the extracted skill_id with the provided skill_id
            if associated_skill_id == skill_id:
            # Check if skill_id is already associated
                logger.info(f"Skill ID {skill_id} is already associated with Vacancy ID {vacancy_id}.")
                return True, {}
        else:
            # Skill is not associated, proceed with POST
            logger.info("Skill ID is not associated. Proceeding with POST.")
            update_data = {
                "@odata.context": context_url,
                "@odata.id": f"mercury_tags({skill_id})"
            }
            post_response = requests.post(url, headers=headers, data=json.dumps(update_data))
            return False, post_response
            # Handle the POST response if necessary
    elif response.status_code == 404:
        # The vacancy record doesn't exist, shouldn't happen
       
        raise DataverseError(f"file - associate_vacancy_with_skill -  \n VacancyID not found. Status code: {response.status_code} - \nResponse:, {response.text}")

    else:
        raise DataverseError(f"file - associate_vacancy_with_skill -  \n Search error. Status code: {response.status_code} - \nResponse:, {response.text}")


def strip_html_tags(html_text):
    """
    Strip HTML tags from text and normalize whitespace.
    
    Args:
        html_text (str): The HTML text to clean
        
    Returns:
        str: Cleaned and normalized text
    """
    if not isinstance(html_text, str):
        return html_text
        
    # First use BeautifulSoup to remove HTML tags
    soup = BeautifulSoup(html_text, 'html.parser')
    text = soup.get_text(separator=' ', strip=True)
    
    # Normalize whitespace and special characters
    text = text.replace('\n', ' ')  # Replace newlines with spaces
    text = text.replace('\r', ' ')  # Replace carriage returns with spaces
    text = text.replace('\t', ' ')  # Replace tabs with spaces
    text = ' '.join(text.split())   # Normalize all whitespace to single spaces
    
    # Remove any non-breaking spaces and other special spaces
    text = text.replace('\xa0', ' ')
    
    # Remove any leading/trailing whitespace
    text = text.strip()
    
    return text

def is_valid_job_description(text):
    """
    Check if a job description is valid for processing.
    
    Args:
        text (str): The job description text to validate
        
    Returns:
        bool: True if the text is valid, False otherwise
    """
    if not text:
        return False
                    
    # Check if it's just the template
    if strip_html_tags(text) == strip_html_tags(JOB_TEMPLATE_HTML):
        logger.debug("Text matches template exactly")
        return False
            
    if strip_html_tags(text) == strip_html_tags(JOB_TEMPLATE_HTML1):
        logger.debug("Text matches template exactly")
        return False

    # Check for template phrase
    if "DO NOT include the word introduction when writing up the job" in text:
        logger.debug("Found template phrase")
        return False
            
    # Check word count
    words = re.findall(r'\b\w+\b', text)
    word_count = len(words)
    logger.debug(f"Word count: {word_count}")
    
    return word_count >= 150

def send_template_error_notification(email_helper, dataverse_url, owninguser, deliveryowner, vacancy_ref, vacancy_title, vacancy_id, error_text, logger=None):
    """
    Send email notification to the owning user about template errors.
    
    This function notifies the vacancy owner (or delivery owner as fallback) when there are
    issues with the job template that prevent Catalyst Match from being enabled. The email
    includes details about the specific error and provides a link to the vacancy in Mercury
    so the user can fix the template and resubmit for search match.
    
    Args:
        email_helper: SendGridEmailHelper instance
        dataverse_url: Base URL for the Dataverse environment
        owninguser: Email of the owning user
        deliveryowner: Email of the delivery owner (fallback)
        vacancy_ref: Vacancy reference number
        vacancy_title: Job title
        vacancy_id: Vacancy ID
        error_text: Description of the template error
        logger: Logger instance
        
    Returns:
        bool: True if email was sent successfully, False otherwise
    """
    if logger is None:
        logger = getGlobalAppLogger()
    
    # Determine recipient email with fallback logic
    recipient_email = None
    if owninguser and '@' in owninguser:
        recipient_email = owninguser
        logger.info(f"Using owning user email for template error notification: {recipient_email}")
    elif deliveryowner and '@' in deliveryowner:
        recipient_email = deliveryowner
        logger.info(f"Using delivery owner email for template error notification: {recipient_email}")
    else:
        recipient_email = DEFAULT_FALLBACK_EMAIL
        logger.info(f"Using fallback email for template error notification: {recipient_email}")
    
    # Prepare email context
    email_context = {
        "recruiter_name": recipient_email.split('@')[0].replace('.', ' ').title(),  # Convert email to name
        "vacancy_title": vacancy_title or "Unknown Position",
        "vacancy_ref": vacancy_ref,
        "vacancy_url": f"{dataverse_url}/main.aspx?appid=0ec72dfd-7eb1-ee11-a569-00224822704f&pagetype=entityrecord&etn=crimson_vacancy&id={vacancy_id}",
        "error_description": error_text,
        "support_email": DEFAULT_SUPPORT_EMAIL
    }
    
    try:
        response = email_helper.send_templated_email(
            template_base="template_error",
            context=email_context,
            from_email=DEFAULT_FROM_EMAIL,
            to_emails=recipient_email,
            cc_emails=DEFAULT_CC_EMAIL,
            subject_template="{{ vacancy_ref }}: {{ vacancy_title }} - Job Template Error - Action Required"
        )
        
        if 'error' in response:
            raise Exception(response['error'])
        
        logger.info(f"Template error notification sent successfully to {recipient_email} for vacancy {vacancy_ref}")
        return True
        
    except Exception as email_error:
        logger.error(f"Failed to send template error notification for vacancy {vacancy_ref}: {email_error}")
        return False

#Get the weight for skill
def search_skill_in_key(json_data, key, skill_name):
    # Check if the key exists in the JSON data
    if key not in json_data:
        return "normal"
    
    # Search within the values of the specified key
    skills = json_data[key]
    for skill in skills:
        if skill["name"] == skill_name:
            return skill["weight"]
    return "normal"

def associate_add_skills_to_vacancy(token, dataverse_url, vacancyid, skill_data, data, s_key, logger = None):
    if logger == None:
        logger = getGlobalAppLogger()
    for skill in skill_data:
        
        weight = search_skill_in_key(data, s_key, skill)
        logger.info(f"skill is {skill} and weight {weight}")
        #If weight is null, skill is not in subcat, so add as normal.
        if s_key == "soft skills":
            fullname = "Skills > soft skills > " + skill
        elif s_key == "technical skills":
            fullname = "Skills > technical skills > " + skill
        else:
            fullname = "Skills > tools and platforms > " + skill

        #see if this exists
        row = find_row_in_dataverse(token, dataverse_url, "mercury_tag", 'mercury_fullname', fullname)
        
        if row != None:
            skillid = row['mercury_tagid'] 
            #Check if this a skill found from candidate resume, if so don't associate yet as it needs to get added from job vacancies.
            if row['mercury_importednote'] == "Got from Candidate resume":
                logger.info(f"In {s_key} from candidate resume found ---- {skill}  ")
                break
            code, response = associate_vacancy_with_skill(token, dataverse_url, vacancyid, skillid, weight, logger = logger)
            if code == False:
                if response.status_code == 204 or response.status_code == 200:
                    logger.info(f"Successfully associated skill with vacancy. {skill} - {weight}")
                else:
                    raise DataverseError(f"file - associate_add_skills_to_vacancy -  \n associating error. Status code: {response.status_code} - \nResponse:, {response.text}")
        else:
            logger.info(f"In {s_key} not found ---- {skill}")
def update_vacancy_tags_batch(token, dataverse_url, vacancy_id, ids_norm, ids_high, logger = None):
    if logger == None:
        logger = getGlobalAppLogger()
    import uuid
    batch_boundary = f"batch_{uuid.uuid4().hex}"
    
    batch_url = f"{dataverse_url}/api/data/v9.1/$batch"
    headers = {
    "Authorization": f"Bearer {token.get_token()}",
    "Content-Type": f"multipart/mixed;boundary={batch_boundary}"
    }
    
    # Build the batch request body

    batch_body = []
    
    url = f"{dataverse_url}/api/data/v9.1/crimson_vacancies({vacancy_id})/mercury_desirable_tags/$ref"
    context_url = f"{dataverse_url}/api/data/v9.1/$metadata#$ref"

    for id1 in ids_norm:
        if id1 != 0:
            update_data = {
                f"@odata.context": context_url,
                f"@odata.id": f"mercury_tags({id1})"
            }
            # Add the individual request to the batch body
            batch_body.append(
                f"--{batch_boundary}\n"
                f"Content-Type: application/http\n"
                f"Content-Transfer-Encoding: binary\n\n"
                f"POST {url} HTTP/1.1\n"
                f"Content-Type: application/json\n\n"
                f"{json.dumps(update_data)}\n"
            )

    url = f"{dataverse_url}/api/data/v9.1/crimson_vacancies({vacancy_id})/mercury_mandatory_tags/$ref"

    for id2 in ids_high:
        if id2 != 0:
            update_data = {
                f"@odata.context": context_url,
                f"@odata.id": f"mercury_tags({id2})"
            }
            # Add the individual request to the batch body
            batch_body.append(
                f"--{batch_boundary}\n"
                f"Content-Type: application/http\n"
                f"Content-Transfer-Encoding: binary\n\n"
                f"POST {url} HTTP/1.1\n"
                f"Content-Type: application/json\n\n"
                f"{json.dumps(update_data)}\n"
            )

    # Add the final boundary to close the batch
    #batch_body.append(f"--{batch_boundary}\n")

    # Join all parts of the batch body
    batch_request_body = "\r\n".join(batch_body)
    # logger.info(batch_request_body)
    # Send the batch request
    response = requests.post(batch_url, headers=headers, data=batch_request_body)
    if response.status_code != 200:
        raise DataverseError(f"function - update_subcat_field -  \nFailed update cat2. Status code: {response.status_code} - \nResponse:, {response.text}")
        

    return response

def add_tags_master(token, dataverse_url, remaining_tags, skill_tags, sub_cat, logger = None):
    if logger == None:
        logger = getGlobalAppLogger()
    tags_to_add = []
    tag_ids = []
    for skill in skill_tags:
        for rem_tag in remaining_tags:
            if rem_tag == skill['mercury_name'] and (skill['weight'] == 'high' or skill['weight'] == 'medium'):
                tags_to_add.append(skill)
    logger.info(f"skills to be added are {tags_to_add}")
    tag_ids = add_skill_tags_batch (token, dataverse_url, tags_to_add, sub_cat, logger= logger)
    return tag_ids

def pick_highest_weight(weight1, weight2):
    """
    Compare two weights ('high', 'medium', 'normal') and return the highest.
    """
    
    if weight1 == "high" or weight2 == "high":
        return "high"
    elif weight1 == "medium" or weight2 == "medium":
        return "medium"
    else:
        return "normal"
def associate_add_skills_to_vacancy_batch_weights(token, dataverse_url, vacancyid, skills_list, w_data, sub_cat, logger = None):
    if logger == None:
        logger = getGlobalAppLogger()
    skill_tags = []
    p_ts = "Skills > technical skills"
    p_tnp = "Skills > tools and platforms"
    p_ss = "Skills > soft skills"

    data_parent = find_tags_batch(token, dataverse_url, [p_ss, p_ts, p_tnp], logger = logger)
    records = data_parent.get("value", [])
    for rec in records:
        if rec['mercury_name'] == "soft skills":
            ss_id = rec['mercury_tagid']
        elif rec['mercury_name'] == "technical skills":
            ts_id = rec['mercury_tagid']
        elif rec['mercury_name'] == "tools and platforms":
            tnp_id = rec['mercury_tagid']
        else:
            raise



    for skill_ts in skills_list['technical skills']:
        fullname = p_ts + " > " + skill_ts['name']
        weight1 = search_skill_in_key(w_data, 'technical skills', skill_ts['name'])
        weight = pick_highest_weight(weight1, skill_ts['weight'])
        #logger.info(f"tech skill is {skill_ts['name']} - subcat weight is {weight1} - job weight is {skill_ts['weight']} - final {weight}")
        skill_tags.append({"mercury_name": skill_ts['name'], "mercury_fullname": fullname,  "p_id": ts_id, 'weight': weight})
    
    for skill_tp in skills_list['tools and platforms']:
        fullname = p_tnp + " > "  + skill_tp['name']
        weight1 = search_skill_in_key(w_data, 'tools and platforms', skill_tp['name'])
        weight = pick_highest_weight(weight1, skill_tp['weight'])
        #logger.info(f"TP is {skill_tp['name']} - subcat weight is {weight1} - job weight is {skill_tp['weight']} - final {weight}")
        skill_tags.append({"mercury_name": skill_tp['name'], "mercury_fullname": fullname, "p_id": tnp_id,'weight': weight})
    
    for skill_ss in skills_list['soft skills']:
        fullname = p_ss + " > "  + skill_ss['name']
        weight1 = search_skill_in_key(w_data, 'soft skills', skill_ss['name'])
        weight = pick_highest_weight(weight1, skill_ss['weight'])
        #logger.info(f"SS is {skill_ss['name']} - subcat weight is {weight1} - job weight is {skill_ss['weight']} - final {weight}")
        skill_tags.append({"mercury_name": skill_ss['name'], "mercury_fullname": fullname, "p_id": ss_id, 'weight': weight})

    skill_fullnames_normal = [skill["mercury_fullname"] for skill in skill_tags if skill['weight'] == "normal"]

    skill_fullnames_high = [skill["mercury_fullname"] for skill in skill_tags if skill['weight'] == "high" or skill['weight'] == "medium"]
    if skill_fullnames_high != []:
        data_high = find_tags_batch(token, dataverse_url, skill_fullnames_high, logger = logger)
        tags_ids_high = [record["mercury_tagid"] for record in data_high.get("value", [])]
        tags_high_found = [record["mercury_name"] for record in data_high.get("value", [])]
    else:
        tags_ids_high = []
        tags_high_found  = []

    if skill_fullnames_normal != []:
        data_norm = find_tags_batch(token, dataverse_url, skill_fullnames_normal, logger = logger)

        tags_ids_norm = [record["mercury_tagid"] for record in data_norm.get("value", [])]
        tags_norm_found = [record["mercury_name"] for record in data_norm.get("value", [])]
    else:
        tags_ids_norm = []
        tags_norm_found = []
    
    logger.info(f"tags found  normal {len(tags_ids_norm)} - {tags_norm_found}")
    logger.info(f"tags ids found - normal {tags_ids_norm}")
    logger.info(f"tags found  high {len(tags_ids_high)} - {tags_high_found}")
    logger.info(f"tags ids found - normal {tags_ids_high}")
    all_tags = [skill["mercury_name"] for skill in skill_tags]
    # Combine high_tags and normal_tags
    included_tags = set(tags_high_found + tags_norm_found)

    # Find tags that are not in included_tags
    remaining_tags = [tag for tag in all_tags if tag not in included_tags]
    logger.info(f"tags not found in tag database {remaining_tags}")
    #Add only high and medium tags to master
    tag_ids_toadd_high = add_tags_master(token, dataverse_url, remaining_tags, skill_tags, sub_cat, logger = logger)

    tags_ids_high = set(tags_ids_high + tag_ids_toadd_high)

    update_vacancy_tags_batch(token, dataverse_url, vacancyid, tags_ids_norm, tags_ids_high, logger = logger )

def associate_add_skills_to_vacancy_batch(token, dataverse_url, vacancyid, skills_list, w_data, logger = None):
    if logger == None:
        logger = getGlobalAppLogger()
    skill_tags = []
    p_ts = "Skills > technical skills"
    p_tnp = "Skills > tools and platforms"
    p_ss = "Skills > soft skills"
    for skill_ts in skills_list['technical skills']:
        fullname = p_ts + " > " + skill_ts
        weight = search_skill_in_key(w_data, 'technical skills', skill_ts)
        skill_tags.append({"mercury_name": skill_ts, "mercury_fullname": fullname, 'weight': weight})
    for skill_tp in skills_list['tools and platforms']:
        fullname = p_tnp + " > "  + skill_tp
        weight = search_skill_in_key(w_data, 'tools and platforms', skill_tp)
        skill_tags.append({"mercury_name": skill_tp, "mercury_fullname": fullname, 'weight': weight})
    for skill_ss in skills_list['soft skills']:
        fullname = p_ss + " > "  + skill_ss
        weight = search_skill_in_key(w_data, 'soft skills', skill_ss)
        skill_tags.append({"mercury_name": skill_ss, "mercury_fullname": fullname, 'weight': weight})

    skill_fullnames_normal = [skill["mercury_fullname"] for skill in skill_tags if skill['weight'] == "normal"]

    skill_fullnames_high = [skill["mercury_fullname"] for skill in skill_tags if skill['weight'] == "high" or skill['weight'] == "medium"]
    if skill_fullnames_high != []:
        data_high = find_tags_batch(token, dataverse_url, skill_fullnames_high, logger= logger)
        tags_ids_high = [record["mercury_tagid"] for record in data_high.get("value", [])]
        tags_high_found = [record["mercury_name"] for record in data_high.get("value", [])]
    else:
        tags_ids_high = []
        tags_high_found  = []

    if skill_fullnames_normal != []:
        data_norm = find_tags_batch(token, dataverse_url, skill_fullnames_normal, logger= logger)

        tags_ids_norm = [record["mercury_tagid"] for record in data_norm.get("value", [])]
        tags_norm_found = [record["mercury_name"] for record in data_norm.get("value", [])]
    else:
        tags_ids_norm = []
        tags_norm_found = []
    
    logger.info(f"tags found  normal {len(tags_ids_norm)} - {tags_norm_found}")
    logger.info(f"tags ids found - normal {tags_ids_norm}")
    logger.info(f"tags found  high {len(tags_ids_high)} - {tags_high_found}")
    logger.info(f"tags ids found - normal {tags_ids_high}")
    all_tags = [skill["mercury_name"] for skill in skill_tags]
    # Combine high_tags and normal_tags
    excluded_tags = set(tags_high_found + tags_norm_found)

    # Find tags that are not in excluded_tags
    remaining_tags = [tag for tag in all_tags if tag not in excluded_tags]
    logger.info(f"tags not found in tag database {remaining_tags}")

    update_vacancy_tags_batch(token, dataverse_url, vacancyid, tags_ids_norm, tags_ids_high, logger= logger )



# Step 3: Update an existing row
def update_vacancy_row_in_dataverse(token, dataverse_url, row_id, updated_data):
    headers = {
        'Authorization': f'Bearer {token.get_token()}',
        'Content-Type': 'application/json',
        'OData-MaxVersion': '4.0',
        'OData-Version': '4.0'
    }

    # API endpoint for updating a row (singular name of the entity with the row's GUID)
    url = f"{dataverse_url}/api/data/v9.1/crimson_vacancies({row_id})"
    
    response = requests.patch(url, headers=headers, data=json.dumps(updated_data))
    return response


def construct_job_info(row, adverttext2_passed, skipcomma=True, logger=None):

    vacancy_refno = row["crimson_vacancyrefno"]
    job_description = ""
    category = ""
    sub_cat = ""
    job_title = ""


    job_title = row["crimson_jobtitle"]
    if row["recruit_mandatorytagcontrol2"] is not None:
        sub_cat  = row["recruit_mandatorytagcontrol2"]
        if (skipcomma and sub_cat.find(',') != -1):
            #found comma, ignore for now
            logger.info(f"ignoring subcategory with comma - {sub_cat}")
            sub_cat = ""
            return {
                'vacancy_id':'',
                'cat': "SubCategory found with comma",
                'sub_cat': "",
                'job_title': "",
                'job_description': ""
            }
    else:
        logger.info(f"subcategory not found for {vacancy_refno}")
        return {
                'vacancy_id':'',
                'cat': "SubCategory not found",
                'sub_cat': "",
                'job_title': "",
                'job_description': ""
        }
        
    if row["recruit_mandatorytagcontrol0"] is not None:
        category = row["recruit_mandatorytagcontrol0"]
        if (skipcomma and category.find(',') != -1):
            #found comma, ignore for now
            logger.info(f"ignoring category with comma - {category}")
            sub_cat = ""
            return {
                'vacancy_id':'',
                'cat': "Category found with comma",
                'sub_cat': "",
                'job_title': "",
                'job_description': ""
            }
    else:
        logger.info(f"category not found for {vacancy_refno}")
        return {
            'vacancy_id':'',
            'cat': "Category not found",
            'sub_cat': "",
            'job_title': "",
            'job_description': ""
        }
    """ 
    if row["crimson_jobsummaryemail"] is not None:
        job_description = row["crimson_jobsummaryemail"]
        
    if row["crimson_jobsummary"] is not None:
        job_description += "\n"
        job_description += row["crimson_jobsummary"]
        
    if row["mercury_emaildescription"] is not None:
        job_description += "\n"
        job_description += row["mercury_emaildescription"]
    
    """
    if row["recruit_adverttext2"] is not None:
        #Remove the template from the adverttext2
        if adverttext2_passed is not None:
            adverttext2 = re.sub('<.*?>', '', adverttext2_passed)
        else:
            adverttext2 = re.sub('<.*?>', '', row["recruit_adverttext2"])
        job_description += "\n"
        job_description += adverttext2
    """ 
    if row["recruit_adverttext3"] is not None:
        adverttext3 = re.sub('<.*?>', '', row["recruit_adverttext3"])
        job_description += "\n"
        job_description += adverttext3
    """ 
    return {
        'vacancy_id': row["crimson_vacancyid"],
        'cat': category,
        'sub_cat': sub_cat,
        'job_title': job_title,
        'job_description': job_description
    }    

def populate_vacancy_tags(env, rerun=False, logger=None, vacancy_req_no=None):
    if logger == None:
        logger = getGlobalAppLogger()

    if env == 0:
        enum_env = Environment.SANDBOX  # Prod
        postgres_env = PostgresEnvironment.DEV
       
    elif env == 1:
        enum_env = Environment.UAT  # Prod
        postgres_env = PostgresEnvironment.UAT
        
    else:
        enum_env = Environment.PROD  # Prod
        postgres_env = PostgresEnvironment.PROD
       

    token = get_token_for_env(enum_env, logger=logger)
    credentials = get_dataverse_credentials_for_env(enum_env, logger=logger)
    dataverse_url = credentials["RESOURCE_URL"]

    
    db_connector = None

    db_connector = GlobalDBConnector.get_connector(postgres_env, logger=getGlobalAppLogger())
    db_connector.connect()
    if db_connector is None:
        logger.error("Failed to connect to postgres")


    #DB TODO get created_on date as well
    # fields = ['crimson_vacancyrefno', 'recruit_mandatorytagcontrol2', 'crimson_vacancyid', 'recruit_numericuserfield5', 'crimson_vacancyid', 'createdon']
    fields = DV_COLUMNS_TO_FETCH
    
    if vacancy_req_no is not None:
        whereClause = f"crimson_vacancyrefno eq '{vacancy_req_no}'" #For single vacancy just process again.
    else:
        whereClause = f"statecode eq 0 and recruit_mandatorytagcontrol2 ne null and recruit_numericuserfield5 eq null"
        #we are adding template to all vacancies so not checking for recruit_numericuserfield5
        whereClause = f"statecode eq 0 and recruit_mandatorytagcontrol2 ne null"

    # additional_headers = {
    #         'Prefer': 'odata.include-annotations="OData.Community.Display.V1.FormattedValue"'
    # }
    expand = "owninguser($select=domainname),crimson_deliveryownerid($select=domainname),crimson_clientid($select=name)"
    rep = read_fields_from_dataverse(token, dataverse_url, "crimson_vacancy", fields, whereClause, logger=logger, expand=expand, additional_headers=None)
    rows = rep['value']
    logger.debug(json.dumps(rows, indent=4))

    total_count = len(rows)

    # Print the total count
    logger.info(f" number of open vacancies - {total_count}")

    # Initialize SendGrid helper for email notifications
    try:
        email_helper = SendGridEmailHelper(logger=logger)
        logger.info("SendGrid Email Helper initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize SendGrid Email Helper: {e}")
        email_helper = None

    #loop to check every entry 

    for row in rows:
        ref_no = row['crimson_vacancyrefno']
        vacancy_id = row['crimson_vacancyid']
        advert_text2 = row['recruit_adverttext2']
        processed = row['recruit_numericuserfield5']
        advert_text_minus_template = ""
        ret_value = ""
        if processed is None:
            processed = 0
        
        #populate job template
        #processed - digit 0 is used for old tagging and digit 10 is for template processing.
        if processed < 10:
            logger.info(f"running populate job template for {ref_no}")
            ret_value, ret_text = populate_job_template(token, dataverse_url, advert_text2, vacancy_id, logger)
            processed = processed + 10
            response = update_vacancy_row_in_dataverse(token, dataverse_url, vacancy_id, {'recruit_numericuserfield5': processed})
            if response.status_code != 204:
                logger.error(f"Failed to update vacancy recruit_numericuserfield5 for {ref_no}: {response.status_code}, {response.text}")

        else:
            logger.info(f"skipping populate job template for {ref_no} as it is already processed")
            

        # Read owning user and delivery owner fields from Dataverse row
        # This is needed to notify the owner of the vacancy about the search match results
        owninguser = row['owninguser']['domainname'] if row['owninguser'] is not None else ""
        deliveryowner = row['crimson_deliveryownerid']['domainname'] if row['crimson_deliveryownerid'] is not None else ""

        #Check subategory and category, if their is an issue send email to the user.

        error_code = 0
        new_job_data = {}
        #Check is catalyst match is enable.
        if row['recruit_booleanuserfield3'] == True  :
            
            if processed % 10 != 1:
                success, advert_text_minus_template = extract_between_markers(advert_text2, False)
                if len(advert_text_minus_template) > 10:
                    process_vacancy_old(token, dataverse_url, row, advert_text_minus_template, db_connector, logger)
            else:
                logger.info(f"skipping process vacancy old for {ref_no} as it is already processed")
            
            #Check if we generated job template json
          
            category_cache = CategoryCache(logger, db_connector)
            category_id = category_cache.get_category_id(row['recruit_mandatorytagcontrol0'])
            subcategory_id = category_cache.get_subcategory_id(row['recruit_mandatorytagcontrol2'])
            vacancy_db = VacancyDB(logger, db_connector)
            new_vacancy_db_data = vacancy_db.retrieve_vacancy_from_db(vacancy_id)
            if 'job_template' in new_vacancy_db_data and not rerun: #Only run when rerun is true
                logger.info(f"job template already exists for {ref_no}")
            else:
                logger.info(f"catalyst match is enabled for {ref_no}")
                if ret_value == "":
                    ret_value, ret_text = populate_job_template(token, dataverse_url, advert_text2, vacancy_id, logger)
                if ret_value == "job template exists":
                    #Pass the client to add to the template before sending to chatgpt
                    client_name = row['crimson_clientid']['name']
                    error_code, json_text = extract_validate_job_template(advert_text2, client_name, row['recruit_mandatorytagcontrol0'], logger)
                    logger.info(f"json text is {json_text}")
                if ret_value != "job template exists" or error_code > 0:
                    error_text = "Missing items in the job template"
                    if error_code == 1:
                        error_text = "Missing location and years of experience"
                    if error_code == 2:
                        error_text = "Missing technical skills"
                    if error_code == 3:
                        error_text = "Missing certifications and degrees"
                    if error_code == 4:
                        error_text = "Missing confidential, industry or recency of must have skills"
                    if error_code == 5:
                        error_text = "State is unknown"
                    #turn off catalyst match
                    new_row_data = {'recruit_booleanuserfield3': False}
                    response = update_vacancy_row_in_dataverse(token, dataverse_url, vacancy_id, new_row_data)
                    if response.status_code != 204:
                        logger.error(f"Failed to turn off catalyst match: {response.status_code}, {response.text}")
                    logger.info(f"Turned off catalyst match for {ref_no} - error text is {error_text}")
                    #Send email to the user with the error text.
                    if email_helper is not None:
                        try:
                            # Send template error notification
                            send_template_error_notification(
                                email_helper=email_helper,
                                dataverse_url=dataverse_url,
                                owninguser=owninguser,
                                deliveryowner=deliveryowner,
                                vacancy_ref=ref_no,
                                vacancy_title=row['crimson_jobtitle'],
                                vacancy_id=vacancy_id,
                                error_text=error_text,
                                logger=logger
                            )
                        except Exception as email_error:
                            logger.error(f"Failed to send template error notification for {ref_no}: {email_error}")
                    else:
                        logger.warning(f"Email helper not available - skipping template error notification for {ref_no}")

                    continue
                
                
                #Add this meta data to the database.
                
                new_job_data['job_template'] = {}
                new_job_data['job_template']['job_location'] = json_text['job location']
                new_job_data['job_template']['industry'] = json_text['industry']
                new_job_data['job_template']['technical_skills'] = json_text['technical skills']
                new_job_data['job_template']['tools_and_platforms'] = json_text['tools & platforms']
                new_job_data['job_template']['degrees_and_certifications'] = json_text['degrees and certifications']
                new_job_data['job_template']['years_of_experience'] = json_text['years of experience']
                new_job_data['job_template']['confidential'] = json_text['confidential']
                new_job_data['job_template']['recency_of_must_have_skills'] = json_text['recency of must have skills']
                new_job_data['job_template']['client_name_industry'] = json_text['client name industry']['name']
                new_job_data['job_template']['client_naics_code'] = json_text['client name industry']['naics_code']
                logger.info(f"new job data is {new_job_data}")
                
                category_cache = CategoryCache(logger, db_connector)
                category_id = category_cache.get_category_id(row['recruit_mandatorytagcontrol0'])
                subcategory_id = category_cache.get_subcategory_id(row['recruit_mandatorytagcontrol2'])
                vacancy_db = VacancyDB(logger, db_connector)
                new_vacancy_db_data = vacancy_db.retrieve_vacancy_from_db(vacancy_id)
                if new_vacancy_db_data is not None:
                    new_vacancy_db_data['job_template'] = new_job_data['job_template']
                else:
                    logger.error(f"should not happen, vacancy should already exists in the database")

                logger.info(f"vacancy id is {vacancy_id}, {category_id}, {subcategory_id}, owninguser is {owninguser}, deliveryowner is {deliveryowner}")

                
                ret_vac = vacancy_db.insert_vacancy(new_vacancy_db_data, category_id=category_id, subcategory_id=subcategory_id)
                if ret_vac is not None:
                    logger.info(f"job template is inserted in the database for {ref_no}")
                else:
                    logger.info(f"job template is not inserted in the database for {ref_no}")
            
            

            """ 
            if json_text['confidential'] == "No":
            #Getting again in case didn't
            if advert_text_minus_template == "":
                success, advert_text_minus_template = extract_between_markers(advert_text2, False)
                #Create external po sting.

                ret, job_posting_text = create_external_posting(token, dataverse_url, vacancy_id, advert_text_minus_template, logger)
                if not ret:
                    logger.error(f"Failed to create external posting for {ref_no} as advertext1 data is missing")
                    continue
                #Next post in Shazamme. Use the 
            """ 
def process_vacancy_old(token, dataverse_url, row, advert_text_minus_template, db_connector, logger=None):
    #print(f" vac id is {row['crimson_vacancyid']},  processed is {row['recruit_numericuserfield5']}")
    processed = row['recruit_numericuserfield5']
    ref_no = row['crimson_vacancyrefno']
    vacancy_id = row['crimson_vacancyid']
    client_name = row['crimson_clientid']['name']
    owninguser = row['owninguser']['domainname'] if row['owninguser'] is not None else ""
    deliveryowner = row['crimson_deliveryownerid']['domainname'] if row['crimson_deliveryownerid'] is not None else ""

    #For each vacancy
    # get vacancy or job related information from the database now that we
    # have the job code
    # job_info contains cat, subcat, job_title and job_description
    # Commented out as this is redundant call to Dataverse
    # job_info = get_sub_category_from_dv(ref_no, enum_env, logger=logger) 
    logger.info(f"processing - {row}")
    job_info = construct_job_info(row, advert_text_minus_template, logger=logger)
    # logger.info(job_info)

    # Send job description to OpenAI and get data in structured format
    # "soft skills", "technical skills", "tools and platforms", "Degrees and certification"
    #job_data = parse_jobdescription(job_info)
    job_data = parse_jobdescription_w_weights(job_info)
    job_data['refno'] = ref_no
    job_data['vacancy_id'] = vacancy_id
    job_data['sub_cat'] = row['recruit_mandatorytagcontrol2']
    job_data['client name'] = client_name
    sub_cat = row['recruit_mandatorytagcontrol2']

    
    subcat_skills = SubcatSkills(logger, db_connector)
    w_s_data = subcat_skills.get_subcat_skills(sub_cat)

    vacancyid = row['crimson_vacancyid']
    created_on = row['createdon']
    logger.debug(json.dumps(job_data, indent=4))

    # associate_add_skills_to_vacancy_batch(token, dataverse_url, vacancyid, job_data, w_s_data, logger=logger)
    associate_add_skills_to_vacancy_batch_weights(token, dataverse_url, vacancyid, job_data, w_s_data, sub_cat, logger = logger)


    # update recruit_numericuserfield5 to inform we added skills. Use 1 to indicate that.
    processed = processed + 1
    new_row_data = {'recruit_numericuserfield5': processed} 
    #Update the row.
    response = update_vacancy_row_in_dataverse(token, dataverse_url, vacancyid, new_row_data)
    if response.status_code != 204:
        logger.info(f"Failed to add row: {response.status_code}, {response.text}")


    #Also write to database
    if db_connector is not None:
        category_cache = CategoryCache(logger, db_connector)
        category_id = category_cache.get_category_id(row['recruit_mandatorytagcontrol0'])
        subcategory_id = category_cache.get_subcategory_id(row['recruit_mandatorytagcontrol2'])
        vacancy_db = VacancyDB(logger, db_connector)
        created_timestamp = datetime.fromisoformat(created_on.replace("Z", "+00:00"))

        vacancy_json = {
            "vacancy_id": vacancy_id,
            "category_id": category_id,
            "subcategory_id": subcategory_id,
            "owninguser": owninguser,
            "deliveryowner": deliveryowner,
            **job_data
        }
        vacancy_db.insert_vacancy(vacancy_json, category_id=category_id, subcategory_id=subcategory_id)
    return True
       

def test_vacancy_tags(env, logger = None):
    ref_no = "CR/000193" #Get from Sandbox   
    ref_no = "CR/000146" #from Sandbox
    if env == 0:
        credentials = getSandBoxDataverseCredentials()
        vacancy_data_file = '/mnt/incoming/temp/'
    elif env == 1:
        credentials = getUATDataverseCredentials()
        vacancy_data_file = '/mnt/incoming/temp/'
    else:
        credentials = getProdDataverseCredentials()
        vacancy_data_file = '/mnt/incoming/vacancy-prod/'

    if logger is None:
        logger = getGlobalAppLogger()

    token = get_oauth_token(env, logger=logger)
    # Replace these variables with your actual values
    dataverse_url = credentials["RESOURCE_URL"]

    # check recruit_numericuserfield5 if skills were associated that is 1 other wise it is None.
    vac_row = find_row_in_dataverse(token, dataverse_url,'crimson_vacancy', 'crimson_vacancyrefno', ref_no)
    #Load a job json file
    #job_file = '/mnt/incoming/vacancy-prod/021da54b-d2cd-ef11-8ee9-7c1e524a2275.json'
    job_file = '/mnt/incoming/vacancy-prod/49037707-bcce-ef11-b8e8-7c1e525c7700.json'
    with open(job_file, 'r', encoding='utf-8') as f:
        job_data = json.load(f)
    #get the subcategory and the use the weights file.
    #Project Management
    #DB TODO - fill w_s_data from the database
    data_dir = 'data'
    data_dir = os.path.join(data_dir, '') 
    skills_w_weights_subdir = 'skills_w_weights'
    sub_cat = "Project Management"
    skills_file_w = os.path.join(data_dir, skills_w_weights_subdir, f"skills_{sub_cat}.json")
    #there are inconsistency in mercury so check if the subcategory is correct that is we check if the file exists.
    if os.path.exists(skills_file_w):
        with open(skills_file_w, "r") as json_file:
            w_s_data = json.load(json_file)
    else:
        logger.info(f" subcategory issue as file {skills_file_w} doesn't exist")
    
    vacancyid = vac_row['crimson_vacancyid']

    associate_add_skills_to_vacancy_batch_weights(token, dataverse_url, vacancyid, job_data, w_s_data, sub_cat, logger = logger)


def test_template_error_notification(env, logger=None):
    """Test the template error notification functionality."""
    
    if logger is None:
        logger = getGlobalAppLogger()
    
    # Load environment variables
    load_secrets_env_variables()
    
    # Get environment-specific credentials
    if env == 0:
        credentials = getSandBoxDataverseCredentials()
    elif env == 1:
        credentials = getUATDataverseCredentials()
    else:
        credentials = getProdDataverseCredentials()
    
    dataverse_url = credentials["RESOURCE_URL"]
    
    logger.info("Starting template error notification test")
    
    # Initialize SendGrid helper
    try:
        email_helper = SendGridEmailHelper(logger=logger)
        logger.info("SendGrid Email Helper initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize SendGrid Email Helper: {e}")
        return False
    
    # Test data
    test_data = {
        "owninguser": "<EMAIL>",
        "deliveryowner": "<EMAIL>",
        "vacancy_ref": "CR/503090",
        "vacancy_title": "Senior Software Engineer",
        "vacancy_id": "366ce5b2-48b6-ef11-b8e8-7c1e52462d0b",
        "error_text": "Missing technical skills in job template",
        "dataverse_url": "https://tandymgroup.crm.dynamics.com/"  # Test Dataverse URL
    }
    
    # Test with owning user email
    logger.info("Testing with owning user email...")
    result = send_template_error_notification(
        email_helper=email_helper,
        dataverse_url=test_data["dataverse_url"],
        owninguser=test_data["owninguser"],
        deliveryowner=test_data["deliveryowner"],
        vacancy_ref=test_data["vacancy_ref"],
        vacancy_title=test_data["vacancy_title"],
        vacancy_id=test_data["vacancy_id"],
        error_text=test_data["error_text"],
        logger=logger
    )
    
    if result:
        logger.info("✅ Template error notification test passed with owning user email")
    else:
        logger.error("❌ Template error notification test failed with owning user email")
    
    # Test with fallback to delivery owner email
    logger.info("Testing with fallback to delivery owner email...")
    result = send_template_error_notification(
        email_helper=email_helper,
        dataverse_url=test_data["dataverse_url"],
        owninguser="",  # Empty owning user to test fallback
        deliveryowner=test_data["deliveryowner"],
        vacancy_ref=test_data["vacancy_ref"],
        vacancy_title=test_data["vacancy_title"],
        vacancy_id=test_data["vacancy_id"],
        error_text="Missing location and years of experience",
        logger=logger
    )
    
    if result:
        logger.info("✅ Template error notification test passed with delivery owner fallback")
    else:
        logger.error("❌ Template error notification test failed with delivery owner fallback")
    
    # Test with fallback to default email
    logger.info("Testing with fallback to default email...")
    result = send_template_error_notification(
        email_helper=email_helper,
        dataverse_url=test_data["dataverse_url"],
        owninguser="",  # Empty owning user
        deliveryowner="",  # Empty delivery owner to test default fallback
        vacancy_ref=test_data["vacancy_ref"],
        vacancy_title=test_data["vacancy_title"],
        vacancy_id=test_data["vacancy_id"],
        error_text="Missing certifications and degrees",
        logger=logger
    )
    
    if result:
        logger.info("✅ Template error notification test passed with default email fallback")
    else:
        logger.error("❌ Template error notification test failed with default email fallback")
    
    logger.info("Template error notification test completed")
    return True


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Process vacancy skills')

    parser.add_argument('--vacancy', '-v', type=str, help='Optional vacancy reference number')
    parser.add_argument('--rerun', '-r', type=bool, help='Optional rerun flag')
    parser.add_argument('--vacancy-list', type=str, help='Optional list of vacancy reference numbers (comma-separated)')
    parser.add_argument('env', type=int, help='Environment number (0=Sandbox, 1=UAT, 2=Prod)')
    # To test the template error notification, uncomment the following lines and run the script with the --test-template-error flag
    # parser.add_argument('--test-template-error', action='store_true', help='Test template error notification')

    args = parser.parse_args()

    vacancy_req_no = args.vacancy
    vacancy_list_raw = args.vacancy_list
    env = args.env
    rerun = args.rerun
  

    # Start with Sandbox, then UAT and then Prod

    # This for sandbox, change it to UAT and Prod globally at top
    # env = int(param)
    print(f"environment is {env}")

    # Load environment variables.
    load_secrets_env_variables()
    logger = getGlobalAppLogger_vacancy()
    email_helper = SendGridEmailHelper(logger=logger)
    logger.info("SendGrid Email Helper initialized successfully")

    # To test the template error notification, uncomment the following lines and run the script with the --test-template-error flag
    # if args.test_template_error:
    #     test_template_error_notification(env, logger=logger)
    #     exit(0)

    if vacancy_list_raw:
        # Split comma-separated string into list
        vacancy_list = [v.strip() for v in vacancy_list_raw.split(',') if v.strip()]
        for vacancy in vacancy_list:
            logger.info(f"Processing vacancy: {vacancy}")
            populate_vacancy_tags(env, logger=logger, vacancy_req_no=vacancy, rerun=rerun)
    else:
        # If single vacancy provided or none
        populate_vacancy_tags(env, logger=logger, vacancy_req_no=vacancy_req_no, rerun=rerun)

    # print(f"Access Token: {token}")
    # test_vacancy_tags(env, logger=logger)    
