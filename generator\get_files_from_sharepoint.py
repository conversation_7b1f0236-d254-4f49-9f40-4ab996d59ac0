from office365.runtime.auth.client_credential import ClientCredential
from office365.sharepoint.client_context import ClientContext
from office365.sharepoint.files.file import File
from office365.sharepoint.folders.folder import Folder
from office365.runtime.client_request_exception import ClientRequestException
import os
from common.appLogger import getG<PERSON>bal<PERSON><PERSON><PERSON>og<PERSON>, getGlobalAppLogger_candidate
from datetime import datetime
import time

def getUATsharepointCreds():
    credentials = {}
    credentials["site_url"] = "https://execusearchgroup.sharepoint.com/sites/MercuryUAT"
    credentials["client_id"] = os.getenv('AZURE_SHAREPOINT_UAT_CLIENT_ID')
    credentials["client_secret"] = os.getenv('AZURE_SHAREPOINT_UAT_CLIENT_SECRET')
    return credentials

def getSandboxsharepointCreds():
    credentials = {}
    credentials["site_url"] = "https://execusearchgroup.sharepoint.com/sites/MercurySandbox"
    credentials["client_id"] = os.getenv('AZURE_SHAREPOINT_SANDBOX_CLIENT_ID')
    credentials["client_secret"] = os.getenv('AZURE_SHAREPOINT_SANDBOX_CLIENT_SECRET')
    return credentials

def getProdsharepointCreds():
    credentials = {}
    credentials["site_url"] = f"https://execusearchgroup.sharepoint.com/sites/Mercury"
    credentials["client_id"] = os.getenv('AZURE_SHAREPOINT_PROD_CLIENT_ID')
    credentials["client_secret"] = os.getenv('AZURE_SHAREPOINT_PROD_CLIENT_SECRET')
    return credentials

def getKeyVaultsharepointCreds():
    credentials = {}
    credentials["site_url"] = os.getenv('AZURE_SHAREPOINT_SITE_URL')
    credentials["client_id"] = os.getenv('AZURE_SHAREPOINT_CLIENT_ID')
    credentials["client_secret"] = os.getenv('AZURE_SHAREPOINT_CLIENT_SECRET')
    return credentials

# --- Refactored Functions ---
def get_file_list_from_sharepoint(env, cv_url, logger = None):
    """
    Connects to SharePoint using environment-specific credentials,
    retrieves the file list from the folder indicated by cv_url, and
    returns the SharePoint context, list of files, and the extracted cv_basename.
    """
    if logger == None:
        logger = getGlobalAppLogger()
    if env == 0:
        credentials = getSandboxsharepointCreds()
    elif env == 1:
        credentials = getUATsharepointCreds()
    elif env == 2:
        credentials = getProdsharepointCreds()
    else:
        credentials = getKeyVaultsharepointCreds()
    
    site_url = credentials["site_url"]
    client_id = credentials["client_id"]
    client_secret = credentials["client_secret"]
    
    cred = ClientCredential(client_id, client_secret)
    ctx = ClientContext(site_url).with_credentials(cred)

    cv_foldername = os.path.dirname(cv_url)
    cv_basename = os.path.basename(cv_url)
    logger.debug(f"Modified URL Directory: {cv_foldername}, File: {cv_basename}")

    folder = ctx.web.get_folder_by_server_relative_url(cv_foldername)
    files = folder.files
    ctx.load(files)
    execute_with_retry(ctx, logger)

    return ctx, files, cv_basename

 # seconds

def execute_with_retry(ctx, logger):
    MAX_RETRIES = 5
    BASE_BACKOFF = 1 
    backoff = BASE_BACKOFF
    for attempt in range(1, MAX_RETRIES + 1):
        try:
            ctx.execute_query()
            return  # success
        except ClientRequestException as e:
            # If the response exists and is a 503, we retry
            status = getattr(e.response, "status_code", None)
            if status == 503 and attempt < MAX_RETRIES:
                logger.warning(f"503 received, retrying in {backoff}s (attempt {attempt}/{MAX_RETRIES})")
                time.sleep(backoff)
                backoff *= 2  # exponential backoff
                continue
            # For other statuses or if we've exhausted retries, re-raise
            raise

def select_candidate_file(files, cv_basename, logger = None):
    """
    Filters out files that do not have one of the allowed extensions: 
    'doc', 'pdf', 'docx', or 'txt'. Then, from the allowed files, sorts them
    in reverse chronological order and selects a candidate file:
      - It looks for the latest file whose basename (without extension) matches the target.
      - If none is found, returns the newest allowed file overall.
      
    Returns None if no file with an allowed extension is found.
    """
    allowed_extensions = {"doc", "pdf", "docx", "txt"}
    #allowed_extensions = {"doc", "pdf", "docx"}
    
    if logger == None:
        logger = getGlobalAppLogger()
    # Build a filtered list of files with allowed extensions.
    filtered_files = []
    for file in files:
        file_name = file.properties.get('Name', '')
        _, ext = os.path.splitext(file_name)
        ext = ext.lower().strip()[1:]  # Remove the dot and any extra whitespace.
        logger.debug(f"File: {file_name}, Computed extension: '{ext}'")
        if ext in allowed_extensions:
            filtered_files.append(file)
        else:
            logger.debug(f"Skipping file {file_name} due to disallowed extension: {ext}")
    
    if not filtered_files:
        logger.debug("No files with allowed extensions found.")
        return None

    # Build a list of tuples: (file, modification timestamp)
    file_entries = []
    for file in filtered_files:
        file_name = file.properties.get('Name', '')
        ts_val = file.properties.get("TimeLastModified", "")
        if isinstance(ts_val, datetime):
            file_timestamp = ts_val
            ts_str = ts_val.isoformat()
        elif isinstance(ts_val, str):
            ts_str = ts_val.rstrip('Z')
            try:
                file_timestamp = datetime.fromisoformat(ts_str)
            except Exception as e:
                logger.debug(f"Error parsing timestamp '{ts_val}' for file {file_name}: {e}")
                file_timestamp = datetime.min
        else:
            logger.debug(f"Unrecognized timestamp format for file {file_name}: {ts_val}")
            file_timestamp = datetime.min

        logger.debug(f"Allowed File in Folder: {file_name}, Timestamp: {ts_str}")
        file_entries.append((file, file_timestamp))
    
    # Sort allowed file entries by timestamp (newest first).
    file_entries.sort(key=lambda x: x[1], reverse=True)
    
    target_base = os.path.splitext(cv_basename)[0].lower()
    candidate_file = None
    for file, file_timestamp in file_entries:
        file_name = file.properties.get('Name', '')
        base, _ = os.path.splitext(file_name)
        if base.lower() == target_base:
            candidate_file = file
            logger.debug(f"Latest file with '{target_base}' basename found: {file_name}")
            break
    
    if candidate_file is None:
        candidate_file = file_entries[0][0]
        logger.debug(f"No file with '{target_base}' basename found, picking latest allowed file: {candidate_file.properties.get('Name', '')}")
    
    return candidate_file


def download_candidate_file(ctx, candidate_file, logger=None):
    """
    Downloads the candidate file from SharePoint using the provided context.
    Returns a dictionary containing file creation/modification timestamps and content.
    """
    if logger == None:
        logger = getGlobalAppLogger()
    filedata = {}
    filedata["created"] = candidate_file.properties.get("TimeCreated")
    filedata["modified"] = candidate_file.properties.get("TimeLastModified")
    logger.debug(f"Downloading sharepoint file: {candidate_file.properties.get('Name', '')}")
    file_stream = candidate_file.open_binary_stream()
    execute_with_retry(ctx, logger)
    filedata["content"] = file_stream.value
    return filedata


def get_file_metadata_from_sharepoint(env, cv_url, logger = None):
    """
    Retrieves the file list from SharePoint, selects the candidate file based on allowed
    extensions and modification timestamps, and downloads the candidate file.
    Returns a dictionary with file details, or None if no allowed file is found.
    """
    if logger == None:
        logger = getGlobalAppLogger()
    ctx, files, cv_basename = get_file_list_from_sharepoint(env, cv_url, logger=logger)
    candidate_file = select_candidate_file(files, cv_basename, logger=logger)
    if candidate_file is None:
        logger.debug("No candidate file with an allowed extension found.")
        return {}
    filedata = {}
    filedata["created"] = candidate_file.properties.get("TimeCreated")
    filedata["modified"] = candidate_file.properties.get("TimeLastModified")
    return filedata

def get_file_from_sharepoint(env, cv_url, logger = None):
    """
    Retrieves the file list from SharePoint, selects the candidate file based on allowed
    extensions and modification timestamps, and downloads the candidate file.
    Returns a dictionary with file details, or None if no allowed file is found.
    """
    if logger == None:
        logger = getGlobalAppLogger()
    ctx, files, cv_basename = get_file_list_from_sharepoint(env, cv_url, logger=logger)
    candidate_file = select_candidate_file(files, cv_basename, logger=logger)
    if candidate_file is None:
        logger.debug("No candidate file with an allowed extension found.")
        return {}
    filedata = download_candidate_file(ctx, candidate_file, logger=logger)
    return filedata
# --- Test Function ---

def test_select_candidate_file():
    """
    Test the candidate selection logic using a list of dictionaries
    representing file objects.
    This function creates dummy file data (simulating SharePoint file objects)
    using the following files and timestamps:
    
      cv.pdf,          Timestamp: 2025-01-27T20:01:02
      cv_20241028.doc, Timestamp: 2024-10-29T20:18:51
      cv_20241015.doc, Timestamp: 2024-10-28T19:11:34
      cv_20241101.doc, Timestamp: 2024-11-05T21:46:44
      cv_20240825.doc, Timestamp: 2024-09-05T23:21:37
      cv_20241014.doc, Timestamp: 2024-10-15T18:58:12
      cv_20250204.doc, Timestamp: 2025-02-17T04:13:11
      cv_20240727.pdf, Timestamp: 2025-01-27T20:01:02
      tx.txt,          Timestamp: 2025-02-17T04:13:12
      cv_20241008.doc, Timestamp: 2024-10-14T22:37:15
      cv_20240812.doc, Timestamp: 2024-08-25T01:28:11
      cv_20241029.doc, Timestamp: 2024-11-01T18:10:04
      cv_20241123.doc, Timestamp: 2025-02-04T21:11:06
      cv_20240905.doc, Timestamp: 2024-10-08T02:41:36
      hrxml.xml,       Timestamp: 2025-02-17T04:13:13
      cv_20241105.doc, Timestamp: 2024-11-23T20:01:13
      cv.doc,          Timestamp: 2024-02-17T04:13:12
      
    The selection logic filters out files that do not have an allowed extension
    (doc, pdf, docx, or txt), sorts the remaining files by reverse timestamp,
    and picks the latest file with a basename matching "cv". If none match, it
    selects the newest allowed file.
    """
    from datetime import datetime
    import os

    # Define the test data.
    test_files_data = [
        #{"Name": "cv.pdf",          "TimeLastModified": datetime.fromisoformat("2025-01-27T20:01:02"), "TimeCreated": datetime.fromisoformat("2025-01-27T20:01:02")},
        {"Name": "cv_20241028.doc", "TimeLastModified": datetime.fromisoformat("2024-10-29T20:18:51"), "TimeCreated": datetime.fromisoformat("2024-10-29T20:18:51")},
        {"Name": "cv_20241015.doc", "TimeLastModified": datetime.fromisoformat("2024-10-28T19:11:34"), "TimeCreated": datetime.fromisoformat("2024-10-28T19:11:34")},
        {"Name": "cv_20241101.doc", "TimeLastModified": datetime.fromisoformat("2024-11-05T21:46:44"), "TimeCreated": datetime.fromisoformat("2024-11-05T21:46:44")},
        {"Name": "cv_20240825.doc", "TimeLastModified": datetime.fromisoformat("2024-09-05T23:21:37"), "TimeCreated": datetime.fromisoformat("2024-09-05T23:21:37")},
        {"Name": "cv_20241014.doc", "TimeLastModified": datetime.fromisoformat("2024-10-15T18:58:12"), "TimeCreated": datetime.fromisoformat("2024-10-15T18:58:12")},
        {"Name": "cv_20250204.doc", "TimeLastModified": datetime.fromisoformat("2025-02-17T04:13:11"), "TimeCreated": datetime.fromisoformat("2025-02-17T04:13:11")},
        {"Name": "cv_20240727.pdf", "TimeLastModified": datetime.fromisoformat("2025-01-27T20:01:02"), "TimeCreated": datetime.fromisoformat("2025-01-27T20:01:02")},
        {"Name": "tx.txt",          "TimeLastModified": datetime.fromisoformat("2025-02-17T04:13:12"), "TimeCreated": datetime.fromisoformat("2025-02-17T04:13:12")},
        {"Name": "cv_20241008.doc", "TimeLastModified": datetime.fromisoformat("2024-10-14T22:37:15"), "TimeCreated": datetime.fromisoformat("2024-10-14T22:37:15")},
        {"Name": "cv_20240812.doc", "TimeLastModified": datetime.fromisoformat("2024-08-25T01:28:11"), "TimeCreated": datetime.fromisoformat("2024-08-25T01:28:11")},
        {"Name": "cv_20241029.doc", "TimeLastModified": datetime.fromisoformat("2024-11-01T18:10:04"), "TimeCreated": datetime.fromisoformat("2024-11-01T18:10:04")},
        {"Name": "cv_20241123.doc", "TimeLastModified": datetime.fromisoformat("2025-02-04T21:11:06"), "TimeCreated": datetime.fromisoformat("2025-02-04T21:11:06")},
        {"Name": "cv_20240905.doc", "TimeLastModified": datetime.fromisoformat("2024-10-08T02:41:36"), "TimeCreated": datetime.fromisoformat("2024-10-08T02:41:36")},
        {"Name": "hrxml.xml",       "TimeLastModified": datetime.fromisoformat("2025-02-17T04:13:13"), "TimeCreated": datetime.fromisoformat("2025-02-17T04:13:13")},
        {"Name": "cv_20241105.doc", "TimeLastModified": datetime.fromisoformat("2024-11-23T20:01:13"), "TimeCreated": datetime.fromisoformat("2024-11-23T20:01:13")},
        #{"Name": "cv.doc",          "TimeLastModified": datetime.fromisoformat("2025-02-17T04:13:12"), "TimeCreated": datetime.fromisoformat("2024-02-17T04:13:12")},
    ]

    # Dummy wrapper class to simulate a SharePoint file object.
    class DummyFile:
        def __init__(self, properties):
            self.properties = properties

    # Wrap each dictionary in a DummyFile instance.
    test_files = [DummyFile(data) for data in test_files_data]

    # Use "cv" as the target basename.
    candidate = select_candidate_file(test_files, "cv", logger=getGlobalAppLogger_candidate())
    if candidate:
        print("Selected candidate file:", candidate.properties["Name"])
    else:
        print("No candidate file found.")


from common.textExtractor import TextExtractor
from common.secrets_env import load_secrets_env_variables
import json
from datetime import datetime

def main():     
    env = 2
    load_secrets_env_variables()
    #cv_url = "/sites/Mercury/contact/Anna Sunderland_8F730F130BE2EE11904D000D3A578338/CV/cv.pdf"
    cv_url = "/sites/Mercury/contact/Furqan Haq_521926C5594CEF11A317000D3A1345BA/CV/cv.doc"

    filedata = get_file_from_sharepoint(env, cv_url, logger=getGlobalAppLogger_candidate())
    #with open("/mnt/incoming/temp/test_resumes/521926C5594CEF11A317000D3A1345BA.doc", "wb") as f:
    #    f.write(content)
    getGlobalAppLogger().info(len(filedata['content']))
    getGlobalAppLogger().info(filedata['created'])
    getGlobalAppLogger().info(filedata['modified'])

    filedata_without_content = {key: value for key, value in filedata.items() if key != "content"}
    print(json.dumps(filedata_without_content, indent=4, default=lambda o: o.isoformat() if isinstance(o, datetime) else o))
    #getGlobalAppLogger().info(filedata['created1'])
    #getGlobalAppLogger().info(filedata['modified1'])

if __name__ == '__main__':
    main()
    #test_select_candidate_file()
