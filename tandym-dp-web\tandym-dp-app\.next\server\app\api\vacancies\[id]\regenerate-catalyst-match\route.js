const CHUNK_PUBLIC_PATH = "server/app/api/vacancies/[id]/regenerate-catalyst-match/route.js";
const runtime = require("../../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_d6ef95._.js");
runtime.loadChunk("server/chunks/[root of the server]__d958f1._.js");
runtime.loadChunk("server/chunks/_0e4bb1._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/vacancies/[id]/regenerate-catalyst-match/route/actions.js [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/app/api/vacancies/[id]/regenerate-catalyst-match/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
