"use client";

import { useEffect, useState } from "react";
import { Input } from "@/components/ui/input";
import StatsSection from "@/components/pools/StatsSection";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import Loading from "@/components/Loading";
import CategoriesTable from "@/components/workforce-index/CategoriesTable";
import GroupedSubCategoriesTable from "@/components/workforce-index/GroupedSubCategoriesTable";
import { withEntitlementCheck } from "@/utils/withEntitlementCheck";
import { trackedFetch } from "@/library/trackApi";
import { getAppInsights } from "@/library/appInsights";

export interface SubCategoryPool {
  subcategory: string;
  category: string;
  high: number;
  medium: number;
  low: number;
  optimal_count: number;
  unknown: number;
  other: number;
  high_match_address_count: number;
  medium_match_address_count: number;
  high_match_availibility_count: number;
  medium_match_availibility_count: number;
}

export interface SubCategory {
  id: number;
  name: string;
  category_id: number;
  category_name: string;
}

function SubcategoryPools() {
  const [data, setData] = useState<SubCategoryPool[]>([]);
  const [filteredData, setFilteredData] = useState<SubCategoryPool[]>([]);
  const [loading, setLoading] = useState(false);
  const [search, setSearch] = useState("");
  const [sortConfig, setSortConfig] = useState<{
    key: keyof SubCategoryPool | null;
    direction: "asc" | "desc" | null;
  }>({
    key: null,
    direction: null,
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const responseSubCategories = await trackedFetch(
          `/api/subcategories`,
          {},
          { context: "Subcategories" }
        );
        const subCategories: SubCategory[] = await responseSubCategories.json();

        const response = await trackedFetch(
          `/api/pools`,
          {},
          { context: "Pools" }
        );

        if (response.ok) {
          const data: SubCategoryPool[] = await response.json();
          const updatedData = data.map((item) => {
            const subCategoryMatch = subCategories.find(
              (sub) => sub.name === item.subcategory
            );
            return {
              ...item,
              category: subCategoryMatch ? subCategoryMatch.category_name : "",
            };
          });

          setData(updatedData);
          setFilteredData(updatedData);
          getAppInsights()?.trackEvent({
            name: "FE_Workforce_Index_Fetched",
          });
        } else {
          console.error("Failed to fetch data:", response.statusText);
        }
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
      } catch (error: any) {
        console.error("Error fetching data:", error);
        getAppInsights()?.trackException({
          error: new Error("Workforce Index API error: " + error?.message),
          severityLevel: 3,
        });
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, []);

  // Filter Data based on Search
  useEffect(() => {
    const filtered = data.filter((item) =>
      item.subcategory.toLowerCase().includes(search.toLowerCase())
    );
    setFilteredData(filtered);
  }, [search, data]);

  // Handle Sorting
  const handleSort = (key: keyof SubCategoryPool) => {
    let direction: "asc" | "desc" | null = "asc";
    if (sortConfig.key === key && sortConfig.direction === "asc") {
      direction = "desc";
    }

    const sortedData = [...filteredData].sort((a, b) => {
      if (a[key] < b[key]) return direction === "asc" ? -1 : 1;
      if (a[key] > b[key]) return direction === "asc" ? 1 : -1;
      return 0;
    });

    setSortConfig({ key, direction });
    setFilteredData(sortedData);
  };

  return (
    <div className="w-full lg:w-[1260px] 2xl:w-[95%] mx-auto mt-6 p-5 bg-white shadow-lg rounded-lg">
      <h2 className="text-2xl font-bold mb-5 text-center capitalize flex items-center justify-center">
        Workforce Readiness Index
      </h2>
      {/* commented for feature implementation */}
      <StatsSection />
      {/* Search Input */}
      {/* Table Container */}
      <Tabs defaultValue="grouped" className="w-full">
        <TabsList className="grid grid-cols-2 w-[400px] mx-auto mb-5 mt-5">
          <TabsTrigger value="grouped">Go To Market</TabsTrigger>
          <TabsTrigger value="categories">All Sub-Categories</TabsTrigger>
        </TabsList>

        <TabsContent value="categories">
          <div className="mb-4 mt-5 flex justify-between items-center">
            <Input
              type="text"
              placeholder="Search Subcategory..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="w-1/3 border border-gray-300 rounded-lg p-2"
            />
          </div>
          {loading ? (
            <Loading height="h-[37vh]" />
          ) : (
            <CategoriesTable
              handleSort={handleSort}
              loading={loading}
              data={filteredData}
            />
          )}
        </TabsContent>

        <TabsContent value="grouped">
          {loading ? (
            <Loading height="h-[37vh]" />
          ) : (
            <GroupedSubCategoriesTable data={filteredData} />
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default withEntitlementCheck(SubcategoryPools, "Work_force_Index");
