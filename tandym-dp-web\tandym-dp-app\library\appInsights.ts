import { getAppInsightsConnectionString } from "@/app/actions/getAppInsightsConnectionString";
import { ApplicationInsights } from "@microsoft/applicationinsights-web";

let appInsights: ApplicationInsights | null = null;

export async function initAppInsights() {
  const appInsightsConnectionString = await getAppInsightsConnectionString();

  if (!appInsights) {
    const connectionString =
      "InstrumentationKey=" + appInsightsConnectionString || "";
    appInsights = new ApplicationInsights({
      config: {
        connectionString,
        enableAutoRouteTracking: false, // We'll do this manually
      },
    });
    appInsights.loadAppInsights();
  }
  return appInsights;
}

export function getAppInsights() {
  return appInsights;
}
