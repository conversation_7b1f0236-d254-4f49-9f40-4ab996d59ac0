"use client";

import React, { useEffect, useMemo, useState } from "react"; // Removed useEffect
import { Tabs } from "../ui/tabs";
import { TabsList } from "@radix-ui/react-tabs";
import Link from "next/link";
import { But<PERSON> } from "../ui/button";
import { useSkills } from "@/context/SkillsContext";
import { usePathname } from "next/navigation";
import { Drawer } from "@/components/ui/drawer";
import WebHeaderTabs from "@/components/HeaderTabs/WebHeaderTabs";
import MobileHeaderTabs from "./MobileHeaderTabs";
import { useSession, signOut } from "next-auth/react";
import { getInitials } from "@/utils/utils";
import { isADLogin } from "@/api/config";
import { getTabMenus } from "@/utils/tabRoutes";
import { useEntitlement } from "@/context/EntitlementContext";
import { clearUserUuid } from "@/library/utils";
import { getAppInsights } from "@/library/appInsights";
import { trackedFetch } from "@/library/trackApi";

interface HeaderTabsMenuProps {
  routeNameMap: Record<string, string>;
}

interface SubCategory {
  id: number;
  name: string;
  category_id: number;
  category_name: string;
}

const HeaderTabsMenu: React.FC<HeaderTabsMenuProps> = ({ routeNameMap }) => {
  const pathname = usePathname();
  const { data: session } = useSession();
  const userName = session?.user?.name;
  const userEmail = session?.user?.email;
  localStorage.setItem("userName", userEmail || ""); // Store userName in localStorage
  const avatarName = getInitials(userName ?? "");
  const avatarImage = false;

  const isADlogin = isADLogin();
  const { entitlements, isLoaded } = useEntitlement();
  const {
    setIsOpenDiscardModal,
    trackCurrentData,
    setSkillsData,
    isHeaderTabChange,
  } = useSkills(); // Removed setSkillsData

  const [isLogOut, setIsLogOut] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const parsedEntitlement = useMemo(() => {
    if (typeof entitlements === "string") {
      try {
        return JSON.parse(entitlements);
      } catch (error) {
        console.error("Failed to parse entitlement:", error);
        return {};
      }
    }
    return entitlements || {};
  }, [entitlements]);
  // Constants for styling
  const MOBILE_BREAKPOINT = 767;
  const avatarVisible = userName?.length || avatarImage ? true : false;

  // Fetch subcategories data
  useEffect(() => {
    const fetchData = async () => {
      try {
        const responseSubCategories = await trackedFetch(
          `/api/subcategories`,
          {},
          { context: "getSubCategories" }
        );
        const subCategories: SubCategory[] = await responseSubCategories.json();

        setSkillsData(
          subCategories?.sort((a, b) => a.name.localeCompare(b.name))
        );
        getAppInsights()?.trackEvent({
          name: "FE_Subcategories_Fetched",
          properties: {
            email: session?.user?.email,
          },
        });
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
      } catch (error: any) {
        console.error("Error fetching data:", error);
        getAppInsights()?.trackException({
          error: new Error("Subcategories api with error is " + error),
          severityLevel: 3,
        });
      }
    };
    fetchData();
  }, [setSkillsData]);

  const tabs = useMemo(() => {
    if (!isLoaded) return [];
    return getTabMenus(parsedEntitlement, isADlogin);
  }, [parsedEntitlement, isADlogin, isLoaded]);

  const tabsList = () => (
    <>
      {tabs.map((tab, index) => (
        <Tabs key={index} className="flex items-center">
          <TabsList>
            <Link
              href={!isHeaderTabChange ? tab.route : " "}
              onClick={() => {
                if (Object.keys(trackCurrentData).length) {
                  setIsOpenDiscardModal(true);
                } else {
                  setIsMenuOpen(false);
                  isHeaderTabChange && setIsOpenDiscardModal(true);
                }
              }}
            >
              <Button
                className="bg-transparent shadow-none p-0 m-0 hover:bg-transparent"
                variant="ghost"
              >
                <p
                  className={`pb-2 text-base ${
                    tab.route === pathname
                      ? "text-[#2A70EA] font-medium border-b-2 border-[#2A70EA] inline-block"
                      : "text-[#707070] font-light"
                  }`}
                >
                  {routeNameMap[tab.route]}
                </p>
              </Button>
            </Link>
          </TabsList>
        </Tabs>
      ))}
    </>
  );

  const logoutHandler = () => {
    const tenetId = process.env.NEXT_PUBLIC_AZURE_TENANT_ID;
    const postLogoutRedirect =
      process.env.NEXTAUTH_URL ?? "http://tandymgroup.com/";
    const logoutUrl = `https://login.microsoftonline.com/${tenetId}/oauth2/v2.0/logout?post_logout_redirect_uri=${encodeURIComponent(
      postLogoutRedirect
    )}&prompt=select_account`;
    clearUserUuid();
    getAppInsights()?.trackEvent({
      name: `FE_Logout_Button_Clicked`,
    });
    signOut({ redirect: false }).then(() => {
      window.location.href = logoutUrl;
    });
  };

  if (!isLoaded) return null;

  return (
    <Drawer
      direction="left"
      open={isMenuOpen}
      onClose={() => setIsMenuOpen(false)}
      dismissible
    >
      {isMenuOpen ? (
        <MobileHeaderTabs
          setIsMenuOpen={setIsMenuOpen}
          tabsList={tabsList}
          logoutHandler={logoutHandler}
          avatarVisible={avatarVisible}
        />
      ) : (
        <WebHeaderTabs
          avatarImage={avatarImage}
          avatarImageURL="https://images.unsplash.com/photo-*************-f34c92461ad9"
          avatarImageAltText="avatar-image"
          avatarName={avatarName}
          setIsLogOut={setIsLogOut}
          isLogOut={isLogOut}
          setIsMenuOpen={setIsMenuOpen}
          isMenuOpen={isMenuOpen}
          tabsList={tabsList}
          logoutHandler={logoutHandler}
          avatarVisible={avatarVisible}
        />
      )}
    </Drawer>
  );
};

export default HeaderTabsMenu;
