# dpserver/apis/routes.py
from fastapi import APIRouter, HTTPException, Query, Body
from pydantic import BaseModel, EmailStr, Field, field_validator, RootModel # Added RootModel
from dpserver.services.attribute_service import AttributeService
from dpserver.services.subcategory_service import SubcategoryService
from dpserver.services.vacancy_service import VacancyService
from dpserver.services.parse_resume_service import ParseResumeService
from dpserver.services.experiment_service import ExperimentService
from common.appLogger import AppLogger
from dpserver.services.candidate_stats_service import CandidateStatsService # Assuming this is used
from dataverse_helper.token_manager import Environment # Assuming this is used
from common.db.postgres_connector import PostgresConnector
from datetime import datetime
from typing import Dict, Any, Optional, Literal, Union # For type hinting
import uuid # Added for UUID validation
from enum import Enum # Added for status enum

class PromoteResultsPayload(BaseModel):
    run_id: int
    vacancy_refno: str

class ArchiveVacancyPayload(BaseModel):
    vacancy_refno: str

class ShortlistCandidatePayload(BaseModel):
    reviewer_email: EmailStr
    vacancy_id: str = Field(..., description="UUID string for vacancy ID")
    candidate_id: str = Field(..., description="UUID string for candidate ID")
    
    @field_validator('reviewer_email')
    def validate_reviewer_email_domain(cls, value):
        """Validate that reviewer email is from tandymgroup.com domain."""
        if not value.endswith("@tandymgroup.com"):
            raise ValueError('Reviewer email must be from tandymgroup.com domain')
        return value

    @field_validator('vacancy_id')
    def validate_vacancy_id(cls, value):
        """Validate that vacancy_id is a valid UUID."""
        try:
            uuid.UUID(value)
        except ValueError:
            raise ValueError('vacancy_id must be a valid UUID')
        return value
    
    @field_validator('candidate_id')
    def validate_candidate_id(cls, value):
        """Validate that candidate_id is a valid UUID."""
        try:
            uuid.UUID(value)
        except ValueError:
            raise ValueError('candidate_id must be a valid UUID')
        return value
    
    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "reviewer_email": "<EMAIL>",
                    "vacancy_id": "123e4567-e89b-12d3-a456-************",
                    "candidate_id": "987fcdeb-51a2-43d1-9f12-************"
                }
            ],
            "description": """
            Payload for shortlisting a candidate for a vacancy.
            
            The system will:
            1. Validate the reviewer email domain (@tandymgroup.com)
            2. Attempt to shortlist the candidate in Dataverse
            3. Record the operation result in the database with the following structure:
               {
                 "status": "success" | "failed",
                 "shortlisted_at": "ISO timestamp",
                 "shortlisted_by": "reviewer_email",
                 "error_code": "optional error code if failed",
                 "error_message": "optional error message if failed"
               }
            """
        }
    }

class CandidateFitnessReasonPayload(BaseModel):
    vacancy_refno: str
    candidate_contact_id: str
    fitness_reason_text: Optional[str] = None  # Fixed the type annotation
    author_email: str

class VacancyUpdateTimestamps(BaseModel):
    data_last_updated_at: Optional[datetime] = None
    archived: bool

class CatalystMatchStatus(BaseModel):
    status: Optional[Literal['queued', 'inprocess', 'completed', 'error']] = None
    initiated_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    initiated_by: Optional[str] = None

class CatalystMatchStatusResponse(BaseModel):
    catalyst_match_status: CatalystMatchStatus
    update_timestamps: VacancyUpdateTimestamps

class RegenerateCatalystMatchPayload(BaseModel):
    reviewer_email: EmailStr

class ShortlistStatus(str, Enum):
    """Enum for shortlist operation status."""
    SUCCESS = "success"
    FAILED = "failed"
    PENDING = "pending"  # For future use if needed
    REMOVED = "removed"  # For unshortlist operations

class ShortlistedData(BaseModel):
    """Model for shortlisted data stored in database."""
    status: ShortlistStatus
    shortlisted_at: str
    shortlisted_by: str
    crimson_vacancycandidateid: Optional[str] = None

class ShortlistCandidateSuccessResponse(BaseModel):
    """Success response model for shortlist candidate operation."""
    success: Literal[True] = True
    status_code: Literal[200] = 200
    message: str
    vacancy_id: str
    candidate_id: str
    shortlisted_data: ShortlistedData

class ShortlistCandidateFailureResponse(BaseModel):
    """Failure response model for shortlist candidate operation."""
    success: Literal[False] = False
    status_code: int
    message: str
    details: Optional[str] = None
    vacancy_id: str
    candidate_id: str
    shortlisted_data: Optional[ShortlistedData] = None
    error_code: Optional[str] = None
    error_message: Optional[str] = None

class ShortlistCandidateResponse(RootModel[Union[ShortlistCandidateSuccessResponse, ShortlistCandidateFailureResponse]]):
    """Union response model for shortlist candidate operation."""

class VacancyReviewStatsResponse(BaseModel):
    """Response model for vacancy review statistics."""
    vacancy_id: str
    refno: Optional[str] = None
    total_found: int
    total_rated: int
    liked: int
    disliked: int
    
    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "vacancy_id": "3420a14d-b54a-f011-877a-7c1e5218ef9d",
                    "refno": "CR/12345",
                    "total_found": 150,
                    "total_rated": 45,
                    "liked": 30,
                    "disliked": 15
                }
            ],
            "description": """
            Review statistics for a vacancy showing:
            - total_found: Total number of candidates found for the vacancy
            - total_rated: Number of candidates that have been rated by reviewers
            - liked: Number of candidates rated as 'like' or 'maybe'
            - disliked: Number of candidates rated as 'dislike'
            """
        }
    }

class Routes:
    def __init__(self, 
                 logger: AppLogger, 
                 attribute_service: AttributeService, 
                 subcategory_service: SubcategoryService,
                 vacancy_service: VacancyService,
                 parse_resume_service: ParseResumeService,
                 experiment_service: ExperimentService,
                 postgres_db_connector: PostgresConnector, # Matched param name from main.py
                 env: Environment = Environment.PROD): # env from main.py is db_env, this might need clarification
        self.logger = logger
        self.attribute_service = attribute_service
        self.subcategory_service = subcategory_service
        self.vacancy_service = vacancy_service
        self.parse_resume_service = parse_resume_service
        self.experiment_service = experiment_service
        self.postgres_db_connector = postgres_db_connector 
        # The 'env' parameter here is for Dataverse, not the DB env.
        # If CandidateStatsService uses this 'env', it's separate from the DB's PostgresEnvironment.
        self.candidate_stats_env = env # Clarify if this 'env' is for candidate_stats_service
        self.router = APIRouter()
        self._register_routes()

    def _register_routes(self):
        """Register API routes inside the router."""
        @self.router.get("/", response_model=dict)
        async def root():
            return {"message": "Welcome to the DP Server API"}

        # Existing routes...
        @self.router.get("/categories", response_model=dict)
        async def get_categories():
            return self.subcategory_service.get_categories()

        @self.router.get("/categories/{category_id}/subcategories", response_model=dict)
        async def get_subcategories_by_category(category_id: int):
            return self.subcategory_service.get_subcategories_by_category(category_id)

        @self.router.get("/subcategories", response_model=dict)
        async def get_all_subcategories():
            return self.subcategory_service.get_subcategories()

        @self.router.get("/subcategories/{subcategory_id}/category", response_model=dict)
        async def get_category_by_subcategory(subcategory_id: int):
            return self.subcategory_service.get_category_by_subcategory(subcategory_id)
        
        @self.router.get("/subcategory/pools", response_model=list)
        async def get_subcategory_pools():
            return self.subcategory_service.get_pool_data()
        
        @self.router.get("/v1/subcategory/weight-configs", response_model=dict)
        async def get_all_subcategory_weight_configs():
            """
            Get all subcategory weight configurations.
            Returns subcategory_id, subcategory_name, and all weight fields.
            """
            return self.subcategory_service.get_all_subcategory_weight_configs()

        @self.router.put("/v1/subcategory/weight-configs/{subcategory_id}", response_model=dict)
        async def update_subcategory_weight_config(subcategory_id: int, updates: dict):
            """
            Update weight config for a subcategory.
            Ensures the total of all score fields does not exceed 1.
            """
            # You can extract updated_by from auth/session if needed
            updated_by = "api_user"  # Replace this with actual user if available
            return self.subcategory_service.update_subcategory_weight_config(
                subcategory_id=subcategory_id,
                updates=updates,
                updated_by=updated_by
            )
        
        @self.router.get("/weights/{subcategory_id}", response_model=dict)
        async def get_weights_for_subcategory(subcategory_id: int):
            return self.subcategory_service.get_weights_for_subcategory(subcategory_id)

        @self.router.get("/attributes/{subcategory_id}", response_model=dict)
        async def get_attributes_for_subcategory(subcategory_id: int, limit: int = 10, offset: int = 0):
            return self.attribute_service.get_attributes_for_subcategory(subcategory_id, limit, offset)

        @self.router.get("/jobtitles/{subcategory_id}", response_model=dict)
        async def get_job_titles_by_subcategory(subcategory_id: int, limit: int = 10, offset: int = 0):
            return self.attribute_service.get_job_titles_by_subcategory(subcategory_id, limit, offset)

        @self.router.post("/attributes/{subcategory_id}/update", response_model=dict)
        async def update_attributes_weights(subcategory_id: int, new_weights: dict):
            return self.attribute_service.update_attributes_weights(subcategory_id, new_weights)
        
        @self.router.delete("/attribute/delete/{attribute_id}", response_model=dict)
        async def delete_attribute_by_id(attribute_id: int):
            return self.attribute_service.delete_attribute_by_id(attribute_id)
        
        @self.router.put("/attributes/{attribute_id}/subcategory", response_model=dict)
        async def change_subcategory_of_attribute(attribute_id:int,  payload: dict):
            return self.attribute_service.change_subcategory_of_attribute(attribute_id, payload)
        
        @self.router.put("/attributes/{attribute_id}/approval", response_model=dict)
        async def set_attribute_approval_status( attribute_id:int, payload: dict):
            return self.attribute_service.set_attribute_approval_status(attribute_id, payload)
        
        @self.router.get("/vacancies", response_model=dict)
        async def get_vacancies():
            return self.vacancy_service.get_vacancies()
        @self.router.post("/vacancies/{vacancy_id}/start", response_model=dict)
        async def start_vacancy_review(vacancy_id: str, payload: dict):
            """Start a review session for a vacancy."""
            reviewer_email = payload.get("reviewer")
            if not reviewer_email:
                raise HTTPException(status_code=400, detail="reviewer email is required")
            
            result = self.vacancy_service.start_vacancy_review(vacancy_id, reviewer_email)
            if "error" in result:
                # Extract status_code and ensure it's an integer
                status_code = result.get("status_code", 500)
                if isinstance(status_code, str):
                    try:
                        status_code = int(status_code)
                    except ValueError:
                        status_code = 500
                
                raise HTTPException(
                    status_code=status_code,
                    detail={"message": result["error"], "details": result.get("details")}
                )
            return result

        @self.router.post("/vacancies/{vacancy_id}/complete", response_model=dict)
        async def complete_vacancy_review(vacancy_id: str, payload: dict):
            """Complete a review session and lock the vacancy."""
            reviewer_email = payload.get("reviewer")
            if not reviewer_email:
                raise HTTPException(status_code=400, detail="reviewer email is required")
            
            result = self.vacancy_service.complete_vacancy_review(vacancy_id, reviewer_email)
            if "error" in result:
                # Extract status_code and ensure it's an integer
                status_code = result.get("status_code", 500)
                if isinstance(status_code, str):
                    try:
                        status_code = int(status_code)
                    except ValueError:
                        status_code = 500
                
                raise HTTPException(
                    status_code=status_code,
                    detail={"message": result["error"], "details": result.get("details")}
                )
            return result

        @self.router.post("/vacancies/{vacancy_id}/claim", response_model=dict)
        async def claim_vacancy_review(vacancy_id: str, payload: dict):
            """Allow a new reviewer to claim a previously locked vacancy."""
            reviewer = payload.get("reviewer")
            if not reviewer:
                raise HTTPException(
                    status_code=400,
                    detail={"message": "reviewer email is required", "details": "Please provide the email of the new reviewer"}
                )
            
            result = self.vacancy_service.claim_vacancy_review(vacancy_id, reviewer)
            if "error" in result:
                # Extract status_code and ensure it's an integer
                status_code = result.get("status_code", 500)
                if isinstance(status_code, str):
                    try:
                        status_code = int(status_code)
                    except ValueError:
                        status_code = 500
                
                raise HTTPException(
                    status_code=status_code,
                    detail={"message": result["error"], "details": result.get("details")}
                )
            return result

        @self.router.get("/vacancies/{vacancy_id}/status", response_model=dict)
        async def get_vacancy_review_status(vacancy_id: str):
            """Get the current review status of a vacancy."""
            result = self.vacancy_service.get_vacancy_review_status(vacancy_id)
            if "error" in result:
                # Extract status_code and ensure it's an integer
                status_code = result.get("status_code", 500)
                if isinstance(status_code, str):
                    try:
                        status_code = int(status_code)
                    except ValueError:
                        status_code = 500
                
                raise HTTPException(
                    status_code=status_code,
                    detail={"message": result["error"], "details": result.get("details")}
                )
            return result

        @self.router.post("/vacancies/shortlist", 
                         response_model=ShortlistCandidateResponse,
                         summary="Shortlist a candidate for a vacancy",
                         description="""
                         Shortlist a candidate for a specific vacancy.
                         
                         This endpoint:
                         1. Validates the reviewer email domain (@tandymgroup.com)
                         2. Attempts to shortlist the candidate in Dataverse
                         3. Records the operation result in the database
                         4. Returns detailed response with success/failure status
                         
                         The operation is recorded in the database regardless of success/failure
                         to maintain a complete audit trail.
                         """,
                         responses={
                             200: {
                                 "description": "Candidate successfully shortlisted",
                                 "content": {
                                     "application/json": {
                                         "example": {
                                             "success": True,
                                             "status_code": 200,
                                             "message": "Candidate shortlisted successfully in Dataverse and database",
                                             "vacancy_id": "123e4567-e89b-12d3-a456-************",
                                             "candidate_id": "987fcdeb-51a2-43d1-9f12-************",
                                             "shortlisted_data": {
                                                 "status": "success",
                                                 "shortlisted_at": "2024-01-01T12:00:00Z",
                                                 "shortlisted_by": "<EMAIL>",
                                                 "crimson_vacancycandidateid": "abc123-def4-5678-9abc-def123456789"
                                             }
                                         }
                                     }
                                 }
                             },
                             400: {
                                 "description": "Bad request - Dataverse operation failed",
                                 "content": {
                                     "application/json": {
                                         "example": {
                                             "success": False,
                                             "status_code": 400,
                                             "message": "Failed to shortlist candidate in Dataverse: Invalid reviewer email",
                                             "details": "Dataverse operation failed",
                                             "vacancy_id": "123e4567-e89b-12d3-a456-************",
                                             "candidate_id": "987fcdeb-51a2-43d1-9f12-************",
                                             "shortlisted_data": {
                                                 "status": "failed",
                                                 "shortlisted_at": "2024-01-01T12:00:00Z",
                                                 "shortlisted_by": "<EMAIL>",
                                                 "crimson_vacancycandidateid": None
                                             },
                                             "error_code": "dataverse_error",
                                             "error_message": "Invalid reviewer email"
                                         }
                                     }
                                 }
                             },
                             409: {
                                 "description": "Conflict - Candidate already shortlisted",
                                 "content": {
                                     "application/json": {
                                         "example": {
                                             "success": False,
                                             "status_code": 409,
                                             "message": "Candidate is already shortlisted for this vacancy",
                                             "details": "The candidate has already been successfully shortlisted for this vacancy",
                                             "vacancy_id": "123e4567-e89b-12d3-a456-************",
                                             "candidate_id": "987fcdeb-51a2-43d1-9f12-************",
                                             "shortlisted_data": {
                                                 "status": "success",
                                                 "shortlisted_at": "2024-01-01T12:00:00Z",
                                                 "shortlisted_by": "<EMAIL>",
                                                 "crimson_vacancycandidateid": "abc123-def4-5678-9abc-def123456789"
                                             },
                                             "error_code": "already_shortlisted",
                                             "error_message": "Candidate already shortlisted"
                                         }
                                     }
                                 }
                             },
                             500: {
                                 "description": "Internal server error",
                                 "content": {
                                     "application/json": {
                                         "example": {
                                             "success": False,
                                             "status_code": 500,
                                             "message": "Internal server error while shortlisting candidate",
                                             "details": "Unexpected error occurred",
                                             "vacancy_id": "123e4567-e89b-12d3-a456-************",
                                             "candidate_id": "987fcdeb-51a2-43d1-9f12-************"
                                         }
                                     }
                                 }
                             }
                         })
        async def shortlist_candidate_for_vacancy(payload: ShortlistCandidatePayload = Body(...)):
            """Shortlist a candidate for a vacancy."""
            result = self.vacancy_service.shortlist_candidate_for_vacancy(
                payload.reviewer_email, 
                payload.vacancy_id, 
                payload.candidate_id
            )
            
            # Check if the operation was successful
            if not result.get("success", False):
                # Extract status_code and ensure it's an integer
                status_code = result.get("status_code", 500)
                if isinstance(status_code, str):
                    try:
                        status_code = int(status_code)
                    except ValueError:
                        status_code = 500
                
                raise HTTPException(
                    status_code=status_code,
                    detail={
                        "message": result.get("message", "Unknown error occurred"),
                        "details": result.get("details"),
                        "vacancy_id": result.get("vacancy_id"),
                        "candidate_id": result.get("candidate_id"),
                        "shortlisted_data": result.get("shortlisted_data")
                    }
                )
            
            return result
            
        @self.router.get("/files/vacancies", response_model=dict)
        async def get_vacancies_from_files():
            return self.vacancy_service.get_vacancies_from_files()
        @self.router.get("/files/candidate-resume/{contact_id}", response_model=dict)
        async def get_candidate_resume_from_file(contact_id: str):
            return self.vacancy_service.get_candidate_resume_from_file(contact_id)
        
        @self.router.get("/candidates/{vacancy_id}", response_model=dict)
        async def get_candidates_by_vacancy_id(
            vacancy_id: str, 
            reviewer_email: str = Query("", description="Email of the reviewer whose feedback needs to be fetched")
        ):
            return self.vacancy_service.get_candidates_by_vacancy_id(
            vacancy_id=vacancy_id, 
            reviewer_email=reviewer_email
            )

        @self.router.post("/candidates/update", response_model=dict)
        async def update_candidate_decision_in_file( data: dict):
            return self.vacancy_service.update_candidate_decision(data)

        @self.router.post("/candidates/update_in_db", response_model=dict)
        async def update_candidate_decision(data: dict):
            """Update candidate decision with review lock validation."""
            result = self.vacancy_service.update_candidate_decision_in_db(data)
            if "error" in result:
                # Extract status_code and ensure it's an integer
                status_code = result.get("status_code", 500)
                if isinstance(status_code, str):
                    try:
                        status_code = int(status_code)
                    except ValueError:
                        status_code = 500
                
                raise HTTPException(
                    status_code=status_code,
                    detail={"message": result["error"], "details": result.get("details")}
                )
            return result

        @self.router.post("/candidates/fitness_reason", response_model=dict)
        async def update_candidate_fitness_reason(payload: CandidateFitnessReasonPayload = Body(...)):
            """Update or set the fitness reason for a candidate in a vacancy."""
            result = self.vacancy_service.update_candidate_fitness_reason(payload.model_dump())
            if "error" in result:
                # Extract status_code and ensure it's an integer
                status_code = result.get("status_code", 500)
                if isinstance(status_code, str):
                    try:
                        status_code = int(status_code)
                    except ValueError:
                        status_code = 500
                
                raise HTTPException(
                    status_code=status_code,
                    detail={"message": result["error"], "details": result.get("details")}
                )
            return result

        @self.router.get("/vacancies/{vacancy_id}/catalystmatchstatus", response_model=CatalystMatchStatusResponse, summary="Get Catalyst match generation status and timestamps for a vacancy")
        async def get_catalyst_match_status(vacancy_id: str):
            """
            Returns the current Catalyst match status for a given vacancy, 
            including its progress state, timestamps, who initiated the generation,
            and vacancy data update timestamps.
            """
            self.logger.info(f"Fetching Catalyst match status and timestamps for vacancy_id: {vacancy_id}")
            result = self.vacancy_service.get_catalyst_match_status_for_vacancy(vacancy_id)
            
            if "error" in result:
                error_detail = {
                    "error": {
                        "code": str(result.get("status_code", "UnknownError")),
                        "message": result.get("error", "An unknown error occurred.")
                    }
                }
                if "details" in result: # Add details if present
                    error_detail["error"]["details"] = result.get("details")

                raise HTTPException(
                    status_code=result.get("status_code", 500),
                    detail=error_detail
                )
            catalyst_match_status_data = result.get("catalyst_match_status_data", {})
            # Process main catalyst status fields
            initiated_at = catalyst_match_status_data.get("initiated_at")
            completed_at = catalyst_match_status_data.get("completed_at")
            
            # Process nested timestamp fields
            update_timestamps_data = result.get("update_timestamps", {})
            #match_results_generated_at = update_timestamps_data.get("match_results_generated_at")
            data_last_updated_at = update_timestamps_data.get("data_last_updated_at")

            try:
                if isinstance(initiated_at, str):
                    initiated_at = datetime.fromisoformat(initiated_at)
                if isinstance(completed_at, str):
                    completed_at = datetime.fromisoformat(completed_at)
                # if isinstance(match_results_generated_at, str):
                #     match_results_generated_at = datetime.fromisoformat(match_results_generated_at)
                if isinstance(data_last_updated_at, str):
                    data_last_updated_at = datetime.fromisoformat(data_last_updated_at)
            except (ValueError, TypeError) as ve:
                self.logger.error(f"Invalid date format received from service for catalyst match status, vacancy {vacancy_id}: {ve}")
                raise HTTPException(
                    status_code=500,
                    detail={
                        "error": {
                            "code": "500",
                            "message": "Invalid date format received for timestamps."
                        }
                    }
                )

            return CatalystMatchStatusResponse(
                catalyst_match_status = CatalystMatchStatus(
                    status=catalyst_match_status_data.get("status"),
                    initiated_at=initiated_at,
                    completed_at=completed_at,
                    initiated_by=catalyst_match_status_data.get("initiated_by")
                ),
                update_timestamps = VacancyUpdateTimestamps(
                    data_last_updated_at=data_last_updated_at,
                    archived=update_timestamps_data.get("archived", False)
                )
            )

        @self.router.post("/vacancies/{vacancy_id}/regenerate-catalyst-match", 
                          response_model=CatalystMatchStatus, 
                          summary="Regenerate Catalyst match for a vacancy",
                          status_code=202,
                          responses={
                              202: {"description": "Regeneration job successfully queued."},
                              409: {"description": "An active job already exists for this vacancy."}
                          })
        async def regenerate_catalyst_match(vacancy_id: str, payload: RegenerateCatalystMatchPayload = Body(...)):
            """
            Enqueues a job to regenerate the Catalyst match results for a given vacancy.
            A new job will only be queued if there isn't already an active ('queued' or 'inprocess') job for this vacancy.
            """
            self.logger.info(f"Request to regenerate Catalyst match for vacancy_id: {vacancy_id} by {payload.reviewer_email}")
            
            try:
                uuid.UUID(vacancy_id)
            except ValueError:
                raise HTTPException(status_code=400, detail="Invalid vacancy_id format. Must be a valid UUID.")

            result = self.vacancy_service.regenerate_catalyst_match(vacancy_id, payload.reviewer_email)
            
            if "error" in result:
                status_code = result.get("status_code", 500)
                error_detail = {
                    "error": {
                        "code": str(status_code),
                        "message": result.get("error", "An unknown error occurred.")
                    }
                }
                if "details" in result:
                    error_detail["error"]["details"] = result.get("details")

                raise HTTPException(
                    status_code=status_code,
                    detail=error_detail
                )
            
            return CatalystMatchStatus(**result)

        @self.router.get("/vacancies/{vacancy_id}/reviewstats", 
                         response_model=VacancyReviewStatsResponse, 
                         summary="Get vacancy review statistics",
                         description="""
                         Returns review statistics for a given vacancy including:
                         - Total candidates found for the vacancy
                         - Number of candidates rated by reviewers
                         - Number of candidates liked (like/maybe votes)
                         - Number of candidates disliked (dislike votes)
                         """)
        async def get_vacancy_review_stats(vacancy_id: str):
            """
            Returns review statistics for a given vacancy including total candidates found,
            total rated, liked, and disliked counts.
            """
            self.logger.info(f"Fetching review statistics for vacancy_id: {vacancy_id}")
            result = self.vacancy_service.get_vacancy_review_stats(vacancy_id)
            
            if "error" in result:
                # Extract status_code and ensure it's an integer
                status_code = result.get("status_code", 500)
                if isinstance(status_code, str):
                    try:
                        status_code = int(status_code)
                    except ValueError:
                        status_code = 500
                
                raise HTTPException(
                    status_code=status_code,
                    detail={"message": result["error"], "details": result.get("details")}
                )
            
            return result

        # --- Experiment Viewer Routes (New) ---
        @self.router.get("/experiment/vacancies", response_model=Dict[str, Any], summary="List Experiment Vacancies")
        async def list_experiment_vacancies(page: int = Query(1, ge=1), page_size: int = Query(20, ge=1, le=100)):
            return self.experiment_service.get_experiment_vacancies(page=page, page_size=page_size)

        @self.router.post("/experiment/results/promote", response_model=Dict[str, Any], summary="Promote Experiment Results") # Path changed
        async def promote_experiment_results(payload: PromoteResultsPayload = Body(...)): # run_id and vacancy_refno moved to payload
            try:
                result = self.experiment_service.promote_results(run_id=payload.run_id, vacancy_refno=payload.vacancy_refno)
                return result
            except HTTPException as e:
                # Re-raise HTTPException to let FastAPI handle it
                raise e
            except Exception as e:
                # Catch any other unexpected errors from the service
                self.logger.error(f"Error promoting results for run_id {payload.run_id}, vacancy_refno {payload.vacancy_refno}: {e}", exc_info=True)
                raise HTTPException(status_code=500, detail=f"An unexpected error occurred while promoting results: {str(e)}")

        @self.router.post("/experiment/vacancies/archive", response_model=Dict[str, Any], summary="Archive Vacancy by RefNo")
        async def archive_vacancy_by_refno(payload: ArchiveVacancyPayload = Body(...)):
            try:
                result = self.experiment_service.archive_vacancy(vacancy_refno=payload.vacancy_refno)
                return result
            except HTTPException as e:
                raise e
            except Exception as e:
                self.logger.error(f"Error archiving vacancy for refno {payload.vacancy_refno}: {e}", exc_info=True)
                raise HTTPException(status_code=500, detail=f"An unexpected error occurred while archiving vacancy: {str(e)}")

        @self.router.get("/experiment/vacancies/{vacancy_id}/runs/{run_id}/details", response_model=Dict[str, Any], summary="Vacancy Details for a Run")
        async def get_vacancy_run_details(vacancy_id: str, run_id: int): # Renamed for clarity
            return self.experiment_service.get_vacancy_details_for_run(vacancy_id, run_id)

        @self.router.get("/experiment/vacancies/{vacancy_id}/runs/{run_id}/candidates", response_model=Dict[str, Any], summary="Candidates for Vacancy and Run")
        async def list_candidates_for_vacancy_run( # Renamed for clarity
            vacancy_id: str, 
            run_id: int,
            page: int = Query(1, ge=1), 
            page_size: int = Query(20, ge=1, le=100)
        ):
            return self.experiment_service.get_candidates_for_vacancy_run(vacancy_id, run_id, page=page, page_size=page_size)

        @self.router.get("/experiment/runs/{run_id}/config", response_model=Dict[str, Any], summary="Run Configuration")
        async def get_run_config(run_id: int): # Renamed for clarity
            return self.experiment_service.get_run_configuration(run_id)

        @self.router.get("/experiment/candidates/{contact_id}/resume", response_model=Dict[str, Any], summary="Candidate Resume Data")
        async def get_candidate_resume_data_route(contact_id: str):
            return self.experiment_service.get_candidate_resume_data(contact_id)

        # --- Resume Parsing Endpoints (from original) ---
        @self.router.get("/resume/{contact_id}", response_model=dict)
        async def get_candidate(contact_id: str):
            """Return the resume in JSON format for the given contact ID."""
            return self.parse_resume_service.get_candidate(contact_id)

        @self.router.get("/resume/{contact_id}/parse", response_model=dict)
        async def parse_resume_api(contact_id: str, url: str, save: bool):
            """Parse the resume from the provided URL for the given contact."""
            return self.parse_resume_service.parse_resume_api(contact_id, url, save)

        @self.router.post("/resume/{contact_id}/save", response_model=dict)
        async def save_resume(contact_id: str, json_resume: dict):
            """Save the resume JSON for the given contact and return a status code."""
            return self.parse_resume_service.save_candidate(contact_id, json_resume)

        # --- Candidate Stats (from original, ensure service is correctly initialized if used) ---
        @self.router.get("/api/candidate-stats", response_model=dict)
        async def get_candidate_stats_route(): # Renamed to avoid conflict
            # Pass self.postgres_db_connector explicitly
            service = CandidateStatsService(self.logger, self.postgres_db_connector, self.candidate_stats_env)
            try:
                stats = service.get_all_stats()
                return {"stats": stats}
            except Exception as e:
                self.logger.error(f"Error getting candidate statistics: {e}", exc_info=True)
                raise HTTPException(status_code=500, detail="Error fetching candidate statistics")
            finally:
                service.close()

        @self.router.delete("/vacancies/shortlist", 
                           summary="Unshortlist a candidate for a vacancy (NOT SUPPORTED)",
                           description="""
                           This endpoint is currently disabled for safety reasons.
                           
                           Deleting shortlist records from Dataverse can be dangerous and may have
                           unintended consequences. This functionality has been temporarily disabled
                           until proper safeguards and business requirements are established.
                           
                           If you need to remove a candidate from a shortlist, please contact the
                           development team to discuss alternative approaches.
                           """,
                           responses={
                               501: {
                                   "description": "Not Implemented - Functionality disabled for safety",
                                   "content": {
                                       "application/json": {
                                           "example": {
                                               "success": False,
                                               "status_code": 501,
                                               "message": "Unshortlist functionality is not supported",
                                               "details": "This endpoint is disabled for safety reasons. Contact development team for alternatives.",
                                               "error_code": "not_supported",
                                               "error_message": "DELETE operations on shortlists are disabled"
                                           }
                                       }
                                   }
                               }
                           })
        async def unshortlist_candidate_for_vacancy_placeholder(payload: ShortlistCandidatePayload = Body(...)):
            """Placeholder endpoint for unshortlist functionality (disabled for safety)."""
            raise HTTPException(
                status_code=501,
                detail={
                    "success": False,
                    "status_code": 501,
                    "message": "Unshortlist functionality is not supported",
                    "details": "This endpoint is disabled for safety reasons. Contact development team for alternatives.",
                    "error_code": "not_supported",
                    "error_message": "DELETE operations on shortlists are disabled",
                    "vacancy_id": payload.vacancy_id,
                    "candidate_id": payload.candidate_id
                }
            )

    def get_router(self):
        """Return the configured router."""
        return self.router
