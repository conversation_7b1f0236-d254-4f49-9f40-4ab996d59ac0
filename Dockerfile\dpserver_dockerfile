# Use the official Python image
FROM python:3.12.10-slim-bullseye

# Install system dependencies and Microsoft ODBC Driver 18
RUN apt-get update && apt-get install -y \
    curl \
    gnupg2 \
    ca-certificates \
    apt-transport-https \
    unixodbc \
    unixodbc-dev \
    gcc \
    g++ \
    poppler-utils \
    && curl -sSL https://packages.microsoft.com/keys/microsoft.asc | apt-key add - \
    && curl -sSL https://packages.microsoft.com/config/debian/11/prod.list > /etc/apt/sources.list.d/mssql-release.list \
    && apt-get update \
    && ACCEPT_EULA=Y apt-get install -y msodbcsql18 \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Set the working directory in the container
WORKDIR /app

# Copy the necessary files to the container
COPY dpserver /app/dpserver
COPY dataverse_helper /app/dataverse_helper
COPY common /app/common
COPY generator /app/generator
COPY matcher /app/matcher
COPY data /app/data
COPY data_helper /app/data_helper
COPY requirements.txt /app/

# Install dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Expose the FastAPI port (make sure it matches your `appconfig.json`)
EXPOSE 8005

# Run the FastAPI application
CMD ["python", "-m", "dpserver.main"]
