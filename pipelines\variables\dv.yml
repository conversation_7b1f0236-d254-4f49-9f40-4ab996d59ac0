variables:
  WebLBIP: ************
  ServerLBIP: ************
  PortalLBIP: ************
  ReactAppEnv: dev
  WebMinReplicas: 1
  WebMaxReplicas: 1
  WebMemThreshold: 80
  WebCpuThreshold: 80
  ServerMinReplicas: 1
  ServerMaxReplicas: 1
  ServerMemThreshold: 80
  ServerCpuThreshold: 80
  PortalMinReplicas: 1
  PortalMaxReplicas: 1
  PortalMemThreshold: 80
  PortalCpuThreshold: 80
  NEXTAUTH_URL: https://recruiter.dv.tandymgroup.com
  NEXT_PUBLIC_AD_LOGIN: true
  NEXT_PUBLIC_PARSE_RESUME_FROM_MERCURY: true
  NEXT_PUBLIC_APPINSIGHTS_CONNECTION_STRING: a2333a49-cb49-4a2c-8888-d5a4bc1ed97a;IngestionEndpoint=https://eastus-3.in.applicationinsights.azure.com/;LiveEndpoint=https://eastus.livediagnostics.monitor.azure.com/;ApplicationId=5edf80f7-54c1-42c5-910c-1c767571a849
  NEXT_PUBLIC_AUTH_URL: recruiter.dv.tandymgroup.com
  CRM_URL: https://tandymgroup-sandbox.crm.dynamics.com