import logging
import json
from datetime import datetime
from opentelemetry.trace import get_current_span


class JsonFormatter(logging.Formatter):
    def format(self, record):
        # Get OpenTelemetry span context
        span = get_current_span()
        ctx = span.get_span_context() if span else None

        log_record = {
            "timestamp": self.formatTime(record, self.datefmt),
            "level": record.levelname,
            "message": record.getMessage(),
            "filename": record.filename,
            "lineno": record.lineno,
        }

        if hasattr(record, "correlation_id") and record.correlation_id:
            log_record["correlation_id"] = record.correlation_id

        if ctx and ctx.trace_id:
            log_record["trace_id"] = f"{ctx.trace_id:032x}"
            log_record["span_id"] = f"{ctx.span_id:016x}"

        return json.dumps(log_record)


class AppDiagnosticsLogger:
    def __init__(self, name="app", level=logging.DEBUG):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(level)

        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = JsonFormatter()
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)

        self.logger.propagate = True  # Let OpenTelemetry capture root logs

    def info(self, message):
        self.logger.info(message, stacklevel=2)

    def debug(self, message):
        self.logger.debug(message, stacklevel=2)

    def warning(self, message):
        self.logger.warning(message, stacklevel=2)

    def error(self, message):
        self.logger.error(message, stacklevel=2)

    def exception(self, message, exc_info=True):
        self.logger.exception(message, exc_info=exc_info, stacklevel=2)
