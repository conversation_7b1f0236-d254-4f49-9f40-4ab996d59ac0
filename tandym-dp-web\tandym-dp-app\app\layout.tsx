"use client";
import "./globals.css";
import { NotificationProvider } from "@/hooks/useNotification";
import NotificationBar from "@/components/NotificationBar";
import { SkillsProvider } from "@/context/SkillsContext";
import { Providers } from "@/components/providers";
import { EntitlementProvider } from "@/context/EntitlementContext";
import dynamic from "next/dynamic";
import React from "react";
import { usePathname } from "next/navigation";
import { TAB_ROUTE_MAP } from "@/utils/tabRoutes";
import AppInsightsClient from "@/components/AppInsights";

// Move client-only logic to a separate component

const EntitlementReadyWrapper = dynamic(
  () => import("@/components/HeaderTabs/EntitlementReadyWrapper"),
  {
    ssr: false,
  }
);

// Move client-only logic to a separate component
function ClientLayout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  const routes = Object.values(TAB_ROUTE_MAP) as string[];
  return (
    <>
      {routes.includes(pathname) && (
        <>
          <NotificationBar />
          <EntitlementReadyWrapper />
        </>
      )}
      <div className="h-full">
        <div className="mx-3">{children}</div>
      </div>
    </>
  );
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body>
        <Providers>
          <AppInsightsClient />
          <EntitlementProvider>
            <SkillsProvider>
              <NotificationProvider>
                <ClientLayout>{children}</ClientLayout>
              </NotificationProvider>
            </SkillsProvider>
          </EntitlementProvider>
        </Providers>
      </body>
    </html>
  );
}
