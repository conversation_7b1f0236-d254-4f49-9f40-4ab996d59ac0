import json
import os
from dp_portal.db.postgres_connector import PostgresConnector
from dp_portal.appLogger import AppLogger

class HistoricalService:
    def __init__(self, logger: AppLogger, db_connector: PostgresConnector):
        self.logger = logger
        self.db = db_connector
        self.schema = self.db.schema

    def insert_historical_log(self, email_id: str, portal_name: str, feature: str):
        """
        Common method to insert a record into rp_historical_logs.
        """
        try:
        # Get a live database connection
            conn = self.db.connection
            print(conn)
            if conn is None or conn.closed != 0:
                raise Exception("Database connection is not available or already closed.")

            try:
                with conn.cursor() as cur:
                    insert_query = """
                        INSERT INTO rp_historical_logs(emailid, portalname, eventtype)
                        VALUES (%s, %s, %s)
                    """
                    cur.execute(insert_query, (email_id, portal_name, feature))
                    conn.commit()
            except Exception as insert_err:
                conn.rollback()
                self.logger.error(f"DB Insert Error: {insert_err}")
                return {
                    "error": True,
                    "code": "TR_DB_ERR",
                    "message": f"Database insert error: {str(insert_err)}"
                }

            return {
                "error": False,
                "code": "TR_01",
                "message": "Historical log inserted successfully"
            }

        except Exception as e:
            try:
                # Only rollback if connection is active
                conn = self.db.connection
                if conn and conn.closed == 0:
                    conn.rollback()
            except Exception:                
                pass  # Ignore rollback failure

                self.logger.error(f"Final Exception: {e}")
                return {
                    "error": True,
                    "code": "TR_ERR",
                    "message": f"Error inserting historical log: {str(e)}"
                }
        