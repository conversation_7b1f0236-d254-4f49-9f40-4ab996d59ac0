import { API_ENDPOINTS } from "@/api/config";
import AzureADProvider from "next-auth/providers/azure-ad";
import { trackedFetch } from "./trackApi";
import { getAppInsights } from "./appInsights";

export const authOptions = {
  providers: [
    AzureADProvider({
      clientId: process.env.RECRUITER_SSO_CLIENT_ID! as string,
      clientSecret: process.env.RECRUITER_SSO_CLIENT_SECRET! as string,
      tenantId: process.env.NEXT_PUBLIC_AZURE_TENANT_ID! as string,
    }),
  ],
  secret: process.env.NEXTAUTH_SECRET!,
  pages: {
    signIn: `${process.env.NEXTAUTH_URL}/login`, // Custom sign-in page URL
  },
  callbacks: {
    async redirect({ url, baseUrl }: { url: string; baseUrl: string }) {
      if (url.startsWith("/")) return `${baseUrl}${url}`;
      if (new URL(url).origin === baseUrl) return url;
      return baseUrl;
    },

    async session({ session }: any) {
      if (
        process.env.NEXT_PUBLIC_AD_LOGIN !== "true" &&
        process.env.IS_ENTITLEMENT_ENABLED !== "true"
      ) {
        return session;
      }
      const portal_name = "recruiter";
      let entitlement = {};
      try {
        // Caling an entitlements API to fetch user entitlements
        const url = `${
          API_ENDPOINTS.getEntitlements
        }?email_id=${encodeURIComponent(
          session?.user?.email
        )}&portal_name=${encodeURIComponent(portal_name)}`;
        const res = await trackedFetch(url, {}, { context: "getEntitlements" });

        if (res.ok) {
          const data = await res.json();
          entitlement = data.entitlement;
          getAppInsights()?.trackEvent({
            name: "FE_Entitlements_Fetched",
            properties: {
              email: session?.user?.email,
            },
          });
        } else {
          console.error("Failed to fetch entitlements:", res.statusText);
        }
      } catch (err) {
        console.error("Error fetching entitlements:", err);
      }
      return {
        ...session,
        entitlement,
      };
    },
  },
};
