<!DOCTYPE html>
<html>
  <body style="font-family: Arial, sans-serif; color: #333;">
    <h2>Hello {{ recruiter_name }},</h2>
    
    <p>We encountered an issue with the <strong>job template in Advert Text 1</strong> for vacancy {{ vacancy_ref }} that prevents us from enabling Catalyst Match:</p>

    <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 20px 0; border-radius: 5px;">
      <h3 style="color: #856404; margin-top: 0;">Template Error:</h3>
      <p style="color: #856404; margin-bottom: 0;"><strong>{{ error_description }}</strong></p>
    </div>

    <p>To resolve this issue and enable Catalyst Match for this vacancy:</p>
    
    <ol>
      <li>Click the link below to access the vacancy in Mercury</li>
      <li>Review and update the job template with the missing information</li>
      <li>Save the changes</li>
      <li>Re-enable Catalyst Match for this vacancy</li>
    </ol>

    <p style="margin: 20px 0;">
      <a href="{{ vacancy_url }}" style="background-color: #0078D7; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
        Open Vacancy "{{ vacancy_ref }}: {{ vacancy_title }}" in Mercury
      </a>
    </p>

    <p><strong>Note:</strong> Catalyst Match has been temporarily disabled for this vacancy until the template is corrected.</p>

    <p style="margin-top: 2em;">
      If you need assistance with updating the job template or have any questions, please submit a ticket to RevOps-Catalyst Match Support.
    </p>

    <p>Thank you,<br>Tandym Catalyst Automation Team</p>
  </body>
</html>