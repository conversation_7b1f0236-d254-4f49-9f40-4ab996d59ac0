import os
import threading
import time
import psycopg2
from typing import Optional
from common.appLogger import AppLogger
from common.db.config_postgres import PostgresCredentialManager, PostgresEnvironment
from common.secrets_env import load_secrets_env_variables

class PostgresConnector:
    """Handles PostgreSQL database connections using a configuration dictionary."""

    def __init__(self, env: Optional[PostgresEnvironment] = None, config: Optional[dict] = None, logger=None):
        """
        Initialize with either an environment or a configuration dictionary.

        - If `env` is provided, it retrieves credentials using PostgresCredentialManager.
        - If `config` is provided, it directly initializes the connection settings.
        """
        if logger is None:
            raise ValueError("AppLogger instance is required.")
        
        self.logger = logger
        if env in [PostgresEnvironment.PROD, PostgresEnvironment.UAT_DP_VECTOR]:
            self.schema="tandymdpprod"
        elif env == PostgresEnvironment.DEV_VECTOR:
            self.schema="tandymdpvect"
        elif env == PostgresEnvironment.AZ_APP_VAULT:
            self.schema = os.getenv("DB_USERNAME", "tandymdp")
        else:
            self.schema="tandymdp"

        logger.debug(f"Using schema {self.schema}")
        self.connection = None
        self._lock = threading.Lock()
        
        if env is not None:
            self.logger.debug(f"Initializing PostgresConnector using environment: {env.name}")
            credential_manager = PostgresCredentialManager(self.logger)
            credentials = credential_manager.get_credentials(env).to_dict()
            self._init_with_config(credentials)
        elif config is not None:
            self.logger.debug("Initializing PostgresConnector using provided configuration dictionary.")
            self._init_with_config(config)
        else:
            raise ValueError("Either 'env' or 'config' must be provided.")

    def _init_with_config(self, config: dict):
        """Internal method to initialize the database connection with a configuration dictionary."""
        # Add default connect_timeout if not specified
        self.config = config
        if 'connect_timeout' not in self.config:
            self.config['connect_timeout'] = 10
            self.logger.debug("Setting default connect_timeout to 10 seconds")
        
        self.connection: Optional[psycopg2.extensions.connection] = None

    def connect(self):
        """
        Establish or verify a database connection.
        Retries up to 3 times if connection fails.
        Thread-safe. Reuses healthy connections.
        """
        with self._lock:
            # Reuse healthy connection
            if self.connection is not None:
                try:
                    cur = self.connection.cursor()
                    cur.execute("SELECT 1")
                    cur.close()
                    self.logger.debug(f"Existing connection to {self.config['dbname']} is valid.")
                    return self.connection
                except Exception as e:
                    self.logger.warning(f"Existing connection check failed: {e}")
                    self.close()

            # Retry new connection up to 3 times
            for attempt in range(1, 4):
                try:
                    self.connection = psycopg2.connect(**self.config)
                    self.logger.debug(f"Connected to {self.config['dbname']} (attempt {attempt}).")
                    return self.connection
                except Exception as e:
                    self.logger.warning(f"Attempt {attempt} failed to connect to {self.config['dbname']}: {e}")
                    time.sleep(1)

            # All attempts failed
            self.logger.error(f"Failed to connect to {self.config['dbname']} after 3 attempts.")
            return None

    def close(self):
        if self.connection:
            try:
                self.connection.close()
                self.logger.debug("Database connection closed.")
            except Exception as e:
                self.logger.warning(f"Error closing connection: {e}")
            finally:
                self.connection = None

class PostgresConnectorV2:
    """
    PostgresConnectorV2 provides a robust and context-manager-safe interface for connecting to a PostgreSQL database.

    Key Features:
    - Always returns a new psycopg2 connection on each call to `connect()`, ensuring thread/process safety and avoiding connection caching issues.
    - Supports initialization via either a PostgresEnvironment (fetching credentials securely) or a direct configuration dictionary.
    - Implements automatic retry logic (up to 3 attempts) when establishing a connection, with logging for each attempt.
    - Selects the appropriate database schema based on the provided environment.
    - Requires an AppLogger instance for structured logging.

    Args:
        env (Optional[PostgresEnvironment]): The environment to use for fetching credentials and determining schema.
        config (Optional[dict]): Direct configuration dictionary for psycopg2 connection parameters.
        logger: An AppLogger instance for logging connection events and errors.

    Raises:
        ValueError: If neither 'env' nor 'config' is provided, or if logger is not supplied.

    Methods:
        connect(): Attempts to establish and return a new psycopg2 connection, retrying up to 3 times on failure.

    Note:
        # Difference between PostgresConnectorV2 and PostgresConnector:
        # - PostgresConnectorV2 never caches or reuses connections; each call to `connect()` yields a new connection.
        # - It is designed to be safe for concurrent and context-managed usage, unlike the original PostgresConnector which may cache or reuse connections.
        # - V2 enforces the presence of a logger and provides improved logging and error handling.
    """
    # Improved PostgreSQL connector: always returns a new connection, never caches.
    # Safe for use with context managers and concurrent code.
    def __init__(self, env: Optional[PostgresEnvironment] = None, config: Optional[dict] = None, logger=None):
        if logger is None:
            raise ValueError("AppLogger instance is required.")
        self.logger = logger
        if env in [PostgresEnvironment.PROD, PostgresEnvironment.UAT_DP_VECTOR]:
            self.schema = "tandymdpprod"
        elif env == PostgresEnvironment.DEV_VECTOR:
            self.schema = "tandymdpvect"
        elif env == PostgresEnvironment.AZ_APP_VAULT:
            self.schema = os.getenv("DB_USERNAME", "tandymdp")
        else:
            self.schema = "tandymdp"
        logger.debug(f"Using schema {self.schema}")
        if env is not None:
            self.logger.debug(f"Initializing PostgresConnectorV2 using environment: {env.name}")
            credential_manager = PostgresCredentialManager(self.logger)
            credentials = credential_manager.get_credentials(env).to_dict()
            self._init_with_config(credentials)
        elif config is not None:
            self.logger.debug("Initializing PostgresConnectorV2 using provided configuration dictionary.")
            self._init_with_config(config)
        else:
            raise ValueError("Either 'env' or 'config' must be provided.")

    def _init_with_config(self, config: dict):
        self.config = config
        if 'connect_timeout' not in self.config:
            self.config['connect_timeout'] = 10
            self.logger.debug("Setting default connect_timeout to 10 seconds")

    def connect(self):
        """
        Always returns a new psycopg2 connection.
        Retries up to 3 times if connection fails.
        """
        for attempt in range(1, 4):
            try:
                conn = psycopg2.connect(**self.config)
                self.logger.debug(f"Connected to {self.config['dbname']} (attempt {attempt}).")
                return conn
            except Exception as e:
                self.logger.warning(f"Attempt {attempt} failed to connect to {self.config['dbname']}: {e}")
                time.sleep(1)
        self.logger.error(f"Failed to connect to {self.config['dbname']} after 3 attempts.")
        return None

# Example Usage
def test():
    """Test database connections using PostgresCredentialManager."""
    logger_config = {
        "level": "DEBUG",
        "log_to_stdout": True
    }
    logger = AppLogger(logger_config)
    logger.info("Testing PostgreSQL connections...")

    load_secrets_env_variables()

    # Test constructor with environment argument
    dev_connector = PostgresConnector(env=PostgresEnvironment.DEV, logger=logger)
    uat_connector = PostgresConnector(env=PostgresEnvironment.UAT, logger=logger)
    prod_connector = PostgresConnector(env=PostgresEnvironment.PROD, logger=logger)

    # Test constructor with direct configuration
    custom_config = {
        "host": "custom-host",
        "port": 5432,
        "dbname": "custom_db",
        "user": "custom_user",
        "password": "custom_password",
        "connect_timeout": 10 #10 seconds
    }
    custom_connector = PostgresConnector(config=custom_config, logger=logger)

    # Test connections
    for connector in [
        dev_connector, 
        uat_connector, 
        prod_connector, 
        #custom_connector
        ]:
        conn = connector.connect()
        if conn:
            query = f"select * from {connector.schema}.attribute_types;"
            cur = conn.cursor()
            cur.execute(query)
            results = cur.fetchall()
            logger.info(f"Retrived count = {len(results)}")
            for row in results:
                logger.info(row)  # Converts row into a dictionary
            connector.close()


if __name__ == "__main__":
    test()
