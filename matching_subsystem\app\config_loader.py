import json
import os
import glob # For listing files
from typing import Dict, Optional, Any
from copy import deepcopy
import logging

class DuplicateSubcategorySpecError(Exception):
    """Custom exception for duplicate subcategory specifications."""
    pass

class ConfigManager:
    def __init__(self, master_config_path: str, logger: logging.Logger):
        self.master_config_path = master_config_path
        self.logger = logger
        self.master_config = self._load_json_file(master_config_path, is_master_config=True)
        if not self.master_config:
            # _load_json_file logs the error, so just raise
            raise ValueError(f"Critical: Master configuration could not be loaded from {master_config_path}")

        self.subcategory_config_folder_path = self.master_config.get('config_paths', {}).get('subcategory_config_folder')
        if not self.subcategory_config_folder_path:
            self.logger.warning("`config_paths.subcategory_config_folder` not specified in master config. Subcategory specs cannot be loaded.")
            self.subcategory_specs_map: Dict[str, Dict] = {}
        else:
            self.logger.info(f"Master config loaded. Subcategory config folder: '{self.subcategory_config_folder_path}'")
            self.subcategory_specs_map = self._load_all_subcategory_specs(self.subcategory_config_folder_path)
        
        self.logger.info(f"ConfigManager initialized. Loaded {len(self.subcategory_specs_map)} subcategory specifications.")


    def _load_json_file(self, file_path: str, is_master_config: bool = False) -> Optional[Dict]:
        if not os.path.exists(file_path):
            self.logger.error(f"Configuration file not found: {file_path}")
            return None
        if not os.path.isfile(file_path):
            self.logger.error(f"Configuration path is not a file: {file_path}")
            return None
        try:
            with open(file_path, 'r') as f:
                return json.load(f)
        except json.JSONDecodeError as e:
            self.logger.error(f"Error decoding JSON from {file_path}: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Unexpected error loading file {file_path}: {e}", exc_info=True)
            return None

    def _load_all_subcategory_specs(self, folder_path: str) -> Dict[str, Dict]:
        """
        Loads all .json files from the specified folder, expecting each to contain
        a 'subcategory_name' key.
        """
        specs_map: Dict[str, Dict] = {}
        if not os.path.isdir(folder_path):
            self.logger.warning(f"Subcategory configuration folder not found or not a directory: {folder_path}")
            return specs_map

        json_files = glob.glob(os.path.join(folder_path, "*.json"))
        if not json_files:
            self.logger.info(f"No .json specification files found in folder: {folder_path}")
            return specs_map

        self.logger.info(f"Found {len(json_files)} potential spec files in {folder_path}.")

        for file_path in json_files:
            self.logger.debug(f"Attempting to load spec file: {file_path}")
            spec_data = self._load_json_file(file_path)
            if not spec_data:
                self.logger.warning(f"Skipping file {file_path} due to load error.")
                continue

            subcategory_name_in_file = spec_data.get("subcategory_name")
            if not subcategory_name_in_file or not isinstance(subcategory_name_in_file, str):
                self.logger.warning(f"File {file_path} does not contain a valid 'subcategory_name' string key. Skipping.")
                continue
            
            if subcategory_name_in_file in specs_map:
                # Duplicate found!
                error_msg = (f"Duplicate subcategory_name '{subcategory_name_in_file}' found. "
                             f"Original in: {specs_map[subcategory_name_in_file].get('_source_file', 'unknown')}, "
                             f"Duplicate in: {file_path}.")
                self.logger.error(error_msg)
                raise DuplicateSubcategorySpecError(error_msg) # Or handle differently, e.g., log and skip duplicate
            
            spec_data['_source_file'] = file_path # Store for debugging
            specs_map[subcategory_name_in_file] = spec_data
            self.logger.info(f"Successfully loaded and mapped spec for subcategory: '{subcategory_name_in_file}' from {file_path}")
            
        return specs_map

    def _deep_merge_dicts(self, base: Dict, override: Dict) -> Dict:
        # (Keep this helper function as previously defined)
        merged = deepcopy(base)
        for key, value in override.items():
            if isinstance(value, dict) and key in merged and isinstance(merged[key], dict):
                merged[key] = self._deep_merge_dicts(merged[key], value)
            else:
                merged[key] = value
        return merged

    def _apply_vacancy_overrides(self, vacancy_overrides: Dict[str, Any], effective_config: Dict[str, Any]):
        if not vacancy_overrides:
            return
        
        if vacancy_overrides.get("recency_indices_for_match"):
            self.logger.info("Applying vacancy-specific overrides (Future placeholder).")
            #set recency_indices_for_match in phase2_semantic_score_calculation
            effective_config["phase2_semantic_score_calculation"]["recency_indices_for_match"] = vacancy_overrides["recency_indices_for_match"]

            #set recency_indices_for_title_match in phase1_prefilter
            phase1_prefilter = effective_config.get("phase1_prefilter", {})
            priority_order = phase1_prefilter.get("priority_order", [])

            for priority_item in priority_order:
                if priority_item.get("filter_type") == "job_title_semantic":
                    priority_item.setdefault("params", {})["recency_indices_for_title_match"] = vacancy_overrides["recency_indices_for_match"]
                    break  # Only update the first match

            # Reassign only if keys were missing
            if "priority_order" not in phase1_prefilter:
                phase1_prefilter["priority_order"] = priority_order

            if "phase1_prefilter" not in effective_config:
                effective_config["phase1_prefilter"] = phase1_prefilter
            # effective_config = self._deep_merge_dicts(effective_config, vacancy_overrides)
            pass

        if vacancy_overrides.get("job_location"):
            for job_location in vacancy_overrides.get("job_location"):
                # need to add support for multiple job locations. For now, just pick the first radius
                effective_config["phase3_ranking"]["enable_distance_filtering"] = True
                effective_config["phase3_ranking"]["max_distance_miles"] = job_location.get("radius")
                break

    def get_effective_config(self, subcategory_name: Optional[str] = None, vacancy_overrides: Optional[Dict] = None) -> Dict:
        effective_config = deepcopy(self.master_config) # Start with master config

        subcategory_spec: Optional[Dict] = None
        if subcategory_name:
            subcategory_spec = self.subcategory_specs_map.get(subcategory_name)
            if subcategory_spec:
                self.logger.info(f"Found pre-loaded spec for subcategory '{subcategory_name}'. Merging.")
            else:
                self.logger.warning(f"No pre-loaded spec found for subcategory '{subcategory_name}'. "
                                    "Using master config defaults where applicable.")
        else:
            self.logger.info("No subcategory name provided. Using master config defaults only.")

        # --- Merge subcategory spec if available ---
        if subcategory_spec:
            # 1. Merge 'default_scoring_parameters' with subcategory overrides
            # The subcategory spec's phase2_semantic_score_calculation can contain direct overrides
            # for keys like "similarity_thresholds_per_category", "frequency_bonus", etc.
            master_default_scoring_params = effective_config.get("default_scoring_parameters", {})
            effective_scoring_params = deepcopy(master_default_scoring_params) # Start with master's

            sub_phase2_config = subcategory_spec.get("phase2_semantic_score_calculation", {})
            for key_to_override in ["similarity_thresholds_per_category", "frequency_bonus", "recency_scores", "vacancy_item_weights_map", "skill_recency_boosts"]:
                if key_to_override in sub_phase2_config:
                    # If key exists in master's scoring params and is a dict, deep merge
                    if key_to_override in effective_scoring_params and isinstance(effective_scoring_params[key_to_override], dict) \
                       and isinstance(sub_phase2_config[key_to_override], dict):
                        effective_scoring_params[key_to_override] = self._deep_merge_dicts(
                            effective_scoring_params[key_to_override],
                            sub_phase2_config[key_to_override]
                        )
                    else: # Otherwise, direct override (e.g., if master didn't have it, or types don't match for deep merge)
                        effective_scoring_params[key_to_override] = deepcopy(sub_phase2_config[key_to_override])
            
            effective_config["effective_scoring_parameters"] = effective_scoring_params

            # 2. Merge other top-level keys from subcategory_spec if they exist
            # These keys will fully replace the ones from master_config if present in sub_spec.
            for key in ["default_category_weights", "phase1_prefilter", "phase3_ranking"]:
                if key in subcategory_spec:
                    effective_config[key] = deepcopy(subcategory_spec[key])
            
            # 3. Merge remaining phase2_semantic_score_calculation settings (non-scoring_param overrides)
            if "phase2_semantic_score_calculation" not in effective_config:
                effective_config["phase2_semantic_score_calculation"] = {}
            
            for key, value in sub_phase2_config.items():
                if key not in ["similarity_thresholds_per_category", "frequency_bonus", "recency_scores", "vacancy_item_weights_map", "skill_recency_boosts"]:
                    effective_config["phase2_semantic_score_calculation"][key] = deepcopy(value)
        else: # No subcategory_spec, or subcategory_name not provided
            # Ensure 'effective_scoring_parameters' is populated from master's defaults
            effective_config["effective_scoring_parameters"] = deepcopy(effective_config.get("default_scoring_parameters", {}))

        self._apply_vacancy_overrides(vacancy_overrides, effective_config)

        # Final checks for essential structures
        if "effective_scoring_parameters" not in effective_config:
             effective_config["effective_scoring_parameters"] = {}
        if "default_category_weights" not in effective_config:
            self.logger.warning("No 'default_category_weights' found in effective config. Ranking may be affected.")
            effective_config["default_category_weights"] = {}
        if "phase1_prefilter" not in effective_config: # Ensure phase1 always exists, even if empty
            self.logger.debug("No 'phase1_prefilter' defined in subcategory or master, defaulting to empty.")
            effective_config["phase1_prefilter"] = {"priority_order": []}

        return effective_config

    def get_master_config(self) -> Dict:
        return deepcopy(self.master_config)

    def get_subcategory_spec(self, subcategory_name: str) -> Optional[Dict]:
        """Returns the loaded specification for a given subcategory_name, if found."""
        return deepcopy(self.subcategory_specs_map.get(subcategory_name))