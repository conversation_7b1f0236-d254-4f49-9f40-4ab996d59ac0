{"/CandidateTuning/For_Mercury_Portal/page": "app/CandidateTuning/For_Mercury_Portal/page.js", "/api/vacancies/route": "app/api/vacancies/route.js", "/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/page": "app/page.js", "/candidates/page": "app/candidates/page.js", "/api/subcategories/route": "app/api/subcategories/route.js", "/api/vacancies/[id]/route": "app/api/vacancies/[id]/route.js", "/api/entitlements/route": "app/api/entitlements/route.js", "/_not-found/page": "app/_not-found/page.js", "/api/vacancies/[id]/regenerate-catalyst-match/route": "app/api/vacancies/[id]/regenerate-catalyst-match/route.js", "/api/vacancies/[id]/catalystmatchstatus/route": "app/api/vacancies/[id]/catalystmatchstatus/route.js"}