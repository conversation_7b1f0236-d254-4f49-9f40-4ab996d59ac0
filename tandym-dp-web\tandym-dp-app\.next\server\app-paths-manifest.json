{"/page": "app/page.js", "/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/api/subcategories/route": "app/api/subcategories/route.js", "/api/entitlements/route": "app/api/entitlements/route.js", "/candidates/page": "app/candidates/page.js", "/api/vacancies/route": "app/api/vacancies/route.js", "/api/vacancies/[id]/route": "app/api/vacancies/[id]/route.js", "/api/vacancies/files/route": "app/api/vacancies/files/route.js", "/_not-found/page": "app/_not-found/page.js", "/CandidateTuning/For_Mercury_Portal/page": "app/CandidateTuning/For_Mercury_Portal/page.js", "/api/vacancies/[id]/regenerate-catalyst-match/route": "app/api/vacancies/[id]/regenerate-catalyst-match/route.js"}