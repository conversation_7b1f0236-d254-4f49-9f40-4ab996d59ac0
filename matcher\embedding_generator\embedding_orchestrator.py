import argparse
import os
import json
import uuid
from datetime import datetime
from typing import List, Dict, Any, Literal, Optional
import fcntl
import sys

from common.appLogger import AppLogger
from common.db.postgres_connector import PostgresConnectorV2, PostgresEnvironment
from common.secrets_env import load_secrets_env_variables
import psycopg2
from psycopg2.extras import RealDictCursor

# Import the new function from candidate_embedding
from matcher.embedding_generator.candidate_embedding import generate_embeddings_for_single_candidate, client as openai_client # Ensure client is initialized
from matcher.embedding_generator.experience_calculator import calculate_total_experience # Added

MODES_TO_PROCESS: List[Literal['job_title', 'soft_skill', 'technical_skill', 'tool_platform', 'degree_certification', 'industry', 'degree_certification_object', 'latest_work_experience']] = [
    'job_title',
    'soft_skill',
    'technical_skill',
    'tool_platform',
    'degree_certification',
    'industry',
    'degree_certification_object',
    'latest_work_experience'
]

MODE_TO_QUEUE_COLUMN_MAP = {
    'job_title': 'job_title_embedding_status',
    'soft_skill': 'soft_skills_embedding_status',
    'technical_skill': 'tech_skills_embedding_status',
    'tool_platform': 'tools_platform_embedding_status',
    'degree_certification': 'degees_certification_embedding_status', # Matches schema typo
    'industry': 'industry_embedding_status',
    'degree_certification_object': 'degrees_certification_as_json_object_embedding_status',
    'latest_work_experience': 'latest_work_experience_embedding_status'
}
QUEUE_TABLE_NAME = "candidate_embedding_generation_processing_queue"
LOCK_FILE_PATH = "/mnt/incoming/statuses/embedding_generator/embedding_orchestrator.lock" # Choose a suitable path

def ensure_connection(conn, db_connector, logger):
    if conn is None or getattr(conn, "closed", 1) != 0:
        logger.warning("DB connection was closed. Reconnecting...")
        return db_connector.connect()
    return conn

def run_orchestrator(
    logger: AppLogger,
    pg_env: PostgresEnvironment,
    # created_after: str, # No longer primary filter
    candidate_limit: Optional[int] = None,
    dry_run: bool = False
):
    logger.info(f"--- Starting Embedding Orchestrator from Queue ---")
    logger.info(f"Environment: {pg_env.name}, Candidate Limit: {candidate_limit}, Dry Run: {dry_run}")

    if not openai_client:
        logger.error("OpenAI client from candidate_embedding module is not initialized. Ensure API key is set and client is created.")
        return

    db_connector = PostgresConnectorV2(env=pg_env, logger=logger)
    db_schema = db_connector.schema
    processed_queue_items_count = 0
    successfully_processed_candidates_count = 0

    # Fetch queue items ONCE at the start
    with db_connector.connect() as conn:
        with conn.cursor(cursor_factory=RealDictCursor) as cursor:
            fetch_queue_query = f"""
                SELECT id, contact_id
                FROM {db_schema}.{QUEUE_TABLE_NAME}
                WHERE status = 'pending'
                ORDER BY id ASC
            """
            query_params_queue = []
            if candidate_limit is not None:
                fetch_queue_query += " LIMIT %s"
                query_params_queue.append(candidate_limit)
            cursor.execute(fetch_queue_query, tuple(query_params_queue))
            queue_items_to_process = cursor.fetchall()

    logger.info(f"Found {len(queue_items_to_process)} candidates in queue with 'pending' status.")

    for queue_item in queue_items_to_process:
        queue_id = queue_item['id']
        contact_id_uuid = queue_item['contact_id']
        contact_id_str = str(contact_id_uuid)
        logger.info(f"Processing candidate: {contact_id_str} from queue (ID: {queue_id})")

        # Open a new connection for each candidate
        with db_connector.connect() as conn:
            try:
                # Fetch and lock candidate details from candidates table
                with conn.cursor(cursor_factory=RealDictCursor) as candidate_cursor:
                    fetch_candidate_details_query = f"""
                        SELECT resume_data, job_tuples
                        FROM {db_schema}.candidates
                        WHERE contact_id = %s
                        FOR UPDATE;
                    """
                    candidate_cursor.execute(fetch_candidate_details_query, (contact_id_uuid,))
                    candidate_details = candidate_cursor.fetchone()
                
                if not candidate_details:
                    logger.warning(f"Candidate {contact_id_str} not found in candidates table (after attempting lock). Marking queue item as failed.")
                    # Rollback the current transaction as the lock was not on a found row / or to be clean
                    conn.rollback() 
                    if not dry_run:
                        try:
                            with conn.cursor() as update_q_cursor_notfound: # New cursor for separate transaction
                                update_q_query_notfound = f"""
                                    UPDATE {db_schema}.{QUEUE_TABLE_NAME}
                                    SET status = %s, processed_at = CURRENT_TIMESTAMP, status_notes = %s
                                    WHERE id = %s;
                                """
                                update_q_cursor_notfound.execute(update_q_query_notfound, ('failed', 'Candidate details not found in main table (FOR UPDATE)'[:1024], queue_id))
                            conn.commit() # Commit this specific queue update
                        except psycopg2.Error as qe_notfound:
                            conn.rollback()
                            logger.error(f"Also failed to update queue status to 'failed' for {queue_id} (details not found): {qe_notfound}")
                    processed_queue_items_count += 1
                    continue

                # If candidate found and locked, proceed with processing
                current_resume_data = candidate_details['resume_data'] if candidate_details['resume_data'] else {}
                job_tuples_data = candidate_details.get('job_tuples')

                embedding_statuses_for_resume_data = current_resume_data.get('embedding_statuses')
                if not isinstance(embedding_statuses_for_resume_data, dict):
                    embedding_statuses_for_resume_data = {}
                
                mode_processing_results_for_queue: Dict[str, Optional[str]] = {col: None for col in MODE_TO_QUEUE_COLUMN_MAP.values()}
                
                all_modes_successful_for_candidate = True
                any_mode_processed_in_this_run = False
                processed_modes_count_for_queue = 0
                successful_modes_count_for_queue = 0

                for mode in MODES_TO_PROCESS:
                    any_mode_processed_in_this_run = True
                    logger.info(f"Running mode '{mode}' for candidate {contact_id_str} (Queue ID: {queue_id})...")
                    
                    mode_success, mode_error_reason = generate_embeddings_for_single_candidate(
                        logger=logger,
                        mode=mode,
                        contact_id=contact_id_uuid,
                        pg_env=pg_env,
                        dry_run=dry_run,
                        skip_regenerate_embedding=True
                    )
                    
                    processed_modes_count_for_queue += 1
                    queue_column_name = MODE_TO_QUEUE_COLUMN_MAP[mode]

                    if mode_success:
                        mode_processing_results_for_queue[queue_column_name] = 'Complete'
                        embedding_statuses_for_resume_data[mode] = 'Complete'
                        successful_modes_count_for_queue += 1
                    else:
                        failure_reason = mode_error_reason if mode_error_reason else 'Failed'
                        mode_processing_results_for_queue[queue_column_name] = failure_reason[:255] # Truncate if too long for DB
                        embedding_statuses_for_resume_data[mode] = 'Failed' # Keep resume_data status simple
                        all_modes_successful_for_candidate = False

                    logger.info(f"Mode '{mode}' for candidate {contact_id_str} status: {mode_processing_results_for_queue[queue_column_name]}")

                final_queue_status = 'pending'
                if processed_modes_count_for_queue == 0 and not any_mode_processed_in_this_run :
                     final_queue_status = 'error_no_modes_attempted'
                     logger.warning(f"No modes were attempted for candidate {contact_id_str} (Queue ID: {queue_id}). This should not happen.")
                elif successful_modes_count_for_queue == len(MODES_TO_PROCESS):
                    final_queue_status = 'complete'
                elif successful_modes_count_for_queue > 0:
                    final_queue_status = 'partial_complete'
                elif successful_modes_count_for_queue == 0 and processed_modes_count_for_queue > 0 :
                    final_queue_status = 'failed'
                else:
                    final_queue_status = 'unknown_state_after_processing'
                    logger.error(f"Queue item {queue_id} for candidate {contact_id_str} ended in an unknown state.")

                calculated_total_months = calculate_total_experience(
                    job_tuples_data=job_tuples_data,
                    resume_data=current_resume_data, # Pass the most current resume_data
                    contact_id_str=contact_id_str,
                    logger=logger
                )

                if not dry_run:
                    # Update queue table
                    with conn.cursor() as update_q_cursor:
                        update_q_query = f"""
                            UPDATE {db_schema}.{QUEUE_TABLE_NAME}
                            SET status = %s, processed_at = CURRENT_TIMESTAMP,
                                job_title_embedding_status = %s,
                                tools_platform_embedding_status = %s,
                                tech_skills_embedding_status = %s,
                                soft_skills_embedding_status = %s,
                                industry_embedding_status = %s,
                                degees_certification_embedding_status = %s,
                                degrees_certification_as_json_object_embedding_status = %s,
                                latest_work_experience_embedding_status = %s,
                                status_notes = %s
                            WHERE id = %s;
                        """
                        update_q_params = (
                            final_queue_status,
                            mode_processing_results_for_queue.get(MODE_TO_QUEUE_COLUMN_MAP['job_title']),
                            mode_processing_results_for_queue.get(MODE_TO_QUEUE_COLUMN_MAP['tool_platform']),
                            mode_processing_results_for_queue.get(MODE_TO_QUEUE_COLUMN_MAP['technical_skill']),
                            mode_processing_results_for_queue.get(MODE_TO_QUEUE_COLUMN_MAP['soft_skill']),
                            mode_processing_results_for_queue.get(MODE_TO_QUEUE_COLUMN_MAP['industry']),
                            mode_processing_results_for_queue.get(MODE_TO_QUEUE_COLUMN_MAP['degree_certification']),
                            mode_processing_results_for_queue.get(MODE_TO_QUEUE_COLUMN_MAP['degree_certification_object']),
                            mode_processing_results_for_queue.get(MODE_TO_QUEUE_COLUMN_MAP['latest_work_experience']),
                            None, # status_notes can be empty or a specific message
                            queue_id
                        )
                        update_q_cursor.execute(update_q_query, update_q_params)

                    # Update candidates table: resume_data
                    if any_mode_processed_in_this_run:
                        current_resume_data['embedding_statuses'] = embedding_statuses_for_resume_data
                        current_resume_data['resume_status'] = f"embedding generation {final_queue_status}"
                        new_resume_data_json = json.dumps(current_resume_data)
                        with conn.cursor() as update_resume_cursor:
                            update_resume_query = f"""
                                UPDATE {db_schema}.candidates
                                SET resume_data = %s::jsonb
                                WHERE contact_id = %s;
                            """
                            update_resume_cursor.execute(update_resume_query, (new_resume_data_json, contact_id_uuid))
                    
                    # Update candidates table: total_experience_in_months
                    with conn.cursor() as update_exp_cursor:
                        update_experience_query = f"""
                            UPDATE {db_schema}.candidates
                            SET total_experience_in_months = %s
                            WHERE contact_id = %s;
                        """
                        update_exp_cursor.execute(update_experience_query, (calculated_total_months, contact_id_uuid))
                    
                    conn.commit() # Commit all updates for this candidate atomically
                    logger.info(f"Successfully committed all updates for candidate {contact_id_str} (Queue ID: {queue_id}) with status '{final_queue_status}'.")
                    successfully_processed_candidates_count +=1
                else: # Dry run
                    logger.info(f"[DRY RUN] Would update queue item ID {queue_id} for candidate {contact_id_str} with status '{final_queue_status}' and mode statuses: {mode_processing_results_for_queue}")
                    if any_mode_processed_in_this_run:
                         logger.info(f"[DRY RUN] Would update resume_data in candidates table for {contact_id_str} with statuses: {embedding_statuses_for_resume_data}")
                    log_exp_value_dry_run = f"{calculated_total_months:.2f} months" if calculated_total_months is not None else "NULL"
                    logger.info(f"[DRY RUN] Would update total_experience_in_months in candidates table for {contact_id_str} to {log_exp_value_dry_run}.")
                    successfully_processed_candidates_count +=1
                
                if all_modes_successful_for_candidate and any_mode_processed_in_this_run:
                    logger.info(f"All processed modes for candidates table update were successful for candidate {contact_id_str}.")
                elif not all_modes_successful_for_candidate and any_mode_processed_in_this_run:
                    logger.warning(f"One or more modes for candidates table update failed for candidate {contact_id_str}.")

            except psycopg2.Error as e: # Handles errors from SELECT FOR UPDATE or subsequent operations in the try block
                # Before rollback, ensure connection is open
                conn = ensure_connection(conn, db_connector, logger)
                if conn:
                    conn.rollback()
                logger.error(f"DB error processing candidate {contact_id_str} (Queue ID: {queue_id}): {e}", exc_info=True)
                # Attempt to update queue item to 'failed' in a new, separate transaction
                if not dry_run:
                    try:
                        with conn.cursor() as update_q_fail_cursor:
                            update_q_fail_query = f"""
                                UPDATE {db_schema}.{QUEUE_TABLE_NAME}
                                SET status = %s, processed_at = CURRENT_TIMESTAMP, status_notes = %s
                                WHERE id = %s;
                            """
                            # Use a generic error message for a specific column if others are unknown
                            error_msg_for_queue = f"DBError during processing: {str(e)}"[:1024] # Truncate error
                            update_q_fail_cursor.execute(update_q_fail_query, ('failed', error_msg_for_queue, queue_id))
                        conn.commit()
                    except psycopg2.Error as qe_fail:
                        conn.rollback()
                        logger.error(f"Also failed to update queue status to 'failed' for {queue_id} after main processing DBError: {qe_fail}")
            except Exception as e: # Handles non-DB errors during processing
                # Before rollback, ensure connection is open
                conn = ensure_connection(conn, db_connector, logger)
                if conn:
                    conn.rollback()
                logger.error(f"Unexpected error processing candidate {contact_id_str} (Queue ID: {queue_id}): {e}", exc_info=True)
                # Attempt to update queue item to 'failed' in a new, separate transaction
                if not dry_run:
                    try:
                        # Create a new connection for this isolated update if the main one is in a bad state
                        with PostgresConnectorV2(env=pg_env, logger=logger).connect() as fail_conn:
                            with fail_conn.cursor()as update_q_unexpected_fail_cursor:
                                update_q_unexpected_fail_query = f"""
                                    UPDATE {db_schema}.{QUEUE_TABLE_NAME}
                                    SET status = %s, processed_at = CURRENT_TIMESTAMP, status_notes = %s
                                    WHERE id = %s;
                                """
                                error_msg_for_queue = f"Exception: {str(e)}"[:1024] # Truncate error
                                update_q_unexpected_fail_cursor.execute(update_q_unexpected_fail_query, ('failed', error_msg_for_queue, queue_id))
                            fail_conn.commit()
                    except psycopg2.Error as qe_unexpected_fail:
                        # conn.rollback() # conn might be the original one, which is already rolled back
                        logger.error(f"Also failed to update queue status to 'failed' for {queue_id} after main processing Exception: {qe_unexpected_fail}")
            
            processed_queue_items_count += 1

    logger.info(f"--- Embedding Orchestrator Finished ---")
    logger.info(f"Total queue items evaluated: {processed_queue_items_count}")
    logger.info(f"Total candidates table entries updated/would update (experience/resume_data): {successfully_processed_candidates_count}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Orchestrates embedding generation for candidates from a processing queue.")
    parser.add_argument(
        "--env",
        choices=[env.name for env in PostgresEnvironment],
        default=PostgresEnvironment.DEV.name,
        help="Database environment to run against (e.g., DEV, PROD). Default: DEV."
    )
    parser.add_argument(
        "--limit",
        type=int,
        default=None,
        help="Limit the number of 'pending' candidates to process from the queue (for testing)."
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Run in dry-run mode (no actual embeddings generated or DB updates performed by this script, "
             "passes dry_run to generate_embeddings_for_single_candidate)."
    )
    parser.add_argument(
        "--ignore-lock",
        action="store_true",
        help="Ignore the lock file and run the script even if another instance might be running."
    )
    args = parser.parse_args()

    # --- Lock file mechanism ---
    lock_file = None  # Initialize lock_file to None
    try:
        lock_file = open(LOCK_FILE_PATH, 'w')
        fcntl.flock(lock_file, fcntl.LOCK_EX | fcntl.LOCK_NB)
    except BlockingIOError:
        if args.ignore_lock:
            print(f"Warning: Lock file {LOCK_FILE_PATH} exists, but --ignore-lock flag is set. Continuing execution.")
            # We don't hold the lock, so we shouldn't try to release or remove it in finally if it was never acquired.
            # The original instance that holds the lock will manage it.
            # We set lock_file to None to indicate it wasn't acquired by this instance.
            if lock_file: # Should be open if BlockingIOError happened on flock
                lock_file.close() # Close the file handle we opened
            lock_file = None 
        else:
            print(f"Another instance of the script is already running. Lock file: {LOCK_FILE_PATH}. Exiting.")
            if lock_file:
                lock_file.close()
            sys.exit(1)
    except Exception as e: # Catch other potential errors during lock acquisition
        if args.ignore_lock:
            print(f"Warning: Unable to Check the Status of the Lock file {LOCK_FILE_PATH}, but --ignore-lock flag is set. Continuing execution.")
            # We don't hold the lock, so we shouldn't try to release or remove it in finally if it was never acquired.
            # The original instance that holds the lock will manage it.
            # We set lock_file to None to indicate it wasn't acquired by this instance.
            if lock_file: # Should be open if BlockingIOError happened on flock
                lock_file.close() # Close the file handle we opened
            lock_file = None 
        else:
            print(f"Error during lock file operation: {e}. Exiting.")
            if lock_file:
                lock_file.close()
            sys.exit(1)
    # --- End lock file mechanism ---

    load_secrets_env_variables()

    # Ensure log directory exists
    log_dir = "/mnt/incoming/logs/embedding_generator/"
    os.makedirs(log_dir, exist_ok=True)
    log_ts = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file_path = os.path.join(log_dir, f"embedding_orchestrator_{log_ts}.log")

    app_logger = AppLogger({
        "level": "INFO",
        "log_to_stdout": True,
        "log_file": log_file_path,
        "log_mode": "backup"
    })

    # Critical: Ensure OpenAI API Key is set for the imported module
    if not os.getenv("OPENAI_API_KEY"):
        app_logger.error("OPENAI_API_KEY environment variable is not set. Exiting.")
        exit(1)
    # The openai_client in candidate_embedding should be initialized by its own module's global scope execution.

    selected_pg_env = PostgresEnvironment[args.env]

    try:
        run_orchestrator(
            logger=app_logger,
            pg_env=selected_pg_env,
            candidate_limit=args.limit,
            dry_run=args.dry_run
        )
    finally:
        # --- Release lock ---
        if lock_file: # Only release/remove if this instance acquired the lock
            try:
                fcntl.flock(lock_file, fcntl.LOCK_UN)
                lock_file.close()
                # Optionally remove the lock file, though releasing the lock is key
                os.remove(LOCK_FILE_PATH)
                app_logger.info("Script finished and lock released.")
            except Exception as e:
                app_logger.error(f"Error releasing lock: {e}")
        elif args.ignore_lock:
            app_logger.info("Script finished. Lock was ignored, not released by this instance.")
        else:
            # This case should ideally not be reached if exit happened, but as a safeguard:
            app_logger.info("Script finished. Lock was not acquired by this instance.")
        # --- End release lock ---