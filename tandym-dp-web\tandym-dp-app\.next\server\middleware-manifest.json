{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_bbcfe3._.js", "server/edge/chunks/[root of the server]__c55477._.js", "server/edge/chunks/edge-wrapper_aaa207.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(\\/?index|\\/?index\\\\.json))?[\\/#\\?]?$", "originalSource": "/"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/candidates(\\\\.json)?[\\/#\\?]?$", "originalSource": "/candidates"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/jobs(\\\\.json)?[\\/#\\?]?$", "originalSource": "/jobs"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/skills-editor(\\\\.json)?[\\/#\\?]?$", "originalSource": "/skills-editor"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/workforce-index(\\\\.json)?[\\/#\\?]?$", "originalSource": "/workforce-index"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/vacancy(\\\\.json)?[\\/#\\?]?$", "originalSource": "/vacancy"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/vacancy(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/vacancy/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/experiments(\\\\.json)?[\\/#\\?]?$", "originalSource": "/experiments"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/CandidateTuning\\/For_Mercury_Portal(\\\\.json)?[\\/#\\?]?$", "originalSource": "/CandidateTuning/For_Mercury_Portal"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "WHLvqhg+5uAd2CK00BvwlNoi+/rxpBSUqGf1awguCRA=", "__NEXT_PREVIEW_MODE_ID": "e609b18c1fa42a2903206fb6148a8915", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "931d7d49d084df57337d16d98be0b4c8e9daebbe1f2699cbe7ef0ebf525c13a0", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "281cef9f8a0577a44e4553bb2ba8bf0f8d07446a7a4266bd3e6192acbb364eba"}}}, "sortedMiddleware": ["/"], "functions": {}}