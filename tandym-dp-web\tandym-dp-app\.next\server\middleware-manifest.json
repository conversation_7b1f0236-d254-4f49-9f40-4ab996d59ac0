{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_bbcfe3._.js", "server/edge/chunks/[root of the server]__c55477._.js", "server/edge/chunks/edge-wrapper_aaa207.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(\\/?index|\\/?index\\\\.json))?[\\/#\\?]?$", "originalSource": "/"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/candidates(\\\\.json)?[\\/#\\?]?$", "originalSource": "/candidates"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/jobs(\\\\.json)?[\\/#\\?]?$", "originalSource": "/jobs"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/skills-editor(\\\\.json)?[\\/#\\?]?$", "originalSource": "/skills-editor"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/workforce-index(\\\\.json)?[\\/#\\?]?$", "originalSource": "/workforce-index"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/vacancy(\\\\.json)?[\\/#\\?]?$", "originalSource": "/vacancy"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/vacancy(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/vacancy/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/experiments(\\\\.json)?[\\/#\\?]?$", "originalSource": "/experiments"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/CandidateTuning\\/For_Mercury_Portal(\\\\.json)?[\\/#\\?]?$", "originalSource": "/CandidateTuning/For_Mercury_Portal"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "WHLvqhg+5uAd2CK00BvwlNoi+/rxpBSUqGf1awguCRA=", "__NEXT_PREVIEW_MODE_ID": "e9113e0349151d6eec75aa56bd692c27", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "8db8109edebed3ab2ae61ca4ccbbb4b0419bfc717cea2ce13bfe26aaa3d7e930", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "b4837e5ced8f1afa5b9540f0c537a7cd240629d6991b057cd8a81e3130f6d51f"}}}, "sortedMiddleware": ["/"], "functions": {}}