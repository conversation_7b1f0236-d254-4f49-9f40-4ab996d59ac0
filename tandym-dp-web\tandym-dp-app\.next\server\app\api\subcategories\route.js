const CHUNK_PUBLIC_PATH = "server/app/api/subcategories/route.js";
const runtime = require("../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_next_e4c66b._.js");
runtime.loadChunk("server/chunks/node_modules_axios_lib_afbb78._.js");
runtime.loadChunk("server/chunks/node_modules_mime-db_9ebaab._.js");
runtime.loadChunk("server/chunks/node_modules_next-auth_35e54c._.js");
runtime.loadChunk("server/chunks/node_modules_openid-client_ef38b3._.js");
runtime.loadChunk("server/chunks/node_modules_jose_dist_node_cjs_b4a801._.js");
runtime.loadChunk("server/chunks/node_modules_@microsoft_applicationinsights-core-js_dist-es5_4fbf1b._.js");
runtime.loadChunk("server/chunks/node_modules_@microsoft_applicationinsights-common_dist-es5_0d9150._.js");
runtime.loadChunk("server/chunks/node_modules_@microsoft_applicationinsights-analytics-js_dist-es5_ea98a7._.js");
runtime.loadChunk("server/chunks/node_modules_@microsoft_applicationinsights-dependencies-js_dist-es5_3e5946._.js");
runtime.loadChunk("server/chunks/node_modules_@microsoft_applicationinsights-channel-js_dist-es5_a6f2cc._.js");
runtime.loadChunk("server/chunks/node_modules_bac491._.js");
runtime.loadChunk("server/chunks/[root of the server]__3a1b2b._.js");
runtime.loadChunk("server/chunks/_4a4574._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/subcategories/route/actions.js [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/app/api/subcategories/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
