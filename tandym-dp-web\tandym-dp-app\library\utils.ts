import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const USER_UUID_KEY = "tg-user-uuid";

export function getOrCreateUserUuid(): string {
  if (typeof window === "undefined") return "";
  let id = localStorage.getItem(USER_UUID_KEY);
  if (!id) {
    id = crypto.randomUUID();
    localStorage.setItem(USER_UUID_KEY, id);
  }
  return id;
}

export function clearUserUuid() {
  localStorage.removeItem(USER_UUID_KEY); // call on logout if desired
}
export enum APPLICATION_NAVIGATION_ROUTES {
  VACANCY = "Vacancy",
  WORK_FORCE_INDEX = "Work_force_Index",
  SUB_CATEGORY = "Sub_Catregory",
  SEARCH_MATCH = "Search_Match",
  SC_SCORE_CONFIG = "Sc_Score_Config",
}
export const emailInternalAddress = "<EMAIL>";
